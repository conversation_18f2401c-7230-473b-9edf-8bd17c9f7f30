{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/chips\";\nfunction PromotionDashboardComponent_tr_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\")(16, \"mat-chip\", 35);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const employee_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(employee_r1.empNo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(employee_r1.empId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(employee_r1.nic);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(employee_r1.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(employee_r1.designation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(employee_r1.transferInfo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(employee_r1.additionalInfo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(employee_r1.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", employee_r1.status, \" \");\n  }\n}\nexport let PromotionDashboardComponent = /*#__PURE__*/(() => {\n  class PromotionDashboardComponent {\n    constructor() {\n      this.Math = Math;\n      this.stats = {\n        totalEmployees: 243,\n        eligibleForPromotion: 45,\n        pendingReviews: 12,\n        completedPromotions: 28\n      };\n      this.employeeData = [{\n        empNo: 'SUBHASHIKA',\n        empId: '2019/15',\n        nic: '9 ** *** 5648',\n        fullName: 'T.G.Subhashika',\n        designation: '1st Grade',\n        transferInfo: '75',\n        additionalInfo: 'K.Piyasena',\n        status: 'Eligible'\n      }, {\n        empNo: 'JOHN SMITH',\n        empId: '2020/23',\n        nic: '8 ** *** 1234',\n        fullName: 'John Smith',\n        designation: '2nd Grade',\n        transferInfo: '82',\n        additionalInfo: 'M.Fernando',\n        status: 'Pending'\n      }, {\n        empNo: 'SARAH JONES',\n        empId: '2019/45',\n        nic: '9 ** *** 7890',\n        fullName: 'Sarah Jones',\n        designation: '1st Grade',\n        transferInfo: '78',\n        additionalInfo: 'P.Silva',\n        status: 'Eligible'\n      }, {\n        empNo: 'MIKE DAVIS',\n        empId: '2021/12',\n        nic: '7 ** *** 5678',\n        fullName: 'Mike Davis',\n        designation: '3rd Grade',\n        transferInfo: '65',\n        additionalInfo: 'R.Perera',\n        status: 'Rejected'\n      }];\n    }\n    ngOnInit() {\n      // Initialize component\n    }\n    getStatusClass(status) {\n      switch (status.toLowerCase()) {\n        case 'approved':\n          return 'status-approved';\n        case 'pending':\n          return 'status-pending';\n        case 'completed':\n          return 'status-completed';\n        case 'eligible':\n          return 'status-eligible';\n        case 'rejected':\n          return 'status-rejected';\n        default:\n          return 'status-default';\n      }\n    }\n    static {\n      this.ɵfac = function PromotionDashboardComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PromotionDashboardComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PromotionDashboardComponent,\n        selectors: [[\"app-promotion-dashboard\"]],\n        decls: 135,\n        vars: 1,\n        consts: [[1, \"dashboard-container\"], [1, \"top-stats-row\"], [1, \"left-stats\"], [1, \"stat-card\"], [1, \"stat-header\"], [1, \"stat-label\"], [1, \"more-btn\"], [1, \"stat-number\"], [1, \"stats-row\"], [1, \"stat-card\", \"small\"], [1, \"center-chart\"], [1, \"chart-card\"], [1, \"chart-header\"], [1, \"chart-title\"], [1, \"chart-content\"], [1, \"donut-chart\"], [1, \"chart-number\"], [1, \"chart-label\"], [1, \"chart-legend\"], [1, \"legend-item\"], [1, \"legend-color\", \"blue\"], [1, \"legend-text\"], [1, \"legend-value\"], [1, \"legend-color\", \"green\"], [1, \"legend-color\", \"yellow\"], [1, \"legend-color\", \"orange\"], [1, \"right-chart\"], [1, \"export-btn\"], [1, \"legend-color\", \"gray\"], [1, \"table-section\"], [1, \"table-card\"], [1, \"table-header\"], [1, \"table-content\"], [1, \"data-table\"], [4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"]],\n        template: function PromotionDashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"span\", 5);\n            i0.ɵɵtext(6, \"Total Candidates\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"button\", 6);\n            i0.ɵɵtext(8, \"\\u2022\\u2022\\u2022\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 7);\n            i0.ɵɵtext(10, \"533\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 3)(12, \"div\", 4)(13, \"span\", 5);\n            i0.ɵɵtext(14, \"Net Eligible Candidates\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"button\", 6);\n            i0.ɵɵtext(16, \"\\u2022\\u2022\\u2022\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 7);\n            i0.ɵɵtext(18, \"100\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 8)(20, \"div\", 9)(21, \"div\", 4)(22, \"span\", 5);\n            i0.ɵɵtext(23, \"Written Request\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"button\", 6);\n            i0.ɵɵtext(25, \"\\u2022\\u2022\\u2022\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 7);\n            i0.ɵɵtext(27, \"10\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 9)(29, \"div\", 4)(30, \"span\", 5);\n            i0.ɵɵtext(31, \"Another Request\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"button\", 6);\n            i0.ɵɵtext(33, \"\\u2022\\u2022\\u2022\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(34, \"div\", 7);\n            i0.ɵɵtext(35, \"05\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"div\", 3)(37, \"div\", 4)(38, \"span\", 5);\n            i0.ɵɵtext(39, \"Manual Transfer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"button\", 6);\n            i0.ɵɵtext(41, \"\\u2022\\u2022\\u2022\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(42, \"div\", 7);\n            i0.ɵɵtext(43, \"05\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(44, \"div\", 10)(45, \"div\", 11)(46, \"div\", 12)(47, \"span\", 13);\n            i0.ɵɵtext(48, \"Eligible by Department\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"div\", 14)(50, \"div\", 15)(51, \"div\", 16);\n            i0.ɵɵtext(52, \"400\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"div\", 17);\n            i0.ɵɵtext(54, \"Eligible Candidates\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(55, \"div\", 18)(56, \"div\", 19);\n            i0.ɵɵelement(57, \"span\", 20);\n            i0.ɵɵelementStart(58, \"span\", 21);\n            i0.ɵɵtext(59, \"Secretariat (01) - Clerk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"span\", 22);\n            i0.ɵɵtext(61, \"100\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(62, \"div\", 19);\n            i0.ɵɵelement(63, \"span\", 23);\n            i0.ɵɵelementStart(64, \"span\", 21);\n            i0.ɵɵtext(65, \"Secretariat (02) - Clerk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"span\", 22);\n            i0.ɵɵtext(67, \"100\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(68, \"div\", 19);\n            i0.ɵɵelement(69, \"span\", 24);\n            i0.ɵɵelementStart(70, \"span\", 21);\n            i0.ɵɵtext(71, \"Secretariat (03) - Clerk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"span\", 22);\n            i0.ɵɵtext(73, \"100\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(74, \"div\", 19);\n            i0.ɵɵelement(75, \"span\", 25);\n            i0.ɵɵelementStart(76, \"span\", 21);\n            i0.ɵɵtext(77, \"Secretariat (04) - Clerk\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"span\", 22);\n            i0.ɵɵtext(79, \"100\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(80, \"div\", 26)(81, \"div\", 11)(82, \"div\", 12)(83, \"span\", 13);\n            i0.ɵɵtext(84, \"Transfer by own request\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(85, \"button\", 27);\n            i0.ɵɵtext(86, \"Export\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(87, \"div\", 14)(88, \"div\", 15)(89, \"div\", 16);\n            i0.ɵɵtext(90, \"20\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"div\", 17);\n            i0.ɵɵtext(92, \"Employees\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(93, \"div\", 18)(94, \"div\", 19);\n            i0.ɵɵelement(95, \"span\", 20);\n            i0.ɵɵelementStart(96, \"span\", 21);\n            i0.ɵɵtext(97, \"WP is region(10)\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(98, \"div\", 19);\n            i0.ɵɵelement(99, \"span\", 24);\n            i0.ɵɵelementStart(100, \"span\", 21);\n            i0.ɵɵtext(101, \"Another Region(05)\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(102, \"div\", 19);\n            i0.ɵɵelement(103, \"span\", 28);\n            i0.ɵɵelementStart(104, \"span\", 21);\n            i0.ɵɵtext(105, \"Manual Transfer(05)\");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(106, \"div\", 29)(107, \"div\", 30)(108, \"div\", 31)(109, \"h3\");\n            i0.ɵɵtext(110, \"Application Details\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(111, \"button\", 27);\n            i0.ɵɵtext(112, \"Export\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(113, \"div\", 32)(114, \"table\", 33)(115, \"thead\")(116, \"tr\")(117, \"th\");\n            i0.ɵɵtext(118, \"Employee No.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(119, \"th\");\n            i0.ɵɵtext(120, \"EMP ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(121, \"th\");\n            i0.ɵɵtext(122, \"NIC\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(123, \"th\");\n            i0.ɵɵtext(124, \"Full Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(125, \"th\");\n            i0.ɵɵtext(126, \"Designation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(127, \"th\");\n            i0.ɵɵtext(128, \"Transfer of Information\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(129, \"th\");\n            i0.ɵɵtext(130, \"Additional Information\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(131, \"th\");\n            i0.ɵɵtext(132, \"Status\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(133, \"tbody\");\n            i0.ɵɵtemplate(134, PromotionDashboardComponent_tr_134_Template, 18, 9, \"tr\", 34);\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(134);\n            i0.ɵɵproperty(\"ngForOf\", ctx.employeeData);\n          }\n        },\n        dependencies: [CommonModule, i1.NgClass, i1.NgForOf, MatCardModule, MatButtonModule, MatChipsModule, i2.MatChip, RouterModule],\n        styles: [\".dashboard-container[_ngcontent-%COMP%]{padding:20px;background-color:#fff;min-height:100vh;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif}.top-stats-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 2fr 1fr;gap:20px;margin-bottom:30px}.left-stats[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px}.stat-card[_ngcontent-%COMP%]{background:#fff;border-radius:8px;padding:15px;box-shadow:0 2px 4px #0000001a;border:1px solid #e0e0e0}.stat-card.small[_ngcontent-%COMP%]{flex:1}.stats-row[_ngcontent-%COMP%]{display:flex;gap:15px}.stat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px}.stat-label[_ngcontent-%COMP%]{font-size:12px;color:#666;font-weight:500}.more-btn[_ngcontent-%COMP%]{background:none;border:none;color:#999;cursor:pointer;font-size:16px;padding:0}.stat-number[_ngcontent-%COMP%]{font-size:32px;font-weight:700;color:#333}.center-chart[_ngcontent-%COMP%], .right-chart[_ngcontent-%COMP%]{background:#fff;border-radius:8px;box-shadow:0 2px 4px #0000001a;border:1px solid #e0e0e0}.chart-card[_ngcontent-%COMP%]{padding:20px;height:100%}.chart-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.chart-title[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#333}.export-btn[_ngcontent-%COMP%]{background:#f5f5f5;color:#333;border:1px solid #ddd;padding:6px 12px;border-radius:4px;font-size:12px;cursor:pointer}.chart-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:30px}.donut-chart[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;width:120px;height:120px;border-radius:50%;background:conic-gradient(#4285f4 0deg 90deg,#34a853 90deg 180deg,#fbbc04 180deg 270deg,#ea4335 270deg 360deg);position:relative}.donut-chart[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;width:80px;height:80px;background:#fff;border-radius:50%}.chart-number[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#333;z-index:1}.chart-label[_ngcontent-%COMP%]{font-size:12px;color:#666;z-index:1}.chart-legend[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.legend-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:12px}.legend-color[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:2px}.legend-color.blue[_ngcontent-%COMP%]{background-color:#4285f4}.legend-color.green[_ngcontent-%COMP%]{background-color:#34a853}.legend-color.yellow[_ngcontent-%COMP%]{background-color:#fbbc04}.legend-color.orange[_ngcontent-%COMP%]{background-color:#ea4335}.legend-color.gray[_ngcontent-%COMP%]{background-color:#9e9e9e}.legend-text[_ngcontent-%COMP%]{flex:1;color:#666}.legend-value[_ngcontent-%COMP%]{color:#333;font-weight:500}.table-section[_ngcontent-%COMP%]{margin-top:30px}.table-card[_ngcontent-%COMP%]{background:#fff;border-radius:8px;box-shadow:0 2px 4px #0000001a;border:1px solid #e0e0e0;overflow:hidden}.table-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:20px;background:#333;color:#fff}.table-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:16px;font-weight:600}.table-content[_ngcontent-%COMP%]{overflow-x:auto}.data-table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse}.data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:#333;color:#fff;padding:12px 15px;text-align:left;font-size:12px;font-weight:600;border-right:1px solid #555}.data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:last-child{border-right:none}.data-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:12px 15px;border-bottom:1px solid #e0e0e0;border-right:1px solid #e0e0e0;font-size:12px;color:#333}.data-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:last-child{border-right:none}.data-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.mat-mdc-chip[_ngcontent-%COMP%]{border-radius:4px!important;font-size:11px!important;font-weight:500!important;padding:4px 8px!important;height:auto!important;min-height:20px!important}.status-eligible[_ngcontent-%COMP%]{background-color:#e8f5e8!important;color:#2e7d32!important}.status-pending[_ngcontent-%COMP%]{background-color:#fff3e0!important;color:#f57c00!important}.status-rejected[_ngcontent-%COMP%]{background-color:#ffebee!important;color:#d32f2f!important}.status-approved[_ngcontent-%COMP%]{background-color:#e3f2fd!important;color:#1976d2!important}.status-completed[_ngcontent-%COMP%]{background-color:#f3e5f5!important;color:#7b1fa2!important}.status-default[_ngcontent-%COMP%]{background-color:#f5f5f5!important;color:#666!important}@media (max-width: 1200px){.top-stats-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:20px}.chart-content[_ngcontent-%COMP%]{flex-direction:column;gap:20px}}@media (max-width: 768px){.dashboard-container[_ngcontent-%COMP%]{padding:15px}.stats-row[_ngcontent-%COMP%]{flex-direction:column}.table-content[_ngcontent-%COMP%]{font-size:11px}.data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .data-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:8px 10px}}\"]\n      });\n    }\n  }\n  return PromotionDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}