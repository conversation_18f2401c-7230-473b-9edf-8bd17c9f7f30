{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, booleanAttribute, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, inject, ChangeDetectorRef, ElementRef, ANIMATION_MODULE_TYPE, signal, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport { FocusMonitor, AriaDescriber } from '@angular/cdk/a11y';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { ReplaySubject, Subject, merge } from 'rxjs';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/bidi';\n\n/** @docs-private */\nconst _c0 = [\"mat-sort-header\", \"\"];\nconst _c1 = [\"*\"];\nfunction MatSortHeader_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 3);\n    i0.ɵɵelement(2, \"path\", 4);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction getSortDuplicateSortableIdError(id) {\n  return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n  return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n  return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n  return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nlet MatSort = /*#__PURE__*/(() => {\n  class MatSort {\n    _defaultOptions;\n    _initializedStream = new ReplaySubject(1);\n    /** Collection of all registered sortables that this directive manages. */\n    sortables = new Map();\n    /** Used to notify any child components listening to state changes. */\n    _stateChanges = new Subject();\n    /** The id of the most recently sorted MatSortable. */\n    active;\n    /**\n     * The direction to set when an MatSortable is initially sorted.\n     * May be overridden by the MatSortable's sort start.\n     */\n    start = 'asc';\n    /** The sort direction of the currently active MatSortable. */\n    get direction() {\n      return this._direction;\n    }\n    set direction(direction) {\n      if (direction && direction !== 'asc' && direction !== 'desc' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getSortInvalidDirectionError(direction);\n      }\n      this._direction = direction;\n    }\n    _direction = '';\n    /**\n     * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n     * May be overridden by the MatSortable's disable clear input.\n     */\n    disableClear;\n    /** Whether the sortable is disabled. */\n    disabled = false;\n    /** Event emitted when the user changes either the active sort or sort direction. */\n    sortChange = new EventEmitter();\n    /** Emits when the paginator is initialized. */\n    initialized = this._initializedStream;\n    constructor(_defaultOptions) {\n      this._defaultOptions = _defaultOptions;\n    }\n    /**\n     * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n     * collection of MatSortables.\n     */\n    register(sortable) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (!sortable.id) {\n          throw getSortHeaderMissingIdError();\n        }\n        if (this.sortables.has(sortable.id)) {\n          throw getSortDuplicateSortableIdError(sortable.id);\n        }\n      }\n      this.sortables.set(sortable.id, sortable);\n    }\n    /**\n     * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n     * collection of contained MatSortables.\n     */\n    deregister(sortable) {\n      this.sortables.delete(sortable.id);\n    }\n    /** Sets the active sort id and determines the new sort direction. */\n    sort(sortable) {\n      if (this.active != sortable.id) {\n        this.active = sortable.id;\n        this.direction = sortable.start ? sortable.start : this.start;\n      } else {\n        this.direction = this.getNextSortDirection(sortable);\n      }\n      this.sortChange.emit({\n        active: this.active,\n        direction: this.direction\n      });\n    }\n    /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n    getNextSortDirection(sortable) {\n      if (!sortable) {\n        return '';\n      }\n      // Get the sort direction cycle with the potential sortable overrides.\n      const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n      let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n      // Get and return the next direction in the cycle\n      let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n      if (nextDirectionIndex >= sortDirectionCycle.length) {\n        nextDirectionIndex = 0;\n      }\n      return sortDirectionCycle[nextDirectionIndex];\n    }\n    ngOnInit() {\n      this._initializedStream.next();\n    }\n    ngOnChanges() {\n      this._stateChanges.next();\n    }\n    ngOnDestroy() {\n      this._stateChanges.complete();\n      this._initializedStream.complete();\n    }\n    static ɵfac = function MatSort_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSort)(i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSort,\n      selectors: [[\"\", \"matSort\", \"\"]],\n      hostAttrs: [1, \"mat-sort\"],\n      inputs: {\n        active: [0, \"matSortActive\", \"active\"],\n        start: [0, \"matSortStart\", \"start\"],\n        direction: [0, \"matSortDirection\", \"direction\"],\n        disableClear: [2, \"matSortDisableClear\", \"disableClear\", booleanAttribute],\n        disabled: [2, \"matSortDisabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        sortChange: \"matSortChange\"\n      },\n      exportAs: [\"matSort\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return MatSort;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n  let sortOrder = ['asc', 'desc'];\n  if (start == 'desc') {\n    sortOrder.reverse();\n  }\n  if (!disableClear) {\n    sortOrder.push('');\n  }\n  return sortOrder;\n}\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nlet MatSortHeaderIntl = /*#__PURE__*/(() => {\n  class MatSortHeaderIntl {\n    /**\n     * Stream that emits whenever the labels here are changed. Use this to notify\n     * components if the labels have changed after initialization.\n     */\n    changes = new Subject();\n    static ɵfac = function MatSortHeaderIntl_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSortHeaderIntl)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatSortHeaderIntl,\n      factory: MatSortHeaderIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MatSortHeaderIntl;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatSortHeaderIntl();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n  // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n  provide: MatSortHeaderIntl,\n  deps: [[/*#__PURE__*/new Optional(), /*#__PURE__*/new SkipSelf(), MatSortHeaderIntl]],\n  useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY\n};\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nlet MatSortHeader = /*#__PURE__*/(() => {\n  class MatSortHeader {\n    _intl = inject(MatSortHeaderIntl);\n    _sort = inject(MatSort, {\n      optional: true\n    });\n    _columnDef = inject('MAT_SORT_HEADER_COLUMN_DEF', {\n      optional: true\n    });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _focusMonitor = inject(FocusMonitor);\n    _elementRef = inject(ElementRef);\n    _ariaDescriber = inject(AriaDescriber, {\n      optional: true\n    });\n    _renderChanges;\n    _animationModule = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    /**\n     * Indicates which state was just cleared from the sort header.\n     * Will be reset on the next interaction. Used for coordinating animations.\n     */\n    _recentlyCleared = signal(null);\n    /**\n     * The element with role=\"button\" inside this component's view. We need this\n     * in order to apply a description with AriaDescriber.\n     */\n    _sortButton;\n    /**\n     * ID of this sort header. If used within the context of a CdkColumnDef, this will default to\n     * the column's name.\n     */\n    id;\n    /** Sets the position of the arrow that displays when sorted. */\n    arrowPosition = 'after';\n    /** Overrides the sort start value of the containing MatSort for this MatSortable. */\n    start;\n    /** whether the sort header is disabled. */\n    disabled = false;\n    /**\n     * Description applied to MatSortHeader's button element with aria-describedby. This text should\n     * describe the action that will occur when the user clicks the sort header.\n     */\n    get sortActionDescription() {\n      return this._sortActionDescription;\n    }\n    set sortActionDescription(value) {\n      this._updateSortActionDescription(value);\n    }\n    // Default the action description to \"Sort\" because it's better than nothing.\n    // Without a description, the button's label comes from the sort header text content,\n    // which doesn't give any indication that it performs a sorting operation.\n    _sortActionDescription = 'Sort';\n    /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n    disableClear;\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      const defaultOptions = inject(MAT_SORT_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      // Note that we use a string token for the `_columnDef`, because the value is provided both by\n      // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n      // and we want to avoid having the sort header depending on the CDK table because\n      // of this single reference.\n      if (!this._sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getSortHeaderNotContainedWithinSortError();\n      }\n      if (defaultOptions?.arrowPosition) {\n        this.arrowPosition = defaultOptions?.arrowPosition;\n      }\n    }\n    ngOnInit() {\n      if (!this.id && this._columnDef) {\n        this.id = this._columnDef.name;\n      }\n      this._sort.register(this);\n      this._renderChanges = merge(this._sort._stateChanges, this._sort.sortChange).subscribe(() => this._changeDetectorRef.markForCheck());\n      this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n      this._updateSortActionDescription(this._sortActionDescription);\n    }\n    ngAfterViewInit() {\n      // We use the focus monitor because we also want to style\n      // things differently based on the focus origin.\n      this._focusMonitor.monitor(this._elementRef, true).subscribe(() => this._recentlyCleared.set(null));\n    }\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      this._sort.deregister(this);\n      this._renderChanges?.unsubscribe();\n      if (this._sortButton) {\n        this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n      }\n    }\n    /** Triggers the sort on this sort header and removes the indicator hint. */\n    _toggleOnInteraction() {\n      if (!this._isDisabled()) {\n        const wasSorted = this._isSorted();\n        const prevDirection = this._sort.direction;\n        this._sort.sort(this);\n        this._recentlyCleared.set(wasSorted && !this._isSorted() ? prevDirection : null);\n      }\n    }\n    _handleKeydown(event) {\n      if (event.keyCode === SPACE || event.keyCode === ENTER) {\n        event.preventDefault();\n        this._toggleOnInteraction();\n      }\n    }\n    /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n    _isSorted() {\n      return this._sort.active == this.id && (this._sort.direction === 'asc' || this._sort.direction === 'desc');\n    }\n    _isDisabled() {\n      return this._sort.disabled || this.disabled;\n    }\n    /**\n     * Gets the aria-sort attribute that should be applied to this sort header. If this header\n     * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n     * says that the aria-sort property should only be present on one header at a time, so removing\n     * ensures this is true.\n     */\n    _getAriaSortAttribute() {\n      if (!this._isSorted()) {\n        return 'none';\n      }\n      return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n    }\n    /** Whether the arrow inside the sort header should be rendered. */\n    _renderArrow() {\n      return !this._isDisabled() || this._isSorted();\n    }\n    _updateSortActionDescription(newDescription) {\n      // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n      // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n      // for every *cell* in the table, creating a lot of unnecessary noise.\n      // If _sortButton is undefined, the component hasn't been initialized yet so there's\n      // nothing to update in the DOM.\n      if (this._sortButton) {\n        // removeDescription will no-op if there is no existing message.\n        // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n        this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n        this._ariaDescriber?.describe(this._sortButton, newDescription);\n      }\n      this._sortActionDescription = newDescription;\n    }\n    static ɵfac = function MatSortHeader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSortHeader)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSortHeader,\n      selectors: [[\"\", \"mat-sort-header\", \"\"]],\n      hostAttrs: [1, \"mat-sort-header\"],\n      hostVars: 3,\n      hostBindings: function MatSortHeader_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatSortHeader_click_HostBindingHandler() {\n            return ctx._toggleOnInteraction();\n          })(\"keydown\", function MatSortHeader_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"mouseleave\", function MatSortHeader_mouseleave_HostBindingHandler() {\n            return ctx._recentlyCleared.set(null);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-sort\", ctx._getAriaSortAttribute());\n          i0.ɵɵclassProp(\"mat-sort-header-disabled\", ctx._isDisabled());\n        }\n      },\n      inputs: {\n        id: [0, \"mat-sort-header\", \"id\"],\n        arrowPosition: \"arrowPosition\",\n        start: \"start\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        sortActionDescription: \"sortActionDescription\",\n        disableClear: [2, \"disableClear\", \"disableClear\", booleanAttribute]\n      },\n      exportAs: [\"matSortHeader\"],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 17,\n      consts: [[1, \"mat-sort-header-container\", \"mat-focus-indicator\"], [1, \"mat-sort-header-content\"], [1, \"mat-sort-header-arrow\"], [\"viewBox\", \"0 -960 960 960\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z\"]],\n      template: function MatSortHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, MatSortHeader_Conditional_3_Template, 3, 0, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-sort-header-sorted\", ctx._isSorted())(\"mat-sort-header-position-before\", ctx.arrowPosition === \"before\")(\"mat-sort-header-descending\", ctx._sort.direction === \"desc\")(\"mat-sort-header-ascending\", ctx._sort.direction === \"asc\")(\"mat-sort-header-recently-cleared-ascending\", ctx._recentlyCleared() === \"asc\")(\"mat-sort-header-recently-cleared-descending\", ctx._recentlyCleared() === \"desc\")(\"mat-sort-header-animations-disabled\", ctx._animationModule === \"NoopAnimations\");\n          i0.ɵɵattribute(\"tabindex\", ctx._isDisabled() ? null : 0)(\"role\", ctx._isDisabled() ? null : \"button\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx._renderArrow() ? 3 : -1);\n        }\n      },\n      styles: [\".mat-sort-header{cursor:pointer}.mat-sort-header-disabled{cursor:default}.mat-sort-header-container{display:flex;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatSortHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSortModule = /*#__PURE__*/(() => {\n  class MatSortModule {\n    static ɵfac = function MatSortModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSortModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSortModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n      imports: [MatCommonModule]\n    });\n  }\n  return MatSortModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Animations used by MatSort.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matSortAnimations = {\n  // Represents:\n  // trigger('indicator', [\n  //   state('active-asc, asc', style({transform: 'translateY(0px)'})),\n  //   // 10px is the height of the sort indicator, minus the width of the pointers\n  //   state('active-desc, desc', style({transform: 'translateY(10px)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that moves the sort indicator. */\n  indicator: {\n    type: 7,\n    name: 'indicator',\n    definitions: [{\n      type: 0,\n      name: 'active-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(0px)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'active-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(10px)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'active-asc <=> active-desc',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('leftPointer', [\n  //   state('active-asc, asc', style({transform: 'rotate(-45deg)'})),\n  //   state('active-desc, desc', style({transform: 'rotate(45deg)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n  leftPointer: {\n    type: 7,\n    name: 'leftPointer',\n    definitions: [{\n      type: 0,\n      name: 'active-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(-45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'active-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'active-asc <=> active-desc',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('rightPointer', [\n  //   state('active-asc, asc', style({transform: 'rotate(45deg)'})),\n  //   state('active-desc, desc', style({transform: 'rotate(-45deg)'})),\n  //   transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n  rightPointer: {\n    type: 7,\n    name: 'rightPointer',\n    definitions: [{\n      type: 0,\n      name: 'active-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'active-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(-45deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'active-asc <=> active-desc',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('arrowOpacity', [\n  //   state('desc-to-active, asc-to-active, active', style({opacity: 1})),\n  //   state('desc-to-hint, asc-to-hint, hint', style({opacity: 0.54})),\n  //   state(\n  //     'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n  //     style({opacity: 0}),\n  //   ),\n  //   // Transition between all states except for immediate transitions\n  //   transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')),\n  //   transition('* <=> *', animate(SORT_ANIMATION_TRANSITION)),\n  // ])\n  /** Animation that controls the arrow opacity. */\n  arrowOpacity: {\n    type: 7,\n    name: 'arrowOpacity',\n    definitions: [{\n      type: 0,\n      name: 'desc-to-active, asc-to-active, active',\n      styles: {\n        type: 6,\n        styles: {\n          'opacity': 1\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'desc-to-hint, asc-to-hint, hint',\n      styles: {\n        type: 6,\n        styles: {\n          'opacity': 0.54\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n      styles: {\n        type: 6,\n        styles: {\n          'opacity': 0\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => asc, * => desc, * => active, * => hint, * => void',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '0ms'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* <=> *',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('arrowPosition', [\n  //   // Hidden Above => Hint Center\n  //   transition(\n  //     '* => desc-to-hint, * => desc-to-active',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(-25%)'}), style({transform: 'translateY(0)'})]),\n  //     ),\n  //   ),\n  //   // Hint Center => Hidden Below\n  //   transition(\n  //     '* => hint-to-desc, * => active-to-desc',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(25%)'})]),\n  //     ),\n  //   ),\n  //   // Hidden Below => Hint Center\n  //   transition(\n  //     '* => asc-to-hint, * => asc-to-active',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(25%)'}), style({transform: 'translateY(0)'})]),\n  //     ),\n  //   ),\n  //   // Hint Center => Hidden Above\n  //   transition(\n  //     '* => hint-to-asc, * => active-to-asc',\n  //     animate(\n  //       SORT_ANIMATION_TRANSITION,\n  //       keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(-25%)'})]),\n  //     ),\n  //   ),\n  //   state(\n  //     'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n  //     style({transform: 'translateY(0)'}),\n  //   ),\n  //   state('hint-to-desc, active-to-desc, desc', style({transform: 'translateY(-25%)'})),\n  //   state('hint-to-asc, active-to-asc, asc', style({transform: 'translateY(25%)'})),\n  // ])\n  /**\n   * Animation for the translation of the arrow as a whole. States are separated into two\n   * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n   * peek, and active. The other states define a specific animation (source-to-destination)\n   * and are determined as a function of their prev user-perceived state and what the next state\n   * should be.\n   */\n  arrowPosition: {\n    type: 7,\n    name: 'arrowPosition',\n    definitions: [{\n      type: 1,\n      expr: '* => desc-to-hint, * => desc-to-active',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(-25%)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => hint-to-desc, * => active-to-desc',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(25%)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => asc-to-hint, * => asc-to-active',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(25%)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => hint-to-asc, * => active-to-asc',\n      animation: {\n        type: 4,\n        styles: {\n          type: 5,\n          'steps': [{\n            type: 6,\n            styles: {\n              transform: 'translateY(0)'\n            },\n            offset: null\n          }, {\n            type: 6,\n            styles: {\n              transform: 'translateY(-25%)'\n            },\n            offset: null\n          }]\n        },\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }, {\n      type: 0,\n      name: 'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(0)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'hint-to-desc, active-to-desc, desc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(-25%)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'hint-to-asc, active-to-asc, asc',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'translateY(25%)'\n        },\n        offset: null\n      }\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('allowChildren', [\n  //   transition('* <=> *', [query('@*', animateChild(), {optional: true})]),\n  // ])\n  /** Necessary trigger that calls animate on children animations. */\n  allowChildren: {\n    type: 7,\n    name: 'allowChildren',\n    definitions: [{\n      type: 1,\n      expr: '* <=> *',\n      animation: [{\n        type: 11,\n        selector: '@*',\n        animation: {\n          type: 9,\n          options: null\n        },\n        options: {\n          optional: true\n        }\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };\n//# sourceMappingURL=sort.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}