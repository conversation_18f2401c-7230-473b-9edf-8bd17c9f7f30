{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/datepicker\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/table\";\nimport * as i11 from \"@angular/material/tabs\";\nfunction PanelApprovalComponent_mat_option_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const candidate_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", candidate_r1.employeeId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", candidate_r1.employeeName, \" (\", candidate_r1.employeeId, \") - \", candidate_r1.currentDesignation, \" \");\n  }\n}\nfunction PanelApprovalComponent_form_67_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function PanelApprovalComponent_form_67_div_74_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.removeFile());\n    });\n    i0.ɵɵtext(4, \" \\u2715 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.uploadedFile.name);\n  }\n}\nfunction PanelApprovalComponent_form_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 39);\n    i0.ɵɵlistener(\"ngSubmit\", function PanelApprovalComponent_form_67_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmitApproval());\n    });\n    i0.ɵɵelementStart(1, \"div\", 40)(2, \"h3\");\n    i0.ɵɵtext(3, \"Candidate Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"div\", 42)(6, \"div\", 43);\n    i0.ɵɵtext(7, \"Employee ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 44);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 42)(11, \"div\", 43);\n    i0.ɵɵtext(12, \"Employee Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 44);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 42)(16, \"div\", 43);\n    i0.ɵɵtext(17, \"Current Designation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 44);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 42)(21, \"div\", 43);\n    i0.ɵɵtext(22, \"Proposed Designation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 44);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 42)(26, \"div\", 43);\n    i0.ɵɵtext(27, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 44);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 42)(31, \"div\", 43);\n    i0.ɵɵtext(32, \"Suitability Score\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 44)(34, \"mat-chip\", 11);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(36, \"div\", 45)(37, \"h3\");\n    i0.ɵɵtext(38, \"Panel Decision\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 46)(40, \"mat-form-field\", 17)(41, \"mat-label\");\n    i0.ɵɵtext(42, \"Panel Recommendation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"mat-select\", 47)(44, \"mat-option\", 48);\n    i0.ɵɵtext(45, \"Recommended\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"mat-option\", 49);\n    i0.ɵɵtext(47, \"Not Recommended\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"mat-form-field\", 17)(49, \"mat-label\");\n    i0.ɵɵtext(50, \"Effective Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"input\", 50)(52, \"mat-datepicker-toggle\", 51)(53, \"mat-datepicker\", null, 0);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"mat-form-field\", 52)(56, \"mat-label\");\n    i0.ɵɵtext(57, \"Remarks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(58, \"textarea\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"mat-form-field\", 52)(60, \"mat-label\");\n    i0.ɵɵtext(61, \"Order Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(62, \"input\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 55)(64, \"h3\");\n    i0.ɵɵtext(65, \"Upload Panel Document\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function PanelApprovalComponent_form_67_Template_div_click_66_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const fileInput_r4 = i0.ɵɵreference(68);\n      return i0.ɵɵresetView(fileInput_r4.click());\n    });\n    i0.ɵɵelementStart(67, \"input\", 57, 1);\n    i0.ɵɵlistener(\"change\", function PanelApprovalComponent_form_67_Template_input_change_67_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 58)(70, \"p\");\n    i0.ɵɵtext(71, \"\\uD83D\\uDCC4 Upload Panel Decision Document\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"small\");\n    i0.ɵɵtext(73, \"PDF, DOC, DOCX files only\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(74, PanelApprovalComponent_form_67_div_74_Template, 5, 1, \"div\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"div\", 60)(76, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function PanelApprovalComponent_form_67_Template_button_click_76_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onReset());\n    });\n    i0.ɵɵtext(77, \"Reset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"button\", 62);\n    i0.ɵɵtext(79, \" Submit Panel Decision \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const effectivePicker_r6 = i0.ɵɵreference(54);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.approvalForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedCandidate.employeeId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedCandidate.employeeName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedCandidate.currentDesignation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedCandidate.proposedDesignation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedCandidate.department);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getScoreClass(ctx_r2.selectedCandidate.suitabilityScore));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedCandidate.suitabilityScore, \"/100 \");\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"matDatepicker\", effectivePicker_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", effectivePicker_r6);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.uploadedFile);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.approvalForm.invalid);\n  }\n}\nfunction PanelApprovalComponent_th_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 65);\n    i0.ɵɵtext(1, \"Employee\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PanelApprovalComponent_td_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 66)(1, \"div\", 67)(2, \"div\", 68);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const candidate_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(candidate_r7.employeeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(candidate_r7.employeeId);\n  }\n}\nfunction PanelApprovalComponent_th_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 65);\n    i0.ɵɵtext(1, \"Designation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PanelApprovalComponent_td_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 66)(1, \"div\", 70)(2, \"div\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 72);\n    i0.ɵɵtext(5, \"\\u2192\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 73);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const candidate_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(candidate_r8.currentDesignation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(candidate_r8.proposedDesignation);\n  }\n}\nfunction PanelApprovalComponent_th_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 65);\n    i0.ɵɵtext(1, \"Department\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PanelApprovalComponent_td_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const candidate_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(candidate_r9.department);\n  }\n}\nfunction PanelApprovalComponent_th_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 65);\n    i0.ɵɵtext(1, \"Score\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PanelApprovalComponent_td_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 66)(1, \"mat-chip\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const candidate_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getScoreClass(candidate_r10.suitabilityScore));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", candidate_r10.suitabilityScore, \"/100 \");\n  }\n}\nfunction PanelApprovalComponent_th_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 65);\n    i0.ɵɵtext(1, \"Recommendation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PanelApprovalComponent_td_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 66)(1, \"mat-chip\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const candidate_r11 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getRecommendationClass(candidate_r11.panelRecommendation));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", candidate_r11.panelRecommendation, \" \");\n  }\n}\nfunction PanelApprovalComponent_th_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 65);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PanelApprovalComponent_td_96_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 66)(1, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function PanelApprovalComponent_td_96_Template_button_click_1_listener() {\n      const candidate_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.editCandidate(candidate_r13));\n    });\n    i0.ɵɵtext(2, \" Edit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function PanelApprovalComponent_td_96_Template_button_click_3_listener() {\n      const candidate_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewDetails(candidate_r13));\n    });\n    i0.ɵɵtext(4, \" View Details \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PanelApprovalComponent_tr_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 76);\n  }\n}\nfunction PanelApprovalComponent_tr_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 77);\n  }\n}\nexport let PanelApprovalComponent = /*#__PURE__*/(() => {\n  class PanelApprovalComponent {\n    constructor(fb) {\n      this.fb = fb;\n      this.selectedCandidateId = '';\n      this.selectedCandidate = null;\n      this.uploadedFile = null;\n      this.bulkColumns = ['employee', 'designation', 'department', 'score', 'recommendation', 'actions'];\n      this.currentPanel = {\n        panelId: 'PNL-2024-001',\n        panelType: 'Promotion Panel',\n        chairperson: 'Dr. Rajesh Kumar',\n        meetingDate: new Date('2024-03-15'),\n        status: 'In Progress',\n        venue: 'Conference Room A'\n      };\n      this.panelCandidates = [{\n        employeeId: 'EMP001',\n        employeeName: 'Amit Kumar',\n        currentDesignation: 'Assistant',\n        proposedDesignation: 'Superintendent',\n        department: 'Operations',\n        suitabilityScore: 85,\n        panelRecommendation: 'Pending',\n        remarks: ''\n      }, {\n        employeeId: 'EMP002',\n        employeeName: 'Priya Sharma',\n        currentDesignation: 'Junior Assistant',\n        proposedDesignation: 'Assistant',\n        department: 'Finance',\n        suitabilityScore: 92,\n        panelRecommendation: 'Pending',\n        remarks: ''\n      }];\n      this.approvalForm = this.fb.group({\n        panelRecommendation: ['', Validators.required],\n        effectiveDate: ['', Validators.required],\n        remarks: ['', Validators.required],\n        orderNumber: ['', Validators.required]\n      });\n    }\n    ngOnInit() {}\n    onCandidateSelect(event) {\n      const candidateId = event.value;\n      this.selectedCandidate = this.panelCandidates.find(c => c.employeeId === candidateId) || null;\n    }\n    onFileSelected(event) {\n      const file = event.target.files[0];\n      if (file) {\n        this.uploadedFile = file;\n      }\n    }\n    removeFile() {\n      this.uploadedFile = null;\n    }\n    getPanelStatusClass(status) {\n      switch (status) {\n        case 'Scheduled':\n          return 'status-scheduled';\n        case 'In Progress':\n          return 'status-in-progress';\n        case 'Completed':\n          return 'status-completed';\n        default:\n          return 'status-scheduled';\n      }\n    }\n    getScoreClass(score) {\n      if (score >= 90) return 'score-excellent';\n      if (score >= 75) return 'score-good';\n      if (score >= 60) return 'score-average';\n      return 'score-poor';\n    }\n    getRecommendationClass(recommendation) {\n      switch (recommendation) {\n        case 'Recommended':\n          return 'recommendation-recommended';\n        case 'Not Recommended':\n          return 'recommendation-not-recommended';\n        case 'Pending':\n          return 'recommendation-pending';\n        default:\n          return 'recommendation-pending';\n      }\n    }\n    viewPanelDetails() {\n      console.log('View panel details');\n    }\n    onSubmitApproval() {\n      if (this.approvalForm.valid && this.selectedCandidate) {\n        const approvalData = {\n          ...this.approvalForm.value,\n          employeeId: this.selectedCandidate.employeeId,\n          uploadedDocument: this.uploadedFile\n        };\n        console.log('Panel approval submitted:', approvalData);\n      }\n    }\n    onReset() {\n      this.approvalForm.reset();\n      this.selectedCandidate = null;\n      this.selectedCandidateId = '';\n      this.uploadedFile = null;\n    }\n    editCandidate(candidate) {\n      console.log('Edit candidate:', candidate);\n    }\n    viewDetails(candidate) {\n      console.log('View candidate details:', candidate);\n    }\n    approveAll() {\n      console.log('Approve all recommended candidates');\n    }\n    exportResults() {\n      console.log('Export panel results');\n    }\n    static {\n      this.ɵfac = function PanelApprovalComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PanelApprovalComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PanelApprovalComponent,\n        selectors: [[\"app-panel-approval\"]],\n        decls: 104,\n        vars: 16,\n        consts: [[\"effectivePicker\", \"\"], [\"fileInput\", \"\"], [1, \"panel-approval-container\"], [1, \"header-section\"], [1, \"page-header\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"panel-info-card\"], [1, \"panel-details-grid\"], [1, \"detail-item\"], [1, \"detail-label\"], [1, \"detail-value\"], [3, \"ngClass\"], [1, \"approval-tabs\"], [\"label\", \"Individual Approval\"], [1, \"tab-content\"], [1, \"approval-form-card\"], [1, \"candidate-selection\"], [\"appearance\", \"outline\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"label\", \"Bulk Approval\"], [1, \"bulk-approval-card\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"candidates-table\", 3, \"dataSource\"], [\"matColumnDef\", \"employee\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"designation\"], [\"matColumnDef\", \"department\"], [\"matColumnDef\", \"score\"], [\"matColumnDef\", \"recommendation\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [1, \"bulk-actions\"], [\"mat-raised-button\", \"\", 3, \"click\"], [3, \"value\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"candidate-info-section\"], [1, \"info-grid\"], [1, \"info-item\"], [1, \"info-label\"], [1, \"info-value\"], [1, \"approval-section\"], [1, \"form-row\"], [\"formControlName\", \"panelRecommendation\"], [\"value\", \"Recommended\"], [\"value\", \"Not Recommended\"], [\"matInput\", \"\", \"formControlName\", \"effectiveDate\", 3, \"matDatepicker\"], [\"matIconSuffix\", \"\", 3, \"for\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"remarks\", \"rows\", \"4\", \"placeholder\", \"Enter panel remarks and justification\"], [\"matInput\", \"\", \"formControlName\", \"orderNumber\", \"placeholder\", \"Enter official order number\"], [1, \"upload-section\"], [1, \"file-upload-area\", 3, \"click\"], [\"type\", \"file\", \"accept\", \".pdf,.doc,.docx\", 2, \"display\", \"none\", 3, \"change\"], [1, \"upload-content\"], [\"class\", \"uploaded-file\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [1, \"uploaded-file\"], [\"mat-icon-button\", \"\", 3, \"click\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"employee-info\"], [1, \"employee-name\"], [1, \"employee-id\"], [1, \"designation-info\"], [1, \"current\"], [1, \"arrow\"], [1, \"proposed\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n        template: function PanelApprovalComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"h1\");\n            i0.ɵɵtext(4, \"Panel Approval Screen\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Final promotion approval by competent authority\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function PanelApprovalComponent_Template_button_click_7_listener() {\n              return ctx.viewPanelDetails();\n            });\n            i0.ɵɵtext(8, \" View Panel Details \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"mat-card\", 6)(10, \"mat-card-header\")(11, \"mat-card-title\");\n            i0.ɵɵtext(12, \"Panel Information\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-card-subtitle\");\n            i0.ɵɵtext(14, \"Current panel session details\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"mat-card-content\")(16, \"div\", 7)(17, \"div\", 8)(18, \"div\", 9);\n            i0.ɵɵtext(19, \"Panel ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 10);\n            i0.ɵɵtext(21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 8)(23, \"div\", 9);\n            i0.ɵɵtext(24, \"Panel Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 10);\n            i0.ɵɵtext(26);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(27, \"div\", 8)(28, \"div\", 9);\n            i0.ɵɵtext(29, \"Chairperson\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"div\", 10);\n            i0.ɵɵtext(31);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 8)(33, \"div\", 9);\n            i0.ɵɵtext(34, \"Meeting Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 10);\n            i0.ɵɵtext(36);\n            i0.ɵɵpipe(37, \"date\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"div\", 8)(39, \"div\", 9);\n            i0.ɵɵtext(40, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"div\", 10)(42, \"mat-chip\", 11);\n            i0.ɵɵtext(43);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(44, \"div\", 8)(45, \"div\", 9);\n            i0.ɵɵtext(46, \"Venue\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"div\", 10);\n            i0.ɵɵtext(48);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(49, \"mat-tab-group\", 12)(50, \"mat-tab\", 13)(51, \"div\", 14)(52, \"mat-card\", 15)(53, \"mat-card-header\")(54, \"mat-card-title\");\n            i0.ɵɵtext(55, \"Individual Candidate Approval\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"mat-card-subtitle\");\n            i0.ɵɵtext(57, \"Review and approve individual promotion cases\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(58, \"mat-card-content\")(59, \"div\", 16)(60, \"mat-form-field\", 17)(61, \"mat-label\");\n            i0.ɵɵtext(62, \"Select Candidate\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"mat-select\", 18);\n            i0.ɵɵtwoWayListener(\"valueChange\", function PanelApprovalComponent_Template_mat_select_valueChange_63_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedCandidateId, $event) || (ctx.selectedCandidateId = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectionChange\", function PanelApprovalComponent_Template_mat_select_selectionChange_63_listener($event) {\n              return ctx.onCandidateSelect($event);\n            });\n            i0.ɵɵelementStart(64, \"mat-option\", 19);\n            i0.ɵɵtext(65, \"Select a candidate\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(66, PanelApprovalComponent_mat_option_66_Template, 2, 4, \"mat-option\", 20);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(67, PanelApprovalComponent_form_67_Template, 80, 12, \"form\", 21);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(68, \"mat-tab\", 22)(69, \"div\", 14)(70, \"mat-card\", 23)(71, \"mat-card-header\")(72, \"mat-card-title\");\n            i0.ɵɵtext(73, \"Bulk Approval\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"mat-card-subtitle\");\n            i0.ɵɵtext(75, \"Review and approve multiple candidates\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(76, \"mat-card-content\")(77, \"div\", 24)(78, \"table\", 25);\n            i0.ɵɵelementContainerStart(79, 26);\n            i0.ɵɵtemplate(80, PanelApprovalComponent_th_80_Template, 2, 0, \"th\", 27)(81, PanelApprovalComponent_td_81_Template, 6, 2, \"td\", 28);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(82, 29);\n            i0.ɵɵtemplate(83, PanelApprovalComponent_th_83_Template, 2, 0, \"th\", 27)(84, PanelApprovalComponent_td_84_Template, 8, 2, \"td\", 28);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(85, 30);\n            i0.ɵɵtemplate(86, PanelApprovalComponent_th_86_Template, 2, 0, \"th\", 27)(87, PanelApprovalComponent_td_87_Template, 2, 1, \"td\", 28);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(88, 31);\n            i0.ɵɵtemplate(89, PanelApprovalComponent_th_89_Template, 2, 0, \"th\", 27)(90, PanelApprovalComponent_td_90_Template, 3, 2, \"td\", 28);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(91, 32);\n            i0.ɵɵtemplate(92, PanelApprovalComponent_th_92_Template, 2, 0, \"th\", 27)(93, PanelApprovalComponent_td_93_Template, 3, 2, \"td\", 28);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(94, 33);\n            i0.ɵɵtemplate(95, PanelApprovalComponent_th_95_Template, 2, 0, \"th\", 27)(96, PanelApprovalComponent_td_96_Template, 5, 0, \"td\", 28);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵtemplate(97, PanelApprovalComponent_tr_97_Template, 1, 0, \"tr\", 34)(98, PanelApprovalComponent_tr_98_Template, 1, 0, \"tr\", 35);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(99, \"div\", 36)(100, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function PanelApprovalComponent_Template_button_click_100_listener() {\n              return ctx.approveAll();\n            });\n            i0.ɵɵtext(101, \" Approve All Recommended \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(102, \"button\", 37);\n            i0.ɵɵlistener(\"click\", function PanelApprovalComponent_Template_button_click_102_listener() {\n              return ctx.exportResults();\n            });\n            i0.ɵɵtext(103, \" Export Results \");\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(21);\n            i0.ɵɵtextInterpolate(ctx.currentPanel.panelId);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.currentPanel.panelType);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.currentPanel.chairperson);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(37, 13, ctx.currentPanel.meetingDate, \"dd/MM/yyyy\"));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngClass\", ctx.getPanelStatusClass(ctx.currentPanel.status));\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.currentPanel.status, \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.currentPanel.venue);\n            i0.ɵɵadvance(15);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedCandidateId);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.panelCandidates);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedCandidate);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"dataSource\", ctx.panelCandidates);\n            i0.ɵɵadvance(19);\n            i0.ɵɵproperty(\"matHeaderRowDef\", ctx.bulkColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRowDefColumns\", ctx.bulkColumns);\n          }\n        },\n        dependencies: [CommonModule, i2.NgClass, i2.NgForOf, i2.NgIf, i2.DatePipe, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatButtonModule, i4.MatButton, i4.MatIconButton, MatFormFieldModule, i5.MatFormField, i5.MatLabel, i5.MatSuffix, MatInputModule, i6.MatInput, MatSelectModule, i7.MatSelect, i7.MatOption, MatDatepickerModule, i8.MatDatepicker, i8.MatDatepickerInput, i8.MatDatepickerToggle, MatNativeDateModule, MatChipsModule, i9.MatChip, MatIconModule, MatTableModule, i10.MatTable, i10.MatHeaderCellDef, i10.MatHeaderRowDef, i10.MatColumnDef, i10.MatCellDef, i10.MatRowDef, i10.MatHeaderCell, i10.MatCell, i10.MatHeaderRow, i10.MatRow, MatTabsModule, i11.MatTab, i11.MatTabGroup, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName, RouterModule],\n        styles: [\".panel-approval-container[_ngcontent-%COMP%]{padding:20px;background-color:#fff;min-height:100vh}.header-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:30px}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:28px;font-weight:600;color:#333}.page-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0 0;color:#666;font-size:16px}.panel-info-card[_ngcontent-%COMP%], .approval-form-card[_ngcontent-%COMP%], .bulk-approval-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 20px #0000001a;margin-bottom:25px}.panel-details-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px}.detail-item[_ngcontent-%COMP%], .info-item[_ngcontent-%COMP%]{padding:15px;border:1px solid #e0e0e0;border-radius:8px}.detail-label[_ngcontent-%COMP%], .info-label[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-bottom:5px;text-transform:uppercase;letter-spacing:.5px}.detail-value[_ngcontent-%COMP%], .info-value[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:#333}.approval-tabs[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 20px #0000001a}.tab-content[_ngcontent-%COMP%]{padding:20px}.candidate-selection[_ngcontent-%COMP%]{margin-bottom:30px}.candidate-info-section[_ngcontent-%COMP%], .approval-section[_ngcontent-%COMP%], .upload-section[_ngcontent-%COMP%]{margin-bottom:30px;padding-bottom:20px;border-bottom:1px solid #e0e0e0}.candidate-info-section[_ngcontent-%COMP%]:last-child, .approval-section[_ngcontent-%COMP%]:last-child, .upload-section[_ngcontent-%COMP%]:last-child{border-bottom:none}.candidate-info-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .approval-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .upload-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 20px;font-size:18px;font-weight:500;color:#333}.info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:15px}.form-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:20px;margin-bottom:20px}.full-width[_ngcontent-%COMP%]{width:100%}.file-upload-area[_ngcontent-%COMP%]{border:2px dashed #e0e0e0;border-radius:8px;padding:30px;text-align:center;cursor:pointer;transition:all .3s ease;background-color:#fafafa}.file-upload-area[_ngcontent-%COMP%]:hover{border-color:#ccc;background-color:#f5f5f5}.upload-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 5px;font-size:16px;color:#666}.upload-content[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:#999}.uploaded-file[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:10px 15px;background-color:#f5f5f5;border-radius:4px;margin-top:15px;font-size:14px}.form-actions[_ngcontent-%COMP%], .bulk-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:15px;margin-top:30px;padding-top:20px;border-top:1px solid #e0e0e0}.table-container[_ngcontent-%COMP%]{overflow-x:auto}.candidates-table[_ngcontent-%COMP%]{width:100%}.employee-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.employee-name[_ngcontent-%COMP%]{font-weight:500;color:#333}.employee-id[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-top:2px}.designation-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:12px}.current[_ngcontent-%COMP%]{color:#666}.arrow[_ngcontent-%COMP%]{color:#999;font-weight:700}.proposed[_ngcontent-%COMP%]{color:#333;font-weight:500}.score-excellent[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.score-good[_ngcontent-%COMP%]{background-color:#bee3f8;color:#2a4365}.score-average[_ngcontent-%COMP%]{background-color:#feebc8;color:#7b341e}.score-poor[_ngcontent-%COMP%]{background-color:#fed7d7;color:#742a2a}.recommendation-recommended[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.recommendation-not-recommended[_ngcontent-%COMP%]{background-color:#fed7d7;color:#742a2a}.recommendation-pending[_ngcontent-%COMP%]{background-color:#feebc8;color:#7b341e}.status-scheduled[_ngcontent-%COMP%]{background-color:#bee3f8;color:#2a4365}.status-in-progress[_ngcontent-%COMP%]{background-color:#feebc8;color:#7b341e}.status-completed[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}@media (max-width: 768px){.header-section[_ngcontent-%COMP%]{flex-direction:column;gap:20px}.form-row[_ngcontent-%COMP%], .panel-details-grid[_ngcontent-%COMP%], .info-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.designation-info[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:4px}}\"]\n      });\n    }\n  }\n  return PanelApprovalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}