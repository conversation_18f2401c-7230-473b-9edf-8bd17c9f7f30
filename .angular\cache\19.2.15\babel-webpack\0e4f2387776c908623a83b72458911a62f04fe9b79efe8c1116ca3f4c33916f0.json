{"ast": null, "code": "import { a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _DisposeViewRepeaterStrategy {\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n      if (record.previousIndex == null) {\n        const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n        view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n        operation = _ViewRepeaterOperation.INSERTED;\n      } else if (currentIndex == null) {\n        viewContainerRef.remove(adjustedPreviousIndex);\n        operation = _ViewRepeaterOperation.REMOVED;\n      } else {\n        view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        operation = _ViewRepeaterOperation.MOVED;\n      }\n      if (itemViewChanged) {\n        itemViewChanged({\n          context: view?.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n  detach() {}\n}\nexport { _DisposeViewRepeaterStrategy as _ };\n//# sourceMappingURL=dispose-view-repeater-strategy-D_JReLI1.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}