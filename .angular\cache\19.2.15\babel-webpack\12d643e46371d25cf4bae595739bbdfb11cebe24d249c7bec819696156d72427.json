{"ast": null, "code": "export { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load the .cdk-visually-hidden styles.\n * @docs-private\n */\nlet _VisuallyHiddenLoader = /*#__PURE__*/(() => {\n  class _VisuallyHiddenLoader {\n    static ɵfac = function _VisuallyHiddenLoader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _VisuallyHiddenLoader)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _VisuallyHiddenLoader,\n      selectors: [[\"ng-component\"]],\n      exportAs: [\"cdkVisuallyHidden\"],\n      decls: 0,\n      vars: 0,\n      template: function _VisuallyHiddenLoader_Template(rf, ctx) {},\n      styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return _VisuallyHiddenLoader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { _VisuallyHiddenLoader };\n//# sourceMappingURL=private.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}