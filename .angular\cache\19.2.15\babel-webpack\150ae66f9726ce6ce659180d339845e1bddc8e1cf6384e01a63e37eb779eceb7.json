{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Injectable, afterNextRender, NgZone, Injector, ElementRef, booleanAttribute, Directive, Input, InjectionToken, NgModule } from '@angular/core';\nimport { C as CdkMonitorFocus } from './focus-monitor-e2l_RpN3.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nimport { B as BreakpointObserver } from './breakpoints-observer-CljOfYGy.mjs';\nimport { ContentObserver, ObserversModule } from './observers.mjs';\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  /**\n   * Whether to count an element as focusable even if it is not currently visible.\n   */\n  ignoreVisibility = false;\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nlet InteractivityChecker = /*#__PURE__*/(() => {\n  class InteractivityChecker {\n    _platform = inject(Platform);\n    constructor() {}\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n    isDisabled(element) {\n      // This does not capture some cases, such as a non-form control with a disabled attribute or\n      // a form control inside of a disabled form, but should capture the most common cases.\n      return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n    isVisible(element) {\n      return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n    isTabbable(element) {\n      // Nothing is tabbable on the server 😎\n      if (!this._platform.isBrowser) {\n        return false;\n      }\n      const frameElement = getFrameElement(getWindow(element));\n      if (frameElement) {\n        // Frame elements inherit their tabindex onto all child elements.\n        if (getTabIndexValue(frameElement) === -1) {\n          return false;\n        }\n        // Browsers disable tabbing to an element inside of an invisible frame.\n        if (!this.isVisible(frameElement)) {\n          return false;\n        }\n      }\n      let nodeName = element.nodeName.toLowerCase();\n      let tabIndexValue = getTabIndexValue(element);\n      if (element.hasAttribute('contenteditable')) {\n        return tabIndexValue !== -1;\n      }\n      if (nodeName === 'iframe' || nodeName === 'object') {\n        // The frame or object's content may be tabbable depending on the content, but it's\n        // not possibly to reliably detect the content of the frames. We always consider such\n        // elements as non-tabbable.\n        return false;\n      }\n      // In iOS, the browser only considers some specific elements as tabbable.\n      if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n        return false;\n      }\n      if (nodeName === 'audio') {\n        // Audio elements without controls enabled are never tabbable, regardless\n        // of the tabindex attribute explicitly being set.\n        if (!element.hasAttribute('controls')) {\n          return false;\n        }\n        // Audio elements with controls are by default tabbable unless the\n        // tabindex attribute is set to `-1` explicitly.\n        return tabIndexValue !== -1;\n      }\n      if (nodeName === 'video') {\n        // For all video elements, if the tabindex attribute is set to `-1`, the video\n        // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n        // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n        // tabindex attribute is the source of truth here.\n        if (tabIndexValue === -1) {\n          return false;\n        }\n        // If the tabindex is explicitly set, and not `-1` (as per check before), the\n        // video element is always tabbable (regardless of whether it has controls or not).\n        if (tabIndexValue !== null) {\n          return true;\n        }\n        // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n        // has controls enabled. Firefox is special as videos are always tabbable regardless\n        // of whether there are controls or not.\n        return this._platform.FIREFOX || element.hasAttribute('controls');\n      }\n      return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n    isFocusable(element, config) {\n      // Perform checks in order of left to most expensive.\n      // Again, naive approach that does not capture many edge cases and browser quirks.\n      return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n    }\n    static ɵfac = function InteractivityChecker_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InteractivityChecker)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InteractivityChecker,\n      factory: InteractivityChecker.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return InteractivityChecker;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n  _element;\n  _checker;\n  _ngZone;\n  _document;\n  _injector;\n  _startAnchor;\n  _endAnchor;\n  _hasAttached = false;\n  // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n  startAnchorListener = () => this.focusLastTabbableElement();\n  endAnchorListener = () => this.focusFirstTabbableElement();\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  _enabled = true;\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false, /** @breaking-change 20.0.0 param to become required */\n  _injector) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._injector = _injector;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {\n        injector: this._injector\n      });\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nlet FocusTrapFactory = /*#__PURE__*/(() => {\n  class FocusTrapFactory {\n    _checker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _document = inject(DOCUMENT);\n    _injector = inject(Injector);\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n    create(element, deferCaptureElements = false) {\n      return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n    }\n    static ɵfac = function FocusTrapFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusTrapFactory)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusTrapFactory,\n      factory: FocusTrapFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return FocusTrapFactory;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Directive for trapping focus within a region. */\nlet CdkTrapFocus = /*#__PURE__*/(() => {\n  class CdkTrapFocus {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    /** Underlying FocusTrap instance. */\n    focusTrap;\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n    _previouslyFocusedElement = null;\n    /** Whether the focus trap is active. */\n    get enabled() {\n      return this.focusTrap?.enabled || false;\n    }\n    set enabled(value) {\n      if (this.focusTrap) {\n        this.focusTrap.enabled = value;\n      }\n    }\n    /**\n     * Whether the directive should automatically move focus into the trapped region upon\n     * initialization and return focus to the previous activeElement upon destruction.\n     */\n    autoCapture;\n    constructor() {\n      const platform = inject(Platform);\n      if (platform.isBrowser) {\n        this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n      }\n    }\n    ngOnDestroy() {\n      this.focusTrap?.destroy();\n      // If we stored a previously focused element when using autoCapture, return focus to that\n      // element now that the trapped region is being destroyed.\n      if (this._previouslyFocusedElement) {\n        this._previouslyFocusedElement.focus();\n        this._previouslyFocusedElement = null;\n      }\n    }\n    ngAfterContentInit() {\n      this.focusTrap?.attachAnchors();\n      if (this.autoCapture) {\n        this._captureFocus();\n      }\n    }\n    ngDoCheck() {\n      if (this.focusTrap && !this.focusTrap.hasAttached()) {\n        this.focusTrap.attachAnchors();\n      }\n    }\n    ngOnChanges(changes) {\n      const autoCaptureChange = changes['autoCapture'];\n      if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap?.hasAttached()) {\n        this._captureFocus();\n      }\n    }\n    _captureFocus() {\n      this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n      this.focusTrap?.focusInitialElementWhenReady();\n    }\n    static ɵfac = function CdkTrapFocus_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkTrapFocus)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTrapFocus,\n      selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n      inputs: {\n        enabled: [2, \"cdkTrapFocus\", \"enabled\", booleanAttribute],\n        autoCapture: [2, \"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute]\n      },\n      exportAs: [\"cdkTrapFocus\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return CdkTrapFocus;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = /*#__PURE__*/new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nlet LiveAnnouncer = /*#__PURE__*/(() => {\n  class LiveAnnouncer {\n    _ngZone = inject(NgZone);\n    _defaultOptions = inject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    _liveElement;\n    _document = inject(DOCUMENT);\n    _previousTimeout;\n    _currentPromise;\n    _currentResolve;\n    constructor() {\n      const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, {\n        optional: true\n      });\n      this._liveElement = elementToken || this._createLiveElement();\n    }\n    announce(message, ...args) {\n      const defaultOptions = this._defaultOptions;\n      let politeness;\n      let duration;\n      if (args.length === 1 && typeof args[0] === 'number') {\n        duration = args[0];\n      } else {\n        [politeness, duration] = args;\n      }\n      this.clear();\n      clearTimeout(this._previousTimeout);\n      if (!politeness) {\n        politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n      }\n      if (duration == null && defaultOptions) {\n        duration = defaultOptions.duration;\n      }\n      // TODO: ensure changing the politeness works on all environments we support.\n      this._liveElement.setAttribute('aria-live', politeness);\n      if (this._liveElement.id) {\n        this._exposeAnnouncerToModals(this._liveElement.id);\n      }\n      // This 100ms timeout is necessary for some browser + screen-reader combinations:\n      // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n      // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n      //   second time without clearing and then using a non-zero delay.\n      // (using JAWS 17 at time of this writing).\n      return this._ngZone.runOutsideAngular(() => {\n        if (!this._currentPromise) {\n          this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n        }\n        clearTimeout(this._previousTimeout);\n        this._previousTimeout = setTimeout(() => {\n          this._liveElement.textContent = message;\n          if (typeof duration === 'number') {\n            this._previousTimeout = setTimeout(() => this.clear(), duration);\n          }\n          // For some reason in tests this can be undefined\n          // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n          this._currentResolve?.();\n          this._currentPromise = this._currentResolve = undefined;\n        }, 100);\n        return this._currentPromise;\n      });\n    }\n    /**\n     * Clears the current text from the announcer element. Can be used to prevent\n     * screen readers from reading the text out again while the user is going\n     * through the page landmarks.\n     */\n    clear() {\n      if (this._liveElement) {\n        this._liveElement.textContent = '';\n      }\n    }\n    ngOnDestroy() {\n      clearTimeout(this._previousTimeout);\n      this._liveElement?.remove();\n      this._liveElement = null;\n      this._currentResolve?.();\n      this._currentPromise = this._currentResolve = undefined;\n    }\n    _createLiveElement() {\n      const elementClass = 'cdk-live-announcer-element';\n      const previousElements = this._document.getElementsByClassName(elementClass);\n      const liveEl = this._document.createElement('div');\n      // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n      for (let i = 0; i < previousElements.length; i++) {\n        previousElements[i].remove();\n      }\n      liveEl.classList.add(elementClass);\n      liveEl.classList.add('cdk-visually-hidden');\n      liveEl.setAttribute('aria-atomic', 'true');\n      liveEl.setAttribute('aria-live', 'polite');\n      liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n      this._document.body.appendChild(liveEl);\n      return liveEl;\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live announcer element if there is an\n     * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live announcer element.\n     */\n    _exposeAnnouncerToModals(id) {\n      // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n      // the `SnakBarContainer` and other usages.\n      //\n      // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n      // section of the DOM we need to look through. This should cover all the cases we support, but\n      // the selector can be expanded if it turns out to be too narrow.\n      const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n      for (let i = 0; i < modals.length; i++) {\n        const modal = modals[i];\n        const ariaOwns = modal.getAttribute('aria-owns');\n        if (!ariaOwns) {\n          modal.setAttribute('aria-owns', id);\n        } else if (ariaOwns.indexOf(id) === -1) {\n          modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n        }\n      }\n    }\n    static ɵfac = function LiveAnnouncer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LiveAnnouncer)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LiveAnnouncer,\n      factory: LiveAnnouncer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return LiveAnnouncer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nlet CdkAriaLive = /*#__PURE__*/(() => {\n  class CdkAriaLive {\n    _elementRef = inject(ElementRef);\n    _liveAnnouncer = inject(LiveAnnouncer);\n    _contentObserver = inject(ContentObserver);\n    _ngZone = inject(NgZone);\n    /** The aria-live politeness level to use when announcing messages. */\n    get politeness() {\n      return this._politeness;\n    }\n    set politeness(value) {\n      this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n      if (this._politeness === 'off') {\n        if (this._subscription) {\n          this._subscription.unsubscribe();\n          this._subscription = null;\n        }\n      } else if (!this._subscription) {\n        this._subscription = this._ngZone.runOutsideAngular(() => {\n          return this._contentObserver.observe(this._elementRef).subscribe(() => {\n            // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n            const elementText = this._elementRef.nativeElement.textContent;\n            // The `MutationObserver` fires also for attribute\n            // changes which we don't want to announce.\n            if (elementText !== this._previousAnnouncedText) {\n              this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n              this._previousAnnouncedText = elementText;\n            }\n          });\n        });\n      }\n    }\n    _politeness = 'polite';\n    /** Time in milliseconds after which to clear out the announcer element. */\n    duration;\n    _previousAnnouncedText;\n    _subscription;\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    }\n    ngOnDestroy() {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function CdkAriaLive_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAriaLive)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkAriaLive,\n      selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n      inputs: {\n        politeness: [0, \"cdkAriaLive\", \"politeness\"],\n        duration: [0, \"cdkAriaLiveDuration\", \"duration\"]\n      },\n      exportAs: [\"cdkAriaLive\"]\n    });\n  }\n  return CdkAriaLive;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode = /*#__PURE__*/function (HighContrastMode) {\n  HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n  HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n  HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n  return HighContrastMode;\n}(HighContrastMode || {});\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nlet HighContrastModeDetector = /*#__PURE__*/(() => {\n  class HighContrastModeDetector {\n    _platform = inject(Platform);\n    /**\n     * Figuring out the high contrast mode and adding the body classes can cause\n     * some expensive layouts. This flag is used to ensure that we only do it once.\n     */\n    _hasCheckedHighContrastMode;\n    _document = inject(DOCUMENT);\n    _breakpointSubscription;\n    constructor() {\n      this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n        if (this._hasCheckedHighContrastMode) {\n          this._hasCheckedHighContrastMode = false;\n          this._applyBodyHighContrastModeCssClasses();\n        }\n      });\n    }\n    /** Gets the current high-contrast-mode for the page. */\n    getHighContrastMode() {\n      if (!this._platform.isBrowser) {\n        return HighContrastMode.NONE;\n      }\n      // Create a test element with an arbitrary background-color that is neither black nor\n      // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n      // appending the test element to the DOM does not affect layout by absolutely positioning it\n      const testElement = this._document.createElement('div');\n      testElement.style.backgroundColor = 'rgb(1,2,3)';\n      testElement.style.position = 'absolute';\n      this._document.body.appendChild(testElement);\n      // Get the computed style for the background color, collapsing spaces to normalize between\n      // browsers. Once we get this color, we no longer need the test element. Access the `window`\n      // via the document so we can fake it in tests. Note that we have extra null checks, because\n      // this logic will likely run during app bootstrap and throwing can break the entire app.\n      const documentWindow = this._document.defaultView || window;\n      const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n      const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n      testElement.remove();\n      switch (computedColor) {\n        // Pre Windows 11 dark theme.\n        case 'rgb(0,0,0)':\n        // Windows 11 dark themes.\n        case 'rgb(45,50,54)':\n        case 'rgb(32,32,32)':\n          return HighContrastMode.WHITE_ON_BLACK;\n        // Pre Windows 11 light theme.\n        case 'rgb(255,255,255)':\n        // Windows 11 light theme.\n        case 'rgb(255,250,239)':\n          return HighContrastMode.BLACK_ON_WHITE;\n      }\n      return HighContrastMode.NONE;\n    }\n    ngOnDestroy() {\n      this._breakpointSubscription.unsubscribe();\n    }\n    /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n    _applyBodyHighContrastModeCssClasses() {\n      if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n        const bodyClasses = this._document.body.classList;\n        bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n        this._hasCheckedHighContrastMode = true;\n        const mode = this.getHighContrastMode();\n        if (mode === HighContrastMode.BLACK_ON_WHITE) {\n          bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n        } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n          bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n        }\n      }\n    }\n    static ɵfac = function HighContrastModeDetector_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HighContrastModeDetector)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HighContrastModeDetector,\n      factory: HighContrastModeDetector.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return HighContrastModeDetector;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet A11yModule = /*#__PURE__*/(() => {\n  class A11yModule {\n    constructor() {\n      inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n    }\n    static ɵfac = function A11yModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || A11yModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: A11yModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [ObserversModule]\n    });\n  }\n  return A11yModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { A11yModule as A, CdkTrapFocus as C, FocusTrapFactory as F, HighContrastModeDetector as H, InteractivityChecker as I, LiveAnnouncer as L, FocusTrap as a, HighContrastMode as b, IsFocusableConfig as c, CdkAriaLive as d, LIVE_ANNOUNCER_ELEMENT_TOKEN as e, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY as f, LIVE_ANNOUNCER_DEFAULT_OPTIONS as g };\n//# sourceMappingURL=a11y-module-BYox5gpI.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}