{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, ElementRef, Directive, TemplateRef, InjectionToken, EventEmitter, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChild, ContentChildren, ViewChild, Input, Output, ChangeDetectorRef, QueryList, numberAttribute, NgModule } from '@angular/core';\nimport { ControlContainer } from '@angular/forms';\nimport { Subject, of } from 'rxjs';\nimport { startWith, takeUntil } from 'rxjs/operators';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { F as FocusKeyManager } from './focus-key-manager-C1rAQJ5z.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { S as SPACE, c as ENTER } from './keycodes-CpHkExLC.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { BidiModule } from './bidi.mjs';\nimport '@angular/common';\nimport './list-key-manager-CyOIXo8P.mjs';\nimport './typeahead-9ZW4Dtsf.mjs';\nconst _c0 = [\"*\"];\nfunction CdkStep_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nlet CdkStepHeader = /*#__PURE__*/(() => {\n  class CdkStepHeader {\n    _elementRef = inject(ElementRef);\n    constructor() {}\n    /** Focuses the step header. */\n    focus() {\n      this._elementRef.nativeElement.focus();\n    }\n    static ɵfac = function CdkStepHeader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepHeader)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepHeader,\n      selectors: [[\"\", \"cdkStepHeader\", \"\"]],\n      hostAttrs: [\"role\", \"tab\"]\n    });\n  }\n  return CdkStepHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkStepLabel = /*#__PURE__*/(() => {\n  class CdkStepLabel {\n    template = inject(TemplateRef);\n    constructor() {}\n    static ɵfac = function CdkStepLabel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepLabel)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepLabel,\n      selectors: [[\"\", \"cdkStepLabel\", \"\"]]\n    });\n  }\n  return CdkStepLabel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Change event emitted on selection changes. */\nclass StepperSelectionEvent {\n  /** Index of the step now selected. */\n  selectedIndex;\n  /** Index of the step previously selected. */\n  previouslySelectedIndex;\n  /** The step instance now selected. */\n  selectedStep;\n  /** The step instance previously selected. */\n  previouslySelectedStep;\n}\n/** Enum to represent the different states of the steps. */\nconst STEP_STATE = {\n  NUMBER: 'number',\n  EDIT: 'edit',\n  DONE: 'done',\n  ERROR: 'error'\n};\n/** InjectionToken that can be used to specify the global stepper options. */\nconst STEPPER_GLOBAL_OPTIONS = /*#__PURE__*/new InjectionToken('STEPPER_GLOBAL_OPTIONS');\nlet CdkStep = /*#__PURE__*/(() => {\n  class CdkStep {\n    _stepperOptions;\n    _stepper = inject(CdkStepper);\n    _displayDefaultIndicatorType;\n    /** Template for step label if it exists. */\n    stepLabel;\n    /** Forms that have been projected into the step. */\n    _childForms;\n    /** Template for step content. */\n    content;\n    /** The top level abstract control of the step. */\n    stepControl;\n    /** Whether user has attempted to move away from the step. */\n    interacted = false;\n    /** Emits when the user has attempted to move away from the step. */\n    interactedStream = new EventEmitter();\n    /** Plain text label of the step. */\n    label;\n    /** Error message to display when there's an error. */\n    errorMessage;\n    /** Aria label for the tab. */\n    ariaLabel;\n    /**\n     * Reference to the element that the tab is labelled by.\n     * Will be cleared if `aria-label` is set at the same time.\n     */\n    ariaLabelledby;\n    /** State of the step. */\n    state;\n    /** Whether the user can return to this step once it has been marked as completed. */\n    editable = true;\n    /** Whether the completion of step is optional. */\n    optional = false;\n    /** Whether step is marked as completed. */\n    get completed() {\n      return this._completedOverride == null ? this._getDefaultCompleted() : this._completedOverride;\n    }\n    set completed(value) {\n      this._completedOverride = value;\n    }\n    _completedOverride = null;\n    _getDefaultCompleted() {\n      return this.stepControl ? this.stepControl.valid && this.interacted : this.interacted;\n    }\n    /** Whether step has an error. */\n    get hasError() {\n      return this._customError == null ? this._getDefaultError() : this._customError;\n    }\n    set hasError(value) {\n      this._customError = value;\n    }\n    _customError = null;\n    _getDefaultError() {\n      return this.stepControl && this.stepControl.invalid && this.interacted;\n    }\n    constructor() {\n      const stepperOptions = inject(STEPPER_GLOBAL_OPTIONS, {\n        optional: true\n      });\n      this._stepperOptions = stepperOptions ? stepperOptions : {};\n      this._displayDefaultIndicatorType = this._stepperOptions.displayDefaultIndicatorType !== false;\n    }\n    /** Selects this step component. */\n    select() {\n      this._stepper.selected = this;\n    }\n    /** Resets the step to its initial state. Note that this includes resetting form data. */\n    reset() {\n      this.interacted = false;\n      if (this._completedOverride != null) {\n        this._completedOverride = false;\n      }\n      if (this._customError != null) {\n        this._customError = false;\n      }\n      if (this.stepControl) {\n        // Reset the forms since the default error state matchers will show errors on submit and we\n        // want the form to be back to its initial state (see #29781). Submitted state is on the\n        // individual directives, rather than the control, so we need to reset them ourselves.\n        this._childForms?.forEach(form => form.resetForm?.());\n        this.stepControl.reset();\n      }\n    }\n    ngOnChanges() {\n      // Since basically all inputs of the MatStep get proxied through the view down to the\n      // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n      this._stepper._stateChanged();\n    }\n    _markAsInteracted() {\n      if (!this.interacted) {\n        this.interacted = true;\n        this.interactedStream.emit(this);\n      }\n    }\n    /** Determines whether the error state can be shown. */\n    _showError() {\n      // We want to show the error state either if the user opted into/out of it using the\n      // global options, or if they've explicitly set it through the `hasError` input.\n      return this._stepperOptions.showError ?? this._customError != null;\n    }\n    static ɵfac = function CdkStep_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStep)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkStep,\n      selectors: [[\"cdk-step\"]],\n      contentQueries: function CdkStep_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkStepLabel, 5);\n          i0.ɵɵcontentQuery(dirIndex,\n          // Note: we look for `ControlContainer` here, because both `NgForm` and `FormGroupDirective`\n          // provides themselves as such, but we don't want to have a concrete reference to both of\n          // the directives. The type is marked as `Partial` in case we run into a class that provides\n          // itself as `ControlContainer` but doesn't have the same interface as the directives.\n          ControlContainer, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.stepLabel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._childForms = _t);\n        }\n      },\n      viewQuery: function CdkStep_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        }\n      },\n      inputs: {\n        stepControl: \"stepControl\",\n        label: \"label\",\n        errorMessage: \"errorMessage\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        state: \"state\",\n        editable: [2, \"editable\", \"editable\", booleanAttribute],\n        optional: [2, \"optional\", \"optional\", booleanAttribute],\n        completed: [2, \"completed\", \"completed\", booleanAttribute],\n        hasError: [2, \"hasError\", \"hasError\", booleanAttribute]\n      },\n      outputs: {\n        interactedStream: \"interacted\"\n      },\n      exportAs: [\"cdkStep\"],\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function CdkStep_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, CdkStep_ng_template_0_Template, 1, 0, \"ng-template\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return CdkStep;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkStepper = /*#__PURE__*/(() => {\n  class CdkStepper {\n    _dir = inject(Directionality, {\n      optional: true\n    });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Used for managing keyboard focus. */\n    _keyManager;\n    /** Full list of steps inside the stepper, including inside nested steppers. */\n    _steps;\n    /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n    steps = new QueryList();\n    /** The list of step headers of the steps in the stepper. */\n    _stepHeader;\n    /** List of step headers sorted based on their DOM order. */\n    _sortedHeaders = new QueryList();\n    /** Whether the validity of previous steps should be checked or not. */\n    linear = false;\n    /** The index of the selected step. */\n    get selectedIndex() {\n      return this._selectedIndex;\n    }\n    set selectedIndex(index) {\n      if (this._steps) {\n        // Ensure that the index can't be out of bounds.\n        if (!this._isValidIndex(index) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n        }\n        if (this._selectedIndex !== index) {\n          this.selected?._markAsInteracted();\n          if (!this._anyControlsInvalidOrPending(index) && (index >= this._selectedIndex || this.steps.toArray()[index].editable)) {\n            this._updateSelectedItemIndex(index);\n          }\n        }\n      } else {\n        this._selectedIndex = index;\n      }\n    }\n    _selectedIndex = 0;\n    /** The step that is selected. */\n    get selected() {\n      return this.steps ? this.steps.toArray()[this.selectedIndex] : undefined;\n    }\n    set selected(step) {\n      this.selectedIndex = step && this.steps ? this.steps.toArray().indexOf(step) : -1;\n    }\n    /** Event emitted when the selected step has changed. */\n    selectionChange = new EventEmitter();\n    /** Output to support two-way binding on `[(selectedIndex)]` */\n    selectedIndexChange = new EventEmitter();\n    /** Used to track unique ID for each stepper component. */\n    _groupId = inject(_IdGenerator).getId('cdk-stepper-');\n    /** Orientation of the stepper. */\n    get orientation() {\n      return this._orientation;\n    }\n    set orientation(value) {\n      // This is a protected method so that `MatStepper` can hook into it.\n      this._orientation = value;\n      if (this._keyManager) {\n        this._keyManager.withVerticalOrientation(value === 'vertical');\n      }\n    }\n    _orientation = 'horizontal';\n    constructor() {}\n    ngAfterContentInit() {\n      this._steps.changes.pipe(startWith(this._steps), takeUntil(this._destroyed)).subscribe(steps => {\n        this.steps.reset(steps.filter(step => step._stepper === this));\n        this.steps.notifyOnChanges();\n      });\n    }\n    ngAfterViewInit() {\n      // If the step headers are defined outside of the `ngFor` that renders the steps, like in the\n      // Material stepper, they won't appear in the `QueryList` in the same order as they're\n      // rendered in the DOM which will lead to incorrect keyboard navigation. We need to sort\n      // them manually to ensure that they're correct. Alternatively, we can change the Material\n      // template to inline the headers in the `ngFor`, but that'll result in a lot of\n      // code duplication. See #23539.\n      this._stepHeader.changes.pipe(startWith(this._stepHeader), takeUntil(this._destroyed)).subscribe(headers => {\n        this._sortedHeaders.reset(headers.toArray().sort((a, b) => {\n          const documentPosition = a._elementRef.nativeElement.compareDocumentPosition(b._elementRef.nativeElement);\n          // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n          // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n          // tslint:disable-next-line:no-bitwise\n          return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n        }));\n        this._sortedHeaders.notifyOnChanges();\n      });\n      // Note that while the step headers are content children by default, any components that\n      // extend this one might have them as view children. We initialize the keyboard handling in\n      // AfterViewInit so we're guaranteed for both view and content children to be defined.\n      this._keyManager = new FocusKeyManager(this._sortedHeaders).withWrap().withHomeAndEnd().withVerticalOrientation(this._orientation === 'vertical');\n      // The selected index may have changed between when the component was created and when the\n      // key manager was initialized. Use `updateActiveItem` so it's correct, but it doesn't steal\n      // away focus from the user.\n      this._keyManager.updateActiveItem(this.selectedIndex);\n      (this._dir ? this._dir.change : of()).pipe(startWith(this._layoutDirection()), takeUntil(this._destroyed)).subscribe(direction => this._keyManager?.withHorizontalOrientation(direction));\n      this._keyManager.updateActiveItem(this._selectedIndex);\n      // No need to `takeUntil` here, because we're the ones destroying `steps`.\n      this.steps.changes.subscribe(() => {\n        if (!this.selected) {\n          this._selectedIndex = Math.max(this._selectedIndex - 1, 0);\n        }\n      });\n      // The logic which asserts that the selected index is within bounds doesn't run before the\n      // steps are initialized, because we don't how many steps there are yet so we may have an\n      // invalid index on init. If that's the case, auto-correct to the default so we don't throw.\n      if (!this._isValidIndex(this._selectedIndex)) {\n        this._selectedIndex = 0;\n      }\n      // For linear step and selected index is greater than zero,\n      // set all the previous steps to interacted so that we can navigate to previous steps.\n      if (this.linear && this._selectedIndex > 0) {\n        const visitedSteps = this.steps.toArray().slice(0, this._selectedIndex);\n        for (const step of visitedSteps) {\n          step._markAsInteracted();\n        }\n      }\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this.steps.destroy();\n      this._sortedHeaders.destroy();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** Selects and focuses the next step in list. */\n    next() {\n      this.selectedIndex = Math.min(this._selectedIndex + 1, this.steps.length - 1);\n    }\n    /** Selects and focuses the previous step in list. */\n    previous() {\n      this.selectedIndex = Math.max(this._selectedIndex - 1, 0);\n    }\n    /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n    reset() {\n      this._updateSelectedItemIndex(0);\n      this.steps.forEach(step => step.reset());\n      this._stateChanged();\n    }\n    /** Returns a unique id for each step label element. */\n    _getStepLabelId(i) {\n      return `${this._groupId}-label-${i}`;\n    }\n    /** Returns unique id for each step content element. */\n    _getStepContentId(i) {\n      return `${this._groupId}-content-${i}`;\n    }\n    /** Marks the component to be change detected. */\n    _stateChanged() {\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Returns position state of the step with the given index. */\n    _getAnimationDirection(index) {\n      const position = index - this._selectedIndex;\n      if (position < 0) {\n        return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n      } else if (position > 0) {\n        return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n      }\n      return 'current';\n    }\n    /** Returns the type of icon to be displayed. */\n    _getIndicatorType(index, state = STEP_STATE.NUMBER) {\n      const step = this.steps.toArray()[index];\n      const isCurrentStep = this._isCurrentStep(index);\n      return step._displayDefaultIndicatorType ? this._getDefaultIndicatorLogic(step, isCurrentStep) : this._getGuidelineLogic(step, isCurrentStep, state);\n    }\n    _getDefaultIndicatorLogic(step, isCurrentStep) {\n      if (step._showError() && step.hasError && !isCurrentStep) {\n        return STEP_STATE.ERROR;\n      } else if (!step.completed || isCurrentStep) {\n        return STEP_STATE.NUMBER;\n      } else {\n        return step.editable ? STEP_STATE.EDIT : STEP_STATE.DONE;\n      }\n    }\n    _getGuidelineLogic(step, isCurrentStep, state = STEP_STATE.NUMBER) {\n      if (step._showError() && step.hasError && !isCurrentStep) {\n        return STEP_STATE.ERROR;\n      } else if (step.completed && !isCurrentStep) {\n        return STEP_STATE.DONE;\n      } else if (step.completed && isCurrentStep) {\n        return state;\n      } else if (step.editable && isCurrentStep) {\n        return STEP_STATE.EDIT;\n      } else {\n        return state;\n      }\n    }\n    _isCurrentStep(index) {\n      return this._selectedIndex === index;\n    }\n    /** Returns the index of the currently-focused step header. */\n    _getFocusIndex() {\n      return this._keyManager ? this._keyManager.activeItemIndex : this._selectedIndex;\n    }\n    _updateSelectedItemIndex(newIndex) {\n      const stepsArray = this.steps.toArray();\n      this.selectionChange.emit({\n        selectedIndex: newIndex,\n        previouslySelectedIndex: this._selectedIndex,\n        selectedStep: stepsArray[newIndex],\n        previouslySelectedStep: stepsArray[this._selectedIndex]\n      });\n      // If focus is inside the stepper, move it to the next header, otherwise it may become\n      // lost when the active step content is hidden. We can't be more granular with the check\n      // (e.g. checking whether focus is inside the active step), because we don't have a\n      // reference to the elements that are rendering out the content.\n      if (this._keyManager) {\n        this._containsFocus() ? this._keyManager.setActiveItem(newIndex) : this._keyManager.updateActiveItem(newIndex);\n      }\n      this._selectedIndex = newIndex;\n      this.selectedIndexChange.emit(this._selectedIndex);\n      this._stateChanged();\n    }\n    _onKeydown(event) {\n      const hasModifier = hasModifierKey(event);\n      const keyCode = event.keyCode;\n      const manager = this._keyManager;\n      if (manager?.activeItemIndex != null && !hasModifier && (keyCode === SPACE || keyCode === ENTER)) {\n        this.selectedIndex = manager.activeItemIndex;\n        event.preventDefault();\n      } else {\n        manager?.setFocusOrigin('keyboard').onKeydown(event);\n      }\n    }\n    _anyControlsInvalidOrPending(index) {\n      if (this.linear && index >= 0) {\n        return this.steps.toArray().slice(0, index).some(step => {\n          const control = step.stepControl;\n          const isIncomplete = control ? control.invalid || control.pending || !step.interacted : !step.completed;\n          return isIncomplete && !step.optional && !step._completedOverride;\n        });\n      }\n      return false;\n    }\n    _layoutDirection() {\n      return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Checks whether the stepper contains the focused element. */\n    _containsFocus() {\n      const stepperElement = this._elementRef.nativeElement;\n      const focusedElement = _getFocusedElementPierceShadowDom();\n      return stepperElement === focusedElement || stepperElement.contains(focusedElement);\n    }\n    /** Checks whether the passed-in index is a valid step index. */\n    _isValidIndex(index) {\n      return index > -1 && (!this.steps || index < this.steps.length);\n    }\n    static ɵfac = function CdkStepper_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepper)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepper,\n      selectors: [[\"\", \"cdkStepper\", \"\"]],\n      contentQueries: function CdkStepper_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkStep, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkStepHeader, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._steps = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._stepHeader = _t);\n        }\n      },\n      inputs: {\n        linear: [2, \"linear\", \"linear\", booleanAttribute],\n        selectedIndex: [2, \"selectedIndex\", \"selectedIndex\", numberAttribute],\n        selected: \"selected\",\n        orientation: \"orientation\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        selectedIndexChange: \"selectedIndexChange\"\n      },\n      exportAs: [\"cdkStepper\"]\n    });\n  }\n  return CdkStepper;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Button that moves to the next step in a stepper workflow. */\nlet CdkStepperNext = /*#__PURE__*/(() => {\n  class CdkStepperNext {\n    _stepper = inject(CdkStepper);\n    /** Type of the next button. Defaults to \"submit\" if not specified. */\n    type = 'submit';\n    constructor() {}\n    static ɵfac = function CdkStepperNext_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepperNext)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepperNext,\n      selectors: [[\"button\", \"cdkStepperNext\", \"\"]],\n      hostVars: 1,\n      hostBindings: function CdkStepperNext_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkStepperNext_click_HostBindingHandler() {\n            return ctx._stepper.next();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        type: \"type\"\n      }\n    });\n  }\n  return CdkStepperNext;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Button that moves to the previous step in a stepper workflow. */\nlet CdkStepperPrevious = /*#__PURE__*/(() => {\n  class CdkStepperPrevious {\n    _stepper = inject(CdkStepper);\n    /** Type of the previous button. Defaults to \"button\" if not specified. */\n    type = 'button';\n    constructor() {}\n    static ɵfac = function CdkStepperPrevious_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepperPrevious)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepperPrevious,\n      selectors: [[\"button\", \"cdkStepperPrevious\", \"\"]],\n      hostVars: 1,\n      hostBindings: function CdkStepperPrevious_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkStepperPrevious_click_HostBindingHandler() {\n            return ctx._stepper.previous();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        type: \"type\"\n      }\n    });\n  }\n  return CdkStepperPrevious;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkStepperModule = /*#__PURE__*/(() => {\n  class CdkStepperModule {\n    static ɵfac = function CdkStepperModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkStepperModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkStepperModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule]\n    });\n  }\n  return CdkStepperModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { CdkStep, CdkStepHeader, CdkStepLabel, CdkStepper, CdkStepperModule, CdkStepperNext, CdkStepperPrevious, STEPPER_GLOBAL_OPTIONS, STEP_STATE, StepperSelectionEvent };\n//# sourceMappingURL=stepper.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}