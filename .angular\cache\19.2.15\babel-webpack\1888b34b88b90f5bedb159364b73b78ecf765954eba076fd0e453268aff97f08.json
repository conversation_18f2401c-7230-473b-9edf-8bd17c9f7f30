{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableModule } from '@angular/material/table';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/datepicker\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/table\";\nfunction DisciplinaryStatusComponent_mat_card_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 12)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Disciplinary Status Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"form\", 13);\n    i0.ɵɵlistener(\"ngSubmit\", function DisciplinaryStatusComponent_mat_card_23_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(8, \"div\", 14)(9, \"h3\");\n    i0.ɵɵtext(10, \"Employee Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 15)(12, \"mat-form-field\", 7)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Employee ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"mat-form-field\", 7)(17, \"mat-label\");\n    i0.ɵɵtext(18, \"Employee Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 14)(21, \"h3\");\n    i0.ɵɵtext(22, \"Punishment History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 15)(24, \"mat-form-field\", 7)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"Past Punishments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"mat-select\", 18);\n    i0.ɵɵlistener(\"selectionChange\", function DisciplinaryStatusComponent_mat_card_23_Template_mat_select_selectionChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPunishmentChange($event));\n    });\n    i0.ɵɵelementStart(28, \"mat-option\", 19);\n    i0.ɵɵtext(29, \"No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"mat-option\", 19);\n    i0.ɵɵtext(31, \"Yes\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"mat-form-field\", 7)(33, \"mat-label\");\n    i0.ɵɵtext(34, \"Type of Punishment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"mat-select\", 20)(36, \"mat-option\", 21);\n    i0.ɵɵtext(37, \"None\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"mat-option\", 22);\n    i0.ɵɵtext(39, \"Censure\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"mat-option\", 23);\n    i0.ɵɵtext(41, \"Withholding of Increment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"mat-option\", 24);\n    i0.ɵɵtext(43, \"Recovery from Pay\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"mat-option\", 25);\n    i0.ɵɵtext(45, \"Reduction in Rank\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"mat-option\", 26);\n    i0.ɵɵtext(47, \"Compulsory Retirement\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"mat-option\", 27);\n    i0.ɵɵtext(49, \"Dismissal\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(50, \"div\", 15)(51, \"mat-form-field\", 7)(52, \"mat-label\");\n    i0.ɵɵtext(53, \"Date of Punishment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"input\", 28)(55, \"mat-datepicker-toggle\", 29)(56, \"mat-datepicker\", null, 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"mat-form-field\", 7)(59, \"mat-label\");\n    i0.ɵɵtext(60, \"Currency in Force?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"mat-select\", 30)(62, \"mat-option\", 19);\n    i0.ɵɵtext(63, \"No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"mat-option\", 19);\n    i0.ɵɵtext(65, \"Yes\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(66, \"div\", 14)(67, \"h3\");\n    i0.ɵɵtext(68, \"Panel History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 15)(70, \"mat-form-field\", 7)(71, \"mat-label\");\n    i0.ɵɵtext(72, \"Passed Over in Earlier Panel?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"mat-select\", 31)(74, \"mat-option\", 19);\n    i0.ɵɵtext(75, \"No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"mat-option\", 19);\n    i0.ɵɵtext(77, \"Yes\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(78, \"mat-form-field\", 7)(79, \"mat-label\");\n    i0.ɵɵtext(80, \"Eligible for Panel (Auto-calculated)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(81, \"input\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(82, \"div\", 14)(83, \"mat-form-field\", 33)(84, \"mat-label\");\n    i0.ɵɵtext(85, \"Remarks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(86, \"textarea\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(87, \"div\", 35)(88, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function DisciplinaryStatusComponent_mat_card_23_Template_button_click_88_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onReset());\n    });\n    i0.ɵɵtext(89, \"Reset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"button\", 37);\n    i0.ɵɵtext(91, \" Update Disciplinary Status \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    let tmp_8_0;\n    let tmp_10_0;\n    const punishmentPicker_r3 = i0.ɵɵreference(57);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"Employee: \", ctx_r1.selectedEmployee.employeeName, \" (\", ctx_r1.selectedEmployee.employeeId, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.disciplinaryForm);\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"value\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !((tmp_6_0 = ctx_r1.disciplinaryForm.get(\"pastPunishments\")) == null ? null : tmp_6_0.value));\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"matDatepicker\", punishmentPicker_r3)(\"disabled\", !((tmp_8_0 = ctx_r1.disciplinaryForm.get(\"pastPunishments\")) == null ? null : tmp_8_0.value));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", punishmentPicker_r3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", !((tmp_10_0 = ctx_r1.disciplinaryForm.get(\"pastPunishments\")) == null ? null : tmp_10_0.value));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", true);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"value\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ctx_r1.getEligibilityStatus())(\"ngClass\", ctx_r1.getEligibilityClass());\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disciplinaryForm.invalid);\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Punishment Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51)(1, \"mat-chip\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getPunishmentSeverityClass(record_r4.punishmentType));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatPunishmentType(record_r4.punishmentType), \" \");\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_th_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const record_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, record_r5.punishmentDate, \"dd/MM/yyyy\"));\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const record_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(record_r6.description);\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_th_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Currency in Force\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51)(1, \"mat-chip\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", record_r7.currencyInForce ? \"status-active\" : \"status-expired\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", record_r7.currencyInForce ? \"Yes\" : \"No\", \" \");\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_th_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Order Number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_td_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const record_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(record_r8.orderNumber);\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_tr_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 53);\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_tr_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 54);\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 38)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Disciplinary Records History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 39)(8, \"table\", 40);\n    i0.ɵɵelementContainerStart(9, 41);\n    i0.ɵɵtemplate(10, DisciplinaryStatusComponent_mat_card_24_th_10_Template, 2, 0, \"th\", 42)(11, DisciplinaryStatusComponent_mat_card_24_td_11_Template, 3, 2, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(12, 44);\n    i0.ɵɵtemplate(13, DisciplinaryStatusComponent_mat_card_24_th_13_Template, 2, 0, \"th\", 42)(14, DisciplinaryStatusComponent_mat_card_24_td_14_Template, 3, 4, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(15, 45);\n    i0.ɵɵtemplate(16, DisciplinaryStatusComponent_mat_card_24_th_16_Template, 2, 0, \"th\", 42)(17, DisciplinaryStatusComponent_mat_card_24_td_17_Template, 2, 1, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(18, 46);\n    i0.ɵɵtemplate(19, DisciplinaryStatusComponent_mat_card_24_th_19_Template, 2, 0, \"th\", 42)(20, DisciplinaryStatusComponent_mat_card_24_td_20_Template, 3, 2, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(21, 47);\n    i0.ɵɵtemplate(22, DisciplinaryStatusComponent_mat_card_24_th_22_Template, 2, 0, \"th\", 42)(23, DisciplinaryStatusComponent_mat_card_24_td_23_Template, 2, 1, \"td\", 43);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(24, DisciplinaryStatusComponent_mat_card_24_tr_24_Template, 1, 0, \"tr\", 48)(25, DisciplinaryStatusComponent_mat_card_24_tr_25_Template, 1, 0, \"tr\", 49);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Past punishment records for \", ctx_r1.selectedEmployee.employeeName, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dataSource\", ctx_r1.disciplinaryRecords);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r1.recordColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r1.recordColumns);\n  }\n}\nfunction DisciplinaryStatusComponent_mat_card_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 55)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Eligibility Summary\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 56)(6, \"div\", 57)(7, \"div\", 58);\n    i0.ɵɵtext(8, \"Past Punishments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 59)(10, \"mat-chip\", 52);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 57)(13, \"div\", 58);\n    i0.ɵɵtext(14, \"Currency in Force\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 59)(16, \"mat-chip\", 52);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 57)(19, \"div\", 58);\n    i0.ɵɵtext(20, \"Passed Over Earlier\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"mat-chip\", 52);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 57)(25, \"div\", 58);\n    i0.ɵɵtext(26, \"Panel Eligibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 59)(28, \"mat-chip\", 52);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", ((tmp_1_0 = ctx_r1.disciplinaryForm.get(\"pastPunishments\")) == null ? null : tmp_1_0.value) ? \"status-warning\" : \"status-success\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_2_0 = ctx_r1.disciplinaryForm.get(\"pastPunishments\")) == null ? null : tmp_2_0.value) ? \"Yes\" : \"No\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ((tmp_3_0 = ctx_r1.disciplinaryForm.get(\"currencyInForce\")) == null ? null : tmp_3_0.value) ? \"status-danger\" : \"status-success\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_4_0 = ctx_r1.disciplinaryForm.get(\"currencyInForce\")) == null ? null : tmp_4_0.value) ? \"Yes\" : \"No\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ((tmp_5_0 = ctx_r1.disciplinaryForm.get(\"passedOverEarlier\")) == null ? null : tmp_5_0.value) ? \"status-warning\" : \"status-success\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_6_0 = ctx_r1.disciplinaryForm.get(\"passedOverEarlier\")) == null ? null : tmp_6_0.value) ? \"Yes\" : \"No\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getEligibilityClass());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getEligibilityStatus(), \" \");\n  }\n}\nexport let DisciplinaryStatusComponent = /*#__PURE__*/(() => {\n  class DisciplinaryStatusComponent {\n    constructor(fb) {\n      this.fb = fb;\n      this.searchEmployeeId = '';\n      this.selectedEmployee = null;\n      this.recordColumns = ['punishmentType', 'punishmentDate', 'description', 'currencyInForce', 'orderNumber'];\n      this.disciplinaryRecords = [];\n      // Sample employee data\n      this.employees = [{\n        employeeId: 'EMP001',\n        employeeName: 'Arun Kumar'\n      }, {\n        employeeId: 'EMP002',\n        employeeName: 'Priya Sharma'\n      }, {\n        employeeId: 'EMP003',\n        employeeName: 'Rajesh Patel'\n      }, {\n        employeeId: 'EMP004',\n        employeeName: 'Meera Krishnan'\n      }];\n      this.disciplinaryForm = this.fb.group({\n        employeeId: ['', Validators.required],\n        employeeName: ['', Validators.required],\n        pastPunishments: [false],\n        punishmentType: [''],\n        punishmentDate: [null],\n        currencyInForce: [false],\n        passedOverEarlier: [false],\n        remarks: ['']\n      });\n    }\n    ngOnInit() {}\n    searchEmployee() {\n      const employee = this.employees.find(emp => emp.employeeId === this.searchEmployeeId);\n      if (employee) {\n        this.selectedEmployee = employee;\n        this.disciplinaryForm.patchValue({\n          employeeId: employee.employeeId,\n          employeeName: employee.employeeName\n        });\n        this.loadDisciplinaryRecords(employee.employeeId);\n      } else {\n        alert('Employee not found');\n      }\n    }\n    loadDisciplinaryRecords(employeeId) {\n      // Sample disciplinary records\n      if (employeeId === 'EMP003') {\n        this.disciplinaryRecords = [{\n          id: 1,\n          employeeId: 'EMP003',\n          employeeName: 'Rajesh Patel',\n          punishmentType: 'censure',\n          punishmentDate: new Date('2023-05-15'),\n          description: 'Late attendance for consecutive days',\n          currencyInForce: true,\n          orderNumber: 'ORD/2023/156'\n        }];\n      } else {\n        this.disciplinaryRecords = [];\n      }\n    }\n    onPunishmentChange(event) {\n      const hasPunishments = event.value;\n      if (!hasPunishments) {\n        this.disciplinaryForm.patchValue({\n          punishmentType: '',\n          punishmentDate: null,\n          currencyInForce: false\n        });\n      }\n    }\n    getEligibilityStatus() {\n      const pastPunishments = this.disciplinaryForm.get('pastPunishments')?.value;\n      const currencyInForce = this.disciplinaryForm.get('currencyInForce')?.value;\n      const passedOverEarlier = this.disciplinaryForm.get('passedOverEarlier')?.value;\n      if (!pastPunishments && !passedOverEarlier) {\n        return 'Eligible';\n      } else if (pastPunishments && currencyInForce) {\n        return 'Not Eligible';\n      } else if (passedOverEarlier) {\n        return 'Under Review';\n      } else {\n        return 'Eligible';\n      }\n    }\n    getEligibilityClass() {\n      const status = this.getEligibilityStatus();\n      switch (status) {\n        case 'Eligible':\n          return 'eligible';\n        case 'Not Eligible':\n          return 'not-eligible';\n        default:\n          return 'status-warning';\n      }\n    }\n    getPunishmentSeverityClass(type) {\n      const majorPunishments = ['reduction', 'compulsory-retirement', 'dismissal'];\n      const minorPunishments = ['censure', 'increment-stop'];\n      if (majorPunishments.includes(type)) return 'punishment-severe';\n      if (minorPunishments.includes(type)) return 'punishment-minor';\n      return 'punishment-major';\n    }\n    formatPunishmentType(type) {\n      const typeMap = {\n        'censure': 'Censure',\n        'increment-stop': 'Withholding of Increment',\n        'recovery': 'Recovery from Pay',\n        'reduction': 'Reduction in Rank',\n        'compulsory-retirement': 'Compulsory Retirement',\n        'dismissal': 'Dismissal'\n      };\n      return typeMap[type] || type;\n    }\n    checkAllRecords() {\n      console.log('Checking all disciplinary records');\n    }\n    onSubmit() {\n      if (this.disciplinaryForm.valid) {\n        console.log('Disciplinary Status Updated:', this.disciplinaryForm.value);\n      }\n    }\n    onReset() {\n      this.disciplinaryForm.reset();\n      this.selectedEmployee = null;\n      this.disciplinaryRecords = [];\n      this.searchEmployeeId = '';\n    }\n    static {\n      this.ɵfac = function DisciplinaryStatusComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DisciplinaryStatusComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DisciplinaryStatusComponent,\n        selectors: [[\"app-disciplinary-status\"]],\n        decls: 26,\n        vars: 4,\n        consts: [[\"punishmentPicker\", \"\"], [1, \"disciplinary-container\"], [1, \"header-section\"], [1, \"page-header\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"search-card\"], [1, \"search-row\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"placeholder\", \"Enter Employee ID\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"status-form-card\", 4, \"ngIf\"], [\"class\", \"records-card\", 4, \"ngIf\"], [\"class\", \"summary-card\", 4, \"ngIf\"], [1, \"status-form-card\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-section\"], [1, \"form-row\"], [\"matInput\", \"\", \"formControlName\", \"employeeId\", \"readonly\", \"\"], [\"matInput\", \"\", \"formControlName\", \"employeeName\", \"readonly\", \"\"], [\"formControlName\", \"pastPunishments\", 3, \"selectionChange\"], [3, \"value\"], [\"formControlName\", \"punishmentType\", 3, \"disabled\"], [\"value\", \"\"], [\"value\", \"censure\"], [\"value\", \"increment-stop\"], [\"value\", \"recovery\"], [\"value\", \"reduction\"], [\"value\", \"compulsory-retirement\"], [\"value\", \"dismissal\"], [\"matInput\", \"\", \"formControlName\", \"punishmentDate\", 3, \"matDatepicker\", \"disabled\"], [\"matIconSuffix\", \"\", 3, \"for\"], [\"formControlName\", \"currencyInForce\", 3, \"disabled\"], [\"formControlName\", \"passedOverEarlier\"], [\"matInput\", \"\", \"readonly\", \"\", 3, \"value\", \"ngClass\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"remarks\", \"rows\", \"4\", \"placeholder\", \"Enter any additional remarks about disciplinary status\"], [1, \"form-actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [1, \"records-card\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"records-table\", 3, \"dataSource\"], [\"matColumnDef\", \"punishmentType\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"punishmentDate\"], [\"matColumnDef\", \"description\"], [\"matColumnDef\", \"currencyInForce\"], [\"matColumnDef\", \"orderNumber\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [3, \"ngClass\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"summary-card\"], [1, \"summary-grid\"], [1, \"summary-item\"], [1, \"summary-label\"], [1, \"summary-value\"]],\n        template: function DisciplinaryStatusComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"h1\");\n            i0.ɵɵtext(4, \"Disciplinary Status Verification\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Check punishment and disciplinary status for promotion eligibility\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function DisciplinaryStatusComponent_Template_button_click_7_listener() {\n              return ctx.checkAllRecords();\n            });\n            i0.ɵɵtext(8, \" Check All Records \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"mat-card\", 5)(10, \"mat-card-header\")(11, \"mat-card-title\");\n            i0.ɵɵtext(12, \"Employee Lookup\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-card-subtitle\");\n            i0.ɵɵtext(14, \"Search employee to verify disciplinary status\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"mat-card-content\")(16, \"div\", 6)(17, \"mat-form-field\", 7)(18, \"mat-label\");\n            i0.ɵɵtext(19, \"Employee ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"input\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function DisciplinaryStatusComponent_Template_input_ngModelChange_20_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchEmployeeId, $event) || (ctx.searchEmployeeId = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function DisciplinaryStatusComponent_Template_button_click_21_listener() {\n              return ctx.searchEmployee();\n            });\n            i0.ɵɵtext(22, \" Search Employee \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(23, DisciplinaryStatusComponent_mat_card_23_Template, 92, 17, \"mat-card\", 9)(24, DisciplinaryStatusComponent_mat_card_24_Template, 26, 4, \"mat-card\", 10)(25, DisciplinaryStatusComponent_mat_card_25_Template, 30, 8, \"mat-card\", 11);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(20);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchEmployeeId);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedEmployee);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedEmployee && ctx.disciplinaryRecords.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedEmployee);\n          }\n        },\n        dependencies: [CommonModule, i2.NgClass, i2.NgIf, i2.DatePipe, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatButtonModule, i4.MatButton, MatFormFieldModule, i5.MatFormField, i5.MatLabel, i5.MatSuffix, MatInputModule, i6.MatInput, MatSelectModule, i7.MatSelect, i7.MatOption, MatDatepickerModule, i8.MatDatepicker, i8.MatDatepickerInput, i8.MatDatepickerToggle, MatNativeDateModule, MatChipsModule, i9.MatChip, MatIconModule, MatTableModule, i10.MatTable, i10.MatHeaderCellDef, i10.MatHeaderRowDef, i10.MatColumnDef, i10.MatCellDef, i10.MatRowDef, i10.MatHeaderCell, i10.MatCell, i10.MatHeaderRow, i10.MatRow, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName, RouterModule],\n        styles: [\".disciplinary-container[_ngcontent-%COMP%]{padding:20px;background-color:#fff;min-height:100vh}.header-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:30px}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:28px;font-weight:600;color:#333}.page-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0 0;color:#666;font-size:16px}.search-card[_ngcontent-%COMP%], .status-form-card[_ngcontent-%COMP%], .records-card[_ngcontent-%COMP%], .summary-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 20px #0000001a;margin-bottom:25px}.search-row[_ngcontent-%COMP%]{display:flex;gap:20px;align-items:flex-end}.search-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{flex:1}.form-section[_ngcontent-%COMP%]{margin-bottom:30px;padding-bottom:20px;border-bottom:1px solid #e0e0e0}.form-section[_ngcontent-%COMP%]:last-child{border-bottom:none}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 20px;font-size:18px;font-weight:500;color:#333}.form-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:20px;margin-bottom:20px}.full-width[_ngcontent-%COMP%]{width:100%}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:15px;margin-top:30px;padding-top:20px;border-top:1px solid #e0e0e0}.table-container[_ngcontent-%COMP%]{overflow-x:auto}.records-table[_ngcontent-%COMP%]{width:100%}.summary-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px}.summary-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.summary-label[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#666}.summary-value[_ngcontent-%COMP%]{display:flex;align-items:center}.status-success[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.status-warning[_ngcontent-%COMP%]{background-color:#feebc8;color:#7b341e}.status-danger[_ngcontent-%COMP%]{background-color:#fed7d7;color:#742a2a}.status-active[_ngcontent-%COMP%]{background-color:#bee3f8;color:#2a4365}.status-expired[_ngcontent-%COMP%]{background-color:#f7fafc;color:#4a5568}.status-eligible[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.status-not-eligible[_ngcontent-%COMP%]{background-color:#fed7d7;color:#742a2a}.punishment-minor[_ngcontent-%COMP%]{background-color:#feebc8;color:#7b341e}.punishment-major[_ngcontent-%COMP%]{background-color:#fed7d7;color:#742a2a}.punishment-severe[_ngcontent-%COMP%]{background-color:#553c9a;color:#fff}@media (max-width: 768px){.header-section[_ngcontent-%COMP%]{flex-direction:column;gap:20px}.form-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.search-row[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.summary-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return DisciplinaryStatusComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}