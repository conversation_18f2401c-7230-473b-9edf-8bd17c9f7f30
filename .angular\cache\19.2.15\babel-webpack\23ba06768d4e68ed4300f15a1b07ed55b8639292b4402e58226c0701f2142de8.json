{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function isEmpty() {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, () => {\n      subscriber.next(false);\n      subscriber.complete();\n    }, () => {\n      subscriber.next(true);\n      subscriber.complete();\n    }));\n  });\n}\n//# sourceMappingURL=isEmpty.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}