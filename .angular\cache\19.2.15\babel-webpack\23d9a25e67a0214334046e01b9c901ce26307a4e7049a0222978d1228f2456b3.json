{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTableModule, MatTableDataSource } from '@angular/material/table';\nimport { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';\nimport { MatSortModule, MatSort } from '@angular/material/sort';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/table\";\nimport * as i3 from \"@angular/material/paginator\";\nimport * as i4 from \"@angular/material/sort\";\nimport * as i5 from \"@angular/material/input\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/chips\";\nimport * as i9 from \"@angular/material/menu\";\nimport * as i10 from \"@angular/material/card\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/list\";\nimport * as i13 from \"@angular/router\";\nconst _c0 = () => [10, 25, 50, 100];\nconst _c1 = a0 => [\"/suitability-report\", a0];\nfunction EmployeeListComponent_th_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 38);\n    i0.ɵɵtext(1, \"Employee\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmployeeListComponent_td_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 39)(1, \"div\", 40)(2, \"div\", 41);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 42)(5, \"div\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 44);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const employee_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getInitials(employee_r1.employeeName), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(employee_r1.employeeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(employee_r1.employeeId);\n  }\n}\nfunction EmployeeListComponent_th_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 45);\n    i0.ɵɵtext(1, \"Designation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmployeeListComponent_td_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const employee_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(employee_r3.designation);\n  }\n}\nfunction EmployeeListComponent_th_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 45);\n    i0.ɵɵtext(1, \"Department\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmployeeListComponent_td_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const employee_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(employee_r4.department);\n  }\n}\nfunction EmployeeListComponent_th_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 45);\n    i0.ɵɵtext(1, \"Joining Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmployeeListComponent_td_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const employee_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, employee_r5.joiningDate, \"dd/MM/yyyy\"));\n  }\n}\nfunction EmployeeListComponent_th_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 45);\n    i0.ɵɵtext(1, \"Working Days\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmployeeListComponent_td_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const employee_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(employee_r6.workingDays);\n  }\n}\nfunction EmployeeListComponent_th_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 45);\n    i0.ɵɵtext(1, \"Eligibility\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmployeeListComponent_td_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 39)(1, \"mat-chip\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const employee_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(employee_r7.eligibilityStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", employee_r7.eligibilityStatus, \" \");\n  }\n}\nfunction EmployeeListComponent_th_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 47);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EmployeeListComponent_td_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 39)(1, \"div\", 48)(2, \"button\", 49);\n    i0.ɵɵtext(3, \" \\u2022\\u2022\\u2022 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 0)(6, \"button\", 50)(7, \"span\");\n    i0.ɵɵtext(8, \"View Report\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function EmployeeListComponent_td_75_Template_button_click_9_listener() {\n      const employee_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editEmployee(employee_r9));\n    });\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function EmployeeListComponent_td_75_Template_button_click_12_listener() {\n      const employee_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewDetails(employee_r9));\n    });\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"View Details\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(15, \"mat-divider\");\n    i0.ɵɵelementStart(16, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function EmployeeListComponent_td_75_Template_button_click_16_listener() {\n      const employee_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.initiatePromotion(employee_r9));\n    });\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Initiate Promotion\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const employee_r9 = ctx.$implicit;\n    const actionMenu_r10 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", actionMenu_r10);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c1, employee_r9.id));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", employee_r9.eligibilityStatus !== \"Eligible\");\n  }\n}\nfunction EmployeeListComponent_tr_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 53);\n  }\n}\nfunction EmployeeListComponent_tr_77_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 54);\n    i0.ɵɵlistener(\"click\", function EmployeeListComponent_tr_77_Template_tr_click_0_listener() {\n      const row_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectEmployee(row_r12));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport let EmployeeListComponent = /*#__PURE__*/(() => {\n  class EmployeeListComponent {\n    constructor() {\n      this.displayedColumns = ['employee', 'designation', 'department', 'joiningDate', 'workingDays', 'eligibilityStatus', 'actions'];\n      this.dataSource = new MatTableDataSource();\n      this.selectedDepartment = '';\n      this.selectedStatus = '';\n      this.employees = [{\n        id: 1,\n        employeeName: 'Saravanan M',\n        employeeId: 'EMP001',\n        designation: 'Software Engineer',\n        department: 'Engineering',\n        joiningDate: new Date('2023-01-15'),\n        workingDays: 365,\n        dateOfBirth: new Date('1995-06-10'),\n        status: 'Active',\n        eligibilityStatus: 'Eligible'\n      }, {\n        id: 2,\n        employeeName: 'Santoshi K',\n        employeeId: 'EMP002',\n        designation: 'HR Executive',\n        department: 'HR',\n        joiningDate: new Date('2023-03-20'),\n        workingDays: 290,\n        dateOfBirth: new Date('1992-08-25'),\n        status: 'Active',\n        eligibilityStatus: 'Under Review'\n      }\n      // Add more sample data...\n      ];\n    }\n    ngOnInit() {\n      this.dataSource.data = this.employees;\n    }\n    ngAfterViewInit() {\n      this.dataSource.paginator = this.paginator;\n      this.dataSource.sort = this.sort;\n    }\n    applyFilter(event) {\n      const filterValue = event.target.value;\n      this.dataSource.filter = filterValue.trim().toLowerCase();\n    }\n    filterByDepartment() {\n      this.applyFilters();\n    }\n    filterByStatus() {\n      this.applyFilters();\n    }\n    applyFilters() {\n      this.dataSource.filterPredicate = (data, filter) => {\n        const matchesDepartment = !this.selectedDepartment || data.department === this.selectedDepartment;\n        const matchesStatus = !this.selectedStatus || data.eligibilityStatus === this.selectedStatus;\n        const matchesSearch = !filter || data.employeeName.toLowerCase().includes(filter) || data.employeeId.toLowerCase().includes(filter) || data.designation.toLowerCase().includes(filter);\n        return matchesDepartment && matchesStatus && matchesSearch;\n      };\n      this.dataSource.filter = Math.random().toString(); // Trigger filter\n    }\n    clearFilters() {\n      this.selectedDepartment = '';\n      this.selectedStatus = '';\n      this.dataSource.filter = '';\n    }\n    getInitials(name) {\n      return name.split(' ').map(n => n[0]).join('').toUpperCase();\n    }\n    getStatusClass(status) {\n      switch (status.toLowerCase().replace(' ', '-')) {\n        case 'eligible':\n          return 'status-eligible';\n        case 'not-eligible':\n          return 'status-not-eligible';\n        case 'under-review':\n          return 'status-under-review';\n        default:\n          return '';\n      }\n    }\n    selectEmployee(employee) {\n      console.log('Selected employee:', employee);\n    }\n    editEmployee(employee) {\n      console.log('Edit employee:', employee);\n    }\n    viewDetails(employee) {\n      console.log('View details:', employee);\n    }\n    initiatePromotion(employee) {\n      console.log('Initiate promotion for:', employee);\n    }\n    static {\n      this.ɵfac = function EmployeeListComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || EmployeeListComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EmployeeListComponent,\n        selectors: [[\"app-employee-list\"]],\n        viewQuery: function EmployeeListComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        decls: 79,\n        vars: 8,\n        consts: [[\"actionMenu\", \"matMenu\"], [1, \"employee-list-container\"], [1, \"promotion-header\"], [1, \"navy-card\", \"filter-card\"], [1, \"filter-row\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Search by name, ID, or designation\", 3, \"keyup\"], [\"appearance\", \"outline\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [\"value\", \"\"], [\"value\", \"Engineering\"], [\"value\", \"HR\"], [\"value\", \"Finance\"], [\"value\", \"Operations\"], [\"value\", \"Eligible\"], [\"value\", \"Not Eligible\"], [\"value\", \"Under Review\"], [\"mat-raised-button\", \"\", 1, \"navy-button\", 3, \"click\"], [1, \"navy-card\", \"table-card\"], [1, \"table-header\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", 1, \"navy-button\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"employee-table\", 3, \"dataSource\"], [\"matColumnDef\", \"employee\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"employeeName\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"designation\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"department\"], [\"matColumnDef\", \"joiningDate\"], [\"matColumnDef\", \"workingDays\"], [\"matColumnDef\", \"eligibilityStatus\"], [\"matColumnDef\", \"actions\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", \"class\", \"employee-row\", 3, \"click\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"showFirstLastButtons\", \"\", 1, \"table-paginator\", 3, \"pageSizeOptions\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"employeeName\"], [\"mat-cell\", \"\"], [1, \"employee-info\"], [1, \"employee-avatar\"], [1, \"employee-details\"], [1, \"employee-name\"], [1, \"employee-id\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [3, \"ngClass\"], [\"mat-header-cell\", \"\"], [1, \"action-buttons\"], [\"mat-icon-button\", \"\", 1, \"action-menu-button\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", 3, \"routerLink\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 3, \"click\", \"disabled\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\", 1, \"employee-row\", 3, \"click\"]],\n        template: function EmployeeListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h1\");\n            i0.ɵɵtext(3, \"Employee List\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"Manage and track employee promotion eligibility\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"mat-card\", 3)(7, \"mat-card-content\")(8, \"div\", 4)(9, \"mat-form-field\", 5)(10, \"mat-label\");\n            i0.ɵɵtext(11, \"Search employees\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"input\", 6);\n            i0.ɵɵlistener(\"keyup\", function EmployeeListComponent_Template_input_keyup_12_listener($event) {\n              return ctx.applyFilter($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"mat-form-field\", 7)(14, \"mat-label\");\n            i0.ɵɵtext(15, \"Department\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"mat-select\", 8);\n            i0.ɵɵtwoWayListener(\"valueChange\", function EmployeeListComponent_Template_mat_select_valueChange_16_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedDepartment, $event) || (ctx.selectedDepartment = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectionChange\", function EmployeeListComponent_Template_mat_select_selectionChange_16_listener() {\n              return ctx.filterByDepartment();\n            });\n            i0.ɵɵelementStart(17, \"mat-option\", 9);\n            i0.ɵɵtext(18, \"All Departments\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"mat-option\", 10);\n            i0.ɵɵtext(20, \"Engineering\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"mat-option\", 11);\n            i0.ɵɵtext(22, \"Human Resources\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"mat-option\", 12);\n            i0.ɵɵtext(24, \"Finance\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"mat-option\", 13);\n            i0.ɵɵtext(26, \"Operations\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(27, \"mat-form-field\", 7)(28, \"mat-label\");\n            i0.ɵɵtext(29, \"Eligibility Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"mat-select\", 8);\n            i0.ɵɵtwoWayListener(\"valueChange\", function EmployeeListComponent_Template_mat_select_valueChange_30_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedStatus, $event) || (ctx.selectedStatus = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectionChange\", function EmployeeListComponent_Template_mat_select_selectionChange_30_listener() {\n              return ctx.filterByStatus();\n            });\n            i0.ɵɵelementStart(31, \"mat-option\", 9);\n            i0.ɵɵtext(32, \"All Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"mat-option\", 14);\n            i0.ɵɵtext(34, \"Eligible\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"mat-option\", 15);\n            i0.ɵɵtext(36, \"Not Eligible\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"mat-option\", 16);\n            i0.ɵɵtext(38, \"Under Review\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(39, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function EmployeeListComponent_Template_button_click_39_listener() {\n              return ctx.clearFilters();\n            });\n            i0.ɵɵtext(40, \" Clear Filters \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(41, \"mat-card\", 18)(42, \"mat-card-header\")(43, \"mat-card-title\")(44, \"div\", 19)(45, \"span\");\n            i0.ɵɵtext(46);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"div\", 20)(48, \"button\", 21);\n            i0.ɵɵtext(49, \" Export \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"button\", 21);\n            i0.ɵɵtext(51, \" Add Employee \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(52, \"mat-card-content\")(53, \"div\", 22)(54, \"table\", 23);\n            i0.ɵɵelementContainerStart(55, 24);\n            i0.ɵɵtemplate(56, EmployeeListComponent_th_56_Template, 2, 0, \"th\", 25)(57, EmployeeListComponent_td_57_Template, 9, 3, \"td\", 26);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(58, 27);\n            i0.ɵɵtemplate(59, EmployeeListComponent_th_59_Template, 2, 0, \"th\", 28)(60, EmployeeListComponent_td_60_Template, 2, 1, \"td\", 26);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(61, 29);\n            i0.ɵɵtemplate(62, EmployeeListComponent_th_62_Template, 2, 0, \"th\", 28)(63, EmployeeListComponent_td_63_Template, 2, 1, \"td\", 26);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(64, 30);\n            i0.ɵɵtemplate(65, EmployeeListComponent_th_65_Template, 2, 0, \"th\", 28)(66, EmployeeListComponent_td_66_Template, 3, 4, \"td\", 26);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(67, 31);\n            i0.ɵɵtemplate(68, EmployeeListComponent_th_68_Template, 2, 0, \"th\", 28)(69, EmployeeListComponent_td_69_Template, 2, 1, \"td\", 26);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(70, 32);\n            i0.ɵɵtemplate(71, EmployeeListComponent_th_71_Template, 2, 0, \"th\", 28)(72, EmployeeListComponent_td_72_Template, 3, 2, \"td\", 26);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(73, 33);\n            i0.ɵɵtemplate(74, EmployeeListComponent_th_74_Template, 2, 0, \"th\", 34)(75, EmployeeListComponent_td_75_Template, 19, 5, \"td\", 26);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵtemplate(76, EmployeeListComponent_tr_76_Template, 1, 0, \"tr\", 35)(77, EmployeeListComponent_tr_77_Template, 1, 0, \"tr\", 36);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(78, \"mat-paginator\", 37);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedDepartment);\n            i0.ɵɵadvance(14);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedStatus);\n            i0.ɵɵadvance(16);\n            i0.ɵɵtextInterpolate1(\"\", ctx.dataSource.filteredData.length, \" Employees\");\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"pageSizeOptions\", i0.ɵɵpureFunction0(7, _c0));\n          }\n        },\n        dependencies: [CommonModule, i1.NgClass, i1.DatePipe, MatTableModule, i2.MatTable, i2.MatHeaderCellDef, i2.MatHeaderRowDef, i2.MatColumnDef, i2.MatCellDef, i2.MatRowDef, i2.MatHeaderCell, i2.MatCell, i2.MatHeaderRow, i2.MatRow, MatPaginatorModule, i3.MatPaginator, MatSortModule, i4.MatSort, i4.MatSortHeader, MatInputModule, i5.MatInput, i6.MatFormField, i6.MatLabel, MatFormFieldModule, MatButtonModule, i7.MatButton, i7.MatIconButton, MatIconModule, MatChipsModule, i8.MatChip, MatMenuModule, i9.MatMenu, i9.MatMenuItem, i9.MatMenuTrigger, MatCardModule, i10.MatCard, i10.MatCardContent, i10.MatCardHeader, i10.MatCardTitle, MatSelectModule, i11.MatSelect, i11.MatOption, MatDividerModule, i12.MatDivider, RouterModule, i13.RouterLink, FormsModule],\n        styles: [\".employee-list-container[_ngcontent-%COMP%]{padding:20px;max-width:1400px;margin:0 auto}.promotion-header[_ngcontent-%COMP%]{margin-bottom:30px}.promotion-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:28px;font-weight:600;color:#333}.promotion-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0 0;color:#666;font-size:16px}.navy-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 20px #0000001a}.filter-card[_ngcontent-%COMP%]{margin-bottom:20px}.filter-row[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:center;flex-wrap:wrap}.search-field[_ngcontent-%COMP%]{flex:1;min-width:300px}.navy-button[_ngcontent-%COMP%]{background-color:#283163;color:#fff}.navy-button[_ngcontent-%COMP%]:hover{background-color:#1e2555}.table-card[_ngcontent-%COMP%]{overflow:hidden}.table-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%}.header-actions[_ngcontent-%COMP%]{display:flex;gap:12px}.table-container[_ngcontent-%COMP%]{overflow-x:auto;max-height:600px}.employee-table[_ngcontent-%COMP%]{width:100%;background:#fff}.employee-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.employee-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:linear-gradient(45deg,#283163,#384173);display:flex;align-items:center;justify-content:center;color:#fff;font-weight:500;font-size:14px}.employee-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.employee-name[_ngcontent-%COMP%]{font-weight:500;color:#283163;font-size:14px}.employee-id[_ngcontent-%COMP%]{font-size:12px;color:#666}.employee-row[_ngcontent-%COMP%]{cursor:pointer;transition:background-color .3s ease}.employee-row[_ngcontent-%COMP%]:hover{background-color:#28316305}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:8px;align-items:center}.action-menu-button[_ngcontent-%COMP%]{color:#283163}.table-paginator[_ngcontent-%COMP%]{border-top:1px solid rgba(0,0,0,.12);background:#28316305}.status-eligible[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#2e7d32}.status-not-eligible[_ngcontent-%COMP%]{background-color:#ffebee;color:#c62828}.status-under-review[_ngcontent-%COMP%]{background-color:#fff3e0;color:#ef6c00}@media (max-width: 768px){.employee-list-container[_ngcontent-%COMP%]{padding:10px}.filter-row[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.search-field[_ngcontent-%COMP%]{min-width:auto}.table-header[_ngcontent-%COMP%]{flex-direction:column;gap:12px;align-items:flex-start}.header-actions[_ngcontent-%COMP%]{width:100%;justify-content:flex-end}}\"]\n      });\n    }\n  }\n  return EmployeeListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}