{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/chips\";\nimport * as i6 from \"@angular/material/tabs\";\nimport * as i7 from \"@angular/material/table\";\nimport * as i8 from \"@angular/material/checkbox\";\nimport * as i9 from \"@angular/material/radio\";\nimport * as i10 from \"@angular/forms\";\nfunction SuitabilityReportComponent_div_6_div_32_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1, \"*Required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"span\", 47);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-chip\", 48);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 49)(7, \"mat-radio-group\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_div_32_Template_mat_radio_group_ngModelChange_7_listener($event) {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r3.status, $event) || (item_r3.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(8, \"mat-radio-button\", 51);\n    i0.ɵɵtext(9, \"YES\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-radio-button\", 52);\n    i0.ɵɵtext(11, \"NO\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-radio-button\", 53);\n    i0.ɵɵtext(13, \"PENDING\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, SuitabilityReportComponent_div_6_div_32_span_14_Template, 2, 0, \"span\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getChecklistStatusClass(item_r3.status, item_r3.required));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r3.status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r3.status);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", item_r3.required);\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const record_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, record_r5.date, \"dd/MM/yyyy\"));\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const record_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(record_r6.type);\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const record_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(record_r7.description);\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"mat-chip\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", record_r8.active ? \"status-active\" : \"status-resolved\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", record_r8.active ? \"Active\" : \"Resolved\", \" \");\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 68);\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 69);\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"h4\");\n    i0.ɵɵtext(2, \"Punishment Records\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"table\", 57);\n    i0.ɵɵelementContainerStart(4, 58);\n    i0.ɵɵtemplate(5, SuitabilityReportComponent_div_6_div_104_th_5_Template, 2, 0, \"th\", 59)(6, SuitabilityReportComponent_div_6_div_104_td_6_Template, 3, 4, \"td\", 60);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(7, 61);\n    i0.ɵɵtemplate(8, SuitabilityReportComponent_div_6_div_104_th_8_Template, 2, 0, \"th\", 59)(9, SuitabilityReportComponent_div_6_div_104_td_9_Template, 2, 1, \"td\", 60);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(10, 62);\n    i0.ɵɵtemplate(11, SuitabilityReportComponent_div_6_div_104_th_11_Template, 2, 0, \"th\", 59)(12, SuitabilityReportComponent_div_6_div_104_td_12_Template, 2, 1, \"td\", 60);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(13, 63);\n    i0.ɵɵtemplate(14, SuitabilityReportComponent_div_6_div_104_th_14_Template, 2, 0, \"th\", 59)(15, SuitabilityReportComponent_div_6_div_104_td_15_Template, 3, 2, \"td\", 60);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(16, SuitabilityReportComponent_div_6_div_104_tr_16_Template, 1, 0, \"tr\", 64)(17, SuitabilityReportComponent_div_6_div_104_tr_17_Template, 1, 0, \"tr\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dataSource\", ctx_r3.punishmentRecords);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r3.punishmentColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r3.punishmentColumns);\n  }\n}\nfunction SuitabilityReportComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-card\", 4)(2, \"mat-card-header\")(3, \"div\", 5)(4, \"div\", 6);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 7)(7, \"mat-card-title\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-card-subtitle\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 8)(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(19, \"mat-card\", 9)(20, \"mat-tab-group\", 10)(21, \"mat-tab\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h3\");\n    i0.ɵɵtext(25, \"Required Documents Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 14)(27, \"mat-chip\", 15);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"mat-chip\", 16);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 17);\n    i0.ɵɵtemplate(32, SuitabilityReportComponent_div_6_div_32_Template, 15, 5, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"mat-tab\", 19)(34, \"div\", 12)(35, \"h3\");\n    i0.ɵɵtext(36, \"Performance Assessment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 20)(38, \"h4\");\n    i0.ɵɵtext(39, \"Overall Performance Rating\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"mat-radio-group\", 21);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_radio_group_ngModelChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.performanceRating, $event) || (ctx_r3.performanceRating = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(41, \"mat-radio-button\", 22);\n    i0.ɵɵtext(42, \"Excellent (90-100%)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"mat-radio-button\", 23);\n    i0.ɵɵtext(44, \"Good (75-89%)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"mat-radio-button\", 24);\n    i0.ɵɵtext(46, \"Satisfactory (60-74%)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"mat-radio-button\", 25);\n    i0.ɵɵtext(48, \"Needs Improvement (Below 60%)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 20)(50, \"h4\");\n    i0.ɵɵtext(51, \"Key Performance Areas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 26)(53, \"div\", 27)(54, \"span\");\n    i0.ɵɵtext(55, \"Technical Competency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"mat-chip\", 28);\n    i0.ɵɵtext(57, \"85%\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 27)(59, \"span\");\n    i0.ɵɵtext(60, \"Communication Skills\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"mat-chip\", 29);\n    i0.ɵɵtext(62, \"78%\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 27)(64, \"span\");\n    i0.ɵɵtext(65, \"Leadership Qualities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"mat-chip\", 29);\n    i0.ɵɵtext(67, \"82%\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 27)(69, \"span\");\n    i0.ɵɵtext(70, \"Problem Solving\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"mat-chip\", 28);\n    i0.ɵɵtext(72, \"88%\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 27)(74, \"span\");\n    i0.ɵɵtext(75, \"Team Collaboration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"mat-chip\", 28);\n    i0.ɵɵtext(77, \"90%\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(78, \"div\", 20)(79, \"h4\");\n    i0.ɵɵtext(80, \"Behavioral Assessment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"div\", 30)(82, \"mat-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_82_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.behavioralTraits.punctuality, $event) || (ctx_r3.behavioralTraits.punctuality = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(83, \"Punctuality and Attendance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"mat-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_84_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.behavioralTraits.integrity, $event) || (ctx_r3.behavioralTraits.integrity = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(85, \"Integrity and Ethics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"mat-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_86_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.behavioralTraits.initiative, $event) || (ctx_r3.behavioralTraits.initiative = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(87, \"Initiative and Proactiveness\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"mat-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_88_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.behavioralTraits.adaptability, $event) || (ctx_r3.behavioralTraits.adaptability = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(89, \"Adaptability to Change\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"mat-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_90_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.behavioralTraits.customerFocus, $event) || (ctx_r3.behavioralTraits.customerFocus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(91, \"Customer/Stakeholder Focus\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(92, \"mat-tab\", 32)(93, \"div\", 12)(94, \"h3\");\n    i0.ɵɵtext(95, \"Disciplinary History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"div\", 33)(97, \"div\", 34)(98, \"h4\");\n    i0.ɵɵtext(99, \"Clean Disciplinary Record\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"p\");\n    i0.ɵɵtext(101, \"No disciplinary actions or punishments recorded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"mat-chip\", 35);\n    i0.ɵɵtext(103, \"CLEAN RECORD\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(104, SuitabilityReportComponent_div_6_div_104_Template, 18, 3, \"div\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(105, \"mat-tab\", 37)(106, \"div\", 12)(107, \"div\", 38)(108, \"div\", 39)(109, \"h3\");\n    i0.ɵɵtext(110, \"Promotion Recommendation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(111, \"div\", 40)(112, \"div\", 41)(113, \"span\");\n    i0.ɵɵtext(114, \"RECOMMENDED FOR PROMOTION\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(115, \"p\");\n    i0.ɵɵtext(116, \"Based on the comprehensive evaluation, the employee meets all criteria for promotion from Seasonal to Regular status.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(117, \"div\", 42)(118, \"button\", 43);\n    i0.ɵɵtext(119, \" Print Report \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(120, \"button\", 43);\n    i0.ɵɵtext(121, \" Download PDF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(122, \"button\", 44);\n    i0.ɵɵtext(123, \" Submit for Approval \");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getInitials(ctx_r3.employee.employeeName), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.employee.employeeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.employee.designation, \" | \", ctx_r3.employee.employeeId, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Region: \", ctx_r3.employee.region, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Joined: \", i0.ɵɵpipeBind2(16, 17, ctx_r3.employee.joiningDate, \"dd/MM/yyyy\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.employee.workingDays, \" working days\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getCompletedCount(), \" Completed\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getPendingCount(), \" Pending\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.employee.checklist);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.performanceRating);\n    i0.ɵɵadvance(42);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.behavioralTraits.punctuality);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.behavioralTraits.integrity);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.behavioralTraits.initiative);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.behavioralTraits.adaptability);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.behavioralTraits.customerFocus);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.punishmentRecords.length > 0);\n  }\n}\nexport let SuitabilityReportComponent = /*#__PURE__*/(() => {\n  class SuitabilityReportComponent {\n    constructor(route) {\n      this.route = route;\n      this.employee = null;\n      this.punishmentColumns = ['description', 'status'];\n      this.punishmentData = [{\n        description: 'Delinquencies/irregularities prior to 5 years',\n        status: 'Eligible'\n      }, {\n        description: 'Award of punishment of Censure within one year',\n        status: 'Eligible'\n      }, {\n        description: 'Stoppage of increments within check period',\n        status: 'Eligible'\n      }];\n    }\n    ngOnInit() {\n      const employeeId = this.route.snapshot.paramMap.get('id');\n      this.loadEmployeeReport(employeeId);\n    }\n    loadEmployeeReport(id) {\n      // Mock data - in real app, this would come from a service\n      this.employee = {\n        id: 1,\n        employeeName: 'Saravanan M',\n        employeeId: 'EMP001',\n        designation: 'Software Engineer',\n        region: 'Tamil Nadu Civil Supplies Corporation',\n        joiningDate: new Date('2023-01-15'),\n        workingDays: 365,\n        dateOfBirth: new Date('1995-06-10'),\n        checklist: [{\n          name: 'First Page Record Sheet',\n          status: 'YES',\n          required: true\n        }, {\n          name: 'Degree Certificate',\n          status: 'YES',\n          required: true\n        }, {\n          name: 'Driving Certificate',\n          status: 'YES',\n          required: false\n        }, {\n          name: '10th Mark Sheet',\n          status: 'YES',\n          required: true\n        }, {\n          name: 'Transfer Certificate',\n          status: 'YES',\n          required: false\n        }, {\n          name: 'Community Certificate',\n          status: 'YES',\n          required: false\n        }, {\n          name: 'Employment Card',\n          status: 'YES',\n          required: false\n        }, {\n          name: 'Aadhar Card',\n          status: 'YES',\n          required: true\n        }, {\n          name: 'Family Card',\n          status: 'YES',\n          required: false\n        }, {\n          name: 'Duty Certificate',\n          status: 'YES',\n          required: false\n        }, {\n          name: 'Photo',\n          status: 'YES',\n          required: true\n        }, {\n          name: 'Bank Account Details',\n          status: 'YES',\n          required: true\n        }]\n      };\n    }\n    getInitials(name) {\n      return name.split(' ').map(n => n[0]).join('').toUpperCase();\n    }\n    getCompletedCount() {\n      return this.employee?.checklist.filter(item => item.status === 'YES').length || 0;\n    }\n    getPendingCount() {\n      return this.employee?.checklist.filter(item => item.status === 'PENDING').length || 0;\n    }\n    getItemStatusClass(status) {\n      return `status-${status.toLowerCase()}`;\n    }\n    getStatusIcon(status) {\n      switch (status) {\n        case 'YES':\n          return 'check_circle';\n        case 'NO':\n          return 'cancel';\n        case 'PENDING':\n          return 'schedule';\n        default:\n          return 'help';\n      }\n    }\n    getStatusIconClass(status) {\n      return `status-${status.toLowerCase()}`;\n    }\n    getStatusChipClass(status) {\n      switch (status) {\n        case 'YES':\n          return 'status-eligible';\n        case 'NO':\n          return 'status-not-eligible';\n        case 'PENDING':\n          return 'status-pending';\n        default:\n          return '';\n      }\n    }\n    updateItemStatus(index, status) {\n      if (this.employee) {\n        this.employee.checklist[index].status = status;\n      }\n    }\n    static {\n      this.ɵfac = function SuitabilityReportComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SuitabilityReportComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SuitabilityReportComponent,\n        selectors: [[\"app-suitability-report\"]],\n        decls: 7,\n        vars: 1,\n        consts: [[1, \"report-container\"], [1, \"promotion-header\"], [\"class\", \"report-content\", 4, \"ngIf\"], [1, \"report-content\"], [1, \"navy-card\", \"employee-info-card\"], [1, \"employee-header\"], [1, \"employee-avatar-large\"], [1, \"employee-info\"], [1, \"employee-meta\"], [1, \"navy-card\", \"report-tabs-card\"], [1, \"report-tabs\"], [\"label\", \"Document Checklist\"], [1, \"tab-content\"], [1, \"checklist-header\"], [1, \"checklist-stats\"], [1, \"status-completed\"], [1, \"status-pending\"], [1, \"checklist-grid\"], [\"class\", \"checklist-item\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Performance Evaluation\"], [1, \"evaluation-section\"], [1, \"rating-group\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"excellent\"], [\"value\", \"good\"], [\"value\", \"satisfactory\"], [\"value\", \"needs-improvement\"], [1, \"performance-areas\"], [1, \"performance-item\"], [1, \"score-excellent\"], [1, \"score-good\"], [1, \"behavioral-checklist\"], [3, \"ngModelChange\", \"ngModel\"], [\"label\", \"Disciplinary Records\"], [1, \"disciplinary-status\"], [1, \"status-card\", \"clean-record\"], [1, \"status-clean\"], [\"class\", \"punishment-history\", 4, \"ngIf\"], [\"label\", \"Final Report\"], [1, \"final-report\"], [1, \"report-summary\"], [1, \"recommendation-card\"], [1, \"recommendation-status\", \"eligible\"], [1, \"report-actions\"], [\"mat-raised-button\", \"\", 1, \"navy-button\"], [\"mat-raised-button\", \"\", \"color\", \"accent\"], [1, \"checklist-item\"], [1, \"item-header\"], [1, \"item-name\"], [3, \"ngClass\"], [1, \"item-controls\"], [1, \"status-radio-group\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"YES\", 1, \"status-yes\"], [\"value\", \"NO\", 1, \"status-no\"], [\"value\", \"PENDING\", 1, \"status-pending\"], [\"class\", \"required-indicator\", 4, \"ngIf\"], [1, \"required-indicator\"], [1, \"punishment-history\"], [\"mat-table\", \"\", 1, \"punishment-table\", 3, \"dataSource\"], [\"matColumnDef\", \"date\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"description\"], [\"matColumnDef\", \"status\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n        template: function SuitabilityReportComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"Suitability Report Input\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"Comprehensive evaluation for promotion eligibility\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(6, SuitabilityReportComponent_div_6_Template, 124, 20, \"div\", 2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.employee);\n          }\n        },\n        dependencies: [CommonModule, i2.NgClass, i2.NgForOf, i2.NgIf, i2.DatePipe, MatCardModule, i3.MatCard, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatButtonModule, i4.MatButton, MatIconModule, MatChipsModule, i5.MatChip, MatTabsModule, i6.MatTab, i6.MatTabGroup, MatTableModule, i7.MatTable, i7.MatHeaderCellDef, i7.MatHeaderRowDef, i7.MatColumnDef, i7.MatCellDef, i7.MatRowDef, i7.MatHeaderCell, i7.MatCell, i7.MatHeaderRow, i7.MatRow, MatCheckboxModule, i8.MatCheckbox, MatRadioModule, i9.MatRadioGroup, i9.MatRadioButton, MatDialogModule, MatExpansionModule, RouterModule, FormsModule, i10.NgControlStatus, i10.NgModel],\n        styles: [\".report-container[_ngcontent-%COMP%]{padding:20px;max-width:1400px;margin:0 auto}.promotion-header[_ngcontent-%COMP%]{margin-bottom:30px}.promotion-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:28px;font-weight:600;color:#333}.promotion-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0 0;color:#666;font-size:16px}.navy-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 20px #0000001a;margin-bottom:25px}.employee-info-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#283163,#384173);color:#fff}.employee-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:20px;width:100%}.employee-avatar-large[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;background:#fff3;display:flex;align-items:center;justify-content:center;color:#fff;font-weight:600;font-size:24px;border:3px solid rgba(255,255,255,.3)}.employee-info[_ngcontent-%COMP%]{flex:1}.employee-meta[_ngcontent-%COMP%]{display:flex;gap:20px;margin-top:8px;flex-wrap:wrap}.employee-meta[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;color:#fffc;font-size:14px}.report-tabs-card[_ngcontent-%COMP%]{overflow:visible}.report-tabs[_ngcontent-%COMP%]   .mat-mdc-tab-body-wrapper[_ngcontent-%COMP%]{padding:20px 0}.tab-content[_ngcontent-%COMP%]{padding:20px}.checklist-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:30px}.checklist-stats[_ngcontent-%COMP%]{display:flex;gap:10px}.checklist-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:20px}.checklist-item[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:8px;padding:20px;background:#fafafa}.item-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px}.item-name[_ngcontent-%COMP%]{font-weight:500;color:#333}.item-controls[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.status-radio-group[_ngcontent-%COMP%]{display:flex;gap:15px}.required-indicator[_ngcontent-%COMP%]{font-size:12px;color:#d32f2f;font-weight:500}.evaluation-section[_ngcontent-%COMP%]{margin-bottom:30px;padding-bottom:20px;border-bottom:1px solid #e0e0e0}.evaluation-section[_ngcontent-%COMP%]:last-child{border-bottom:none}.evaluation-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 15px;font-size:16px;font-weight:500;color:#333}.rating-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px}.performance-areas[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:15px}.performance-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:15px;background:#f8f9fa;border-radius:8px;border:1px solid #e0e0e0}.behavioral-checklist[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:15px}.disciplinary-status[_ngcontent-%COMP%]{margin-bottom:30px}.status-card[_ngcontent-%COMP%]{padding:20px;border-radius:8px;text-align:center}.status-card.clean-record[_ngcontent-%COMP%]{background:#e8f5e8;border:2px solid #4caf50}.status-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 10px;color:#2e7d32}.status-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 15px;color:#2e7d32}.punishment-table[_ngcontent-%COMP%]{background:#fff;border-radius:8px;overflow:hidden;border:1px solid #e0e0e0}.final-report[_ngcontent-%COMP%]{text-align:center}.recommendation-card[_ngcontent-%COMP%]{background:#4caf501a;border:2px solid #4caf50;border-radius:12px;padding:24px;margin:20px 0}.recommendation-card[_ngcontent-%COMP%]   .recommendation-status[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;font-size:18px;font-weight:600;color:#2e7d32;margin-bottom:12px}.recommendation-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#2e7d32;margin:0}.report-actions[_ngcontent-%COMP%]{display:flex;gap:16px;justify-content:center;margin-top:30px}.navy-button[_ngcontent-%COMP%]{background-color:#283163;color:#fff}.navy-button[_ngcontent-%COMP%]:hover{background-color:#1e2555}.status-completed[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.status-pending[_ngcontent-%COMP%]{background-color:#feebc8;color:#7b341e}.status-clean[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.status-active[_ngcontent-%COMP%]{background-color:#fed7d7;color:#742a2a}.status-resolved[_ngcontent-%COMP%], .score-excellent[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.score-good[_ngcontent-%COMP%]{background-color:#bee3f8;color:#2a4365}@media (max-width: 768px){.report-container[_ngcontent-%COMP%]{padding:10px}.checklist-grid[_ngcontent-%COMP%], .performance-areas[_ngcontent-%COMP%], .behavioral-checklist[_ngcontent-%COMP%]{grid-template-columns:1fr}.employee-header[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.checklist-header[_ngcontent-%COMP%]{flex-direction:column;gap:15px;align-items:flex-start}.report-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:center}}\"]\n      });\n    }\n  }\n  return SuitabilityReportComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}