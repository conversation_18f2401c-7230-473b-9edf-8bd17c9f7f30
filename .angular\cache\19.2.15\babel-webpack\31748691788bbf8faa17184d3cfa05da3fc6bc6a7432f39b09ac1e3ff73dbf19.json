{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, booleanAttribute, NgModule } from '@angular/core';\nimport { CdkTable, CDK_TABLE, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, STICKY_POSITIONING_LISTENER, HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet, CdkCellDef, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkHeaderCell, CdkFooterCell, CdkCell, CdkHeaderRowDef, CdkFooterRowDef, CdkRowDef, CdkHeaderRow, CdkCellOutlet, CdkFooterRow, CdkRow, CdkNoDataRow, CdkTextColumn, CdkTableModule } from '@angular/cdk/table';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, _DisposeViewRepeaterStrategy, DataSource } from '@angular/cdk/collections';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { BehaviorSubject, Subject, merge, of, combineLatest } from 'rxjs';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { map } from 'rxjs/operators';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]], \"*\"];\nconst _c1 = [\"caption\", \"colgroup, col\", \"*\"];\nfunction MatTable_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction MatTable_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 0);\n    i0.ɵɵelementContainer(1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"tbody\", 2);\n    i0.ɵɵelementContainer(3, 3)(4, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"tfoot\", 0);\n    i0.ɵɵelementContainer(6, 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatTable_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 1)(1, 3)(2, 4)(3, 5);\n  }\n}\nfunction MatTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction MatTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dataAccessor(data_r2, ctx_r0.name), \" \");\n  }\n}\nlet MatRecycleRows = /*#__PURE__*/(() => {\n  class MatRecycleRows {\n    static ɵfac = function MatRecycleRows_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRecycleRows)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRecycleRows,\n      selectors: [[\"mat-table\", \"recycleRows\", \"\"], [\"table\", \"mat-table\", \"\", \"recycleRows\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }])]\n    });\n  }\n  return MatRecycleRows;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatTable = /*#__PURE__*/(() => {\n  class MatTable extends CdkTable {\n    /** Overrides the sticky CSS class set by the `CdkTable`. */\n    stickyCssClass = 'mat-mdc-table-sticky';\n    /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n    needsPositionStickyOnElement = false;\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatTable_BaseFactory;\n      return function MatTable_Factory(__ngFactoryType__) {\n        return (ɵMatTable_BaseFactory || (ɵMatTable_BaseFactory = i0.ɵɵgetInheritedFactory(MatTable)))(__ngFactoryType__ || MatTable);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTable,\n      selectors: [[\"mat-table\"], [\"table\", \"mat-table\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-table\", \"mdc-data-table__table\"],\n      hostVars: 2,\n      hostBindings: function MatTable_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-table-fixed-layout\", ctx.fixedLayout);\n        }\n      },\n      exportAs: [\"matTable\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTable,\n        useExisting: MatTable\n      }, {\n        provide: CDK_TABLE,\n        useExisting: MatTable\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n      //  is only included in the build if used.\n      {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }]), i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 2,\n      consts: [[\"role\", \"rowgroup\"], [\"headerRowOutlet\", \"\"], [\"role\", \"rowgroup\", 1, \"mdc-data-table__content\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n      template: function MatTable_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵprojection(0);\n          i0.ɵɵprojection(1, 1);\n          i0.ɵɵtemplate(2, MatTable_Conditional_2_Template, 1, 0)(3, MatTable_Conditional_3_Template, 7, 0)(4, MatTable_Conditional_4_Template, 4, 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx._isServer ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._isNativeHtmlTable ? 3 : 4);\n        }\n      },\n      dependencies: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n      styles: [\".mat-mdc-table-sticky{position:sticky !important}mat-table{display:block}mat-header-row{min-height:var(--mat-table-header-container-height, 56px)}mat-row{min-height:var(--mat-table-row-item-container-height, 52px)}mat-footer-row{min-height:var(--mat-table-footer-container-height, 52px)}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{min-width:100%;border:0;border-spacing:0;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color, var(--mat-sys-surface))}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell{text-align:right}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-header-headline-font, var(--mat-sys-title-small-font, Roboto, sans-serif));line-height:var(--mat-table-header-headline-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-table-header-headline-size, var(--mat-sys-title-small-size, 14px));font-weight:var(--mat-table-header-headline-weight, var(--mat-sys-title-small-weight, 500))}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-row-item-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-row-item-label-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-row-item-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-footer-supporting-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-footer-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-footer-supporting-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-footer-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-table-footer-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking, var(--mat-sys-title-small-tracking));font-weight:inherit;line-height:inherit;box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mat-mdc-header-cell{text-align:right}.mdc-data-table__row:last-child>.mat-mdc-header-cell{border-bottom:none}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking));line-height:inherit}.mdc-data-table__row:last-child>.mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking))}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\\n\"],\n      encapsulation: 2\n    });\n  }\n  return MatTable;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nlet MatCellDef = /*#__PURE__*/(() => {\n  class MatCellDef extends CdkCellDef {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatCellDef_BaseFactory;\n      return function MatCellDef_Factory(__ngFactoryType__) {\n        return (ɵMatCellDef_BaseFactory || (ɵMatCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatCellDef)))(__ngFactoryType__ || MatCellDef);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCellDef,\n      selectors: [[\"\", \"matCellDef\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkCellDef,\n        useExisting: MatCellDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatCellDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nlet MatHeaderCellDef = /*#__PURE__*/(() => {\n  class MatHeaderCellDef extends CdkHeaderCellDef {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatHeaderCellDef_BaseFactory;\n      return function MatHeaderCellDef_Factory(__ngFactoryType__) {\n        return (ɵMatHeaderCellDef_BaseFactory || (ɵMatHeaderCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderCellDef)))(__ngFactoryType__ || MatHeaderCellDef);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHeaderCellDef,\n      selectors: [[\"\", \"matHeaderCellDef\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkHeaderCellDef,\n        useExisting: MatHeaderCellDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatHeaderCellDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nlet MatFooterCellDef = /*#__PURE__*/(() => {\n  class MatFooterCellDef extends CdkFooterCellDef {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatFooterCellDef_BaseFactory;\n      return function MatFooterCellDef_Factory(__ngFactoryType__) {\n        return (ɵMatFooterCellDef_BaseFactory || (ɵMatFooterCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterCellDef)))(__ngFactoryType__ || MatFooterCellDef);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFooterCellDef,\n      selectors: [[\"\", \"matFooterCellDef\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkFooterCellDef,\n        useExisting: MatFooterCellDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatFooterCellDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\nlet MatColumnDef = /*#__PURE__*/(() => {\n  class MatColumnDef extends CdkColumnDef {\n    /** Unique name for this column. */\n    get name() {\n      return this._name;\n    }\n    set name(name) {\n      this._setNameInput(name);\n    }\n    /**\n     * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n     * In the future, this will only add \"mat-column-\" and columnCssClassName\n     * will change from type string[] to string.\n     * @docs-private\n     */\n    _updateColumnCssClassName() {\n      super._updateColumnCssClassName();\n      this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`);\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatColumnDef_BaseFactory;\n      return function MatColumnDef_Factory(__ngFactoryType__) {\n        return (ɵMatColumnDef_BaseFactory || (ɵMatColumnDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatColumnDef)))(__ngFactoryType__ || MatColumnDef);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatColumnDef,\n      selectors: [[\"\", \"matColumnDef\", \"\"]],\n      inputs: {\n        name: [0, \"matColumnDef\", \"name\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkColumnDef,\n        useExisting: MatColumnDef\n      }, {\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: MatColumnDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatColumnDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Header cell template container that adds the right classes and role. */\nlet MatHeaderCell = /*#__PURE__*/(() => {\n  class MatHeaderCell extends CdkHeaderCell {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatHeaderCell_BaseFactory;\n      return function MatHeaderCell_Factory(__ngFactoryType__) {\n        return (ɵMatHeaderCell_BaseFactory || (ɵMatHeaderCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderCell)))(__ngFactoryType__ || MatHeaderCell);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHeaderCell,\n      selectors: [[\"mat-header-cell\"], [\"th\", \"mat-header-cell\", \"\"]],\n      hostAttrs: [\"role\", \"columnheader\", 1, \"mat-mdc-header-cell\", \"mdc-data-table__header-cell\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatHeaderCell;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Footer cell template container that adds the right classes and role. */\nlet MatFooterCell = /*#__PURE__*/(() => {\n  class MatFooterCell extends CdkFooterCell {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatFooterCell_BaseFactory;\n      return function MatFooterCell_Factory(__ngFactoryType__) {\n        return (ɵMatFooterCell_BaseFactory || (ɵMatFooterCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterCell)))(__ngFactoryType__ || MatFooterCell);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFooterCell,\n      selectors: [[\"mat-footer-cell\"], [\"td\", \"mat-footer-cell\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-footer-cell\", \"mdc-data-table__cell\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatFooterCell;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Cell template container that adds the right classes and role. */\nlet MatCell = /*#__PURE__*/(() => {\n  class MatCell extends CdkCell {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatCell_BaseFactory;\n      return function MatCell_Factory(__ngFactoryType__) {\n        return (ɵMatCell_BaseFactory || (ɵMatCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatCell)))(__ngFactoryType__ || MatCell);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCell,\n      selectors: [[\"mat-cell\"], [\"td\", \"mat-cell\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-cell\", \"mdc-data-table__cell\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatCell;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n// We can't reuse `CDK_ROW_TEMPLATE` because it's incompatible with local compilation mode.\nconst ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nlet MatHeaderRowDef = /*#__PURE__*/(() => {\n  class MatHeaderRowDef extends CdkHeaderRowDef {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatHeaderRowDef_BaseFactory;\n      return function MatHeaderRowDef_Factory(__ngFactoryType__) {\n        return (ɵMatHeaderRowDef_BaseFactory || (ɵMatHeaderRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderRowDef)))(__ngFactoryType__ || MatHeaderRowDef);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHeaderRowDef,\n      selectors: [[\"\", \"matHeaderRowDef\", \"\"]],\n      inputs: {\n        columns: [0, \"matHeaderRowDef\", \"columns\"],\n        sticky: [2, \"matHeaderRowDefSticky\", \"sticky\", booleanAttribute]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkHeaderRowDef,\n        useExisting: MatHeaderRowDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatHeaderRowDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nlet MatFooterRowDef = /*#__PURE__*/(() => {\n  class MatFooterRowDef extends CdkFooterRowDef {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatFooterRowDef_BaseFactory;\n      return function MatFooterRowDef_Factory(__ngFactoryType__) {\n        return (ɵMatFooterRowDef_BaseFactory || (ɵMatFooterRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterRowDef)))(__ngFactoryType__ || MatFooterRowDef);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFooterRowDef,\n      selectors: [[\"\", \"matFooterRowDef\", \"\"]],\n      inputs: {\n        columns: [0, \"matFooterRowDef\", \"columns\"],\n        sticky: [2, \"matFooterRowDefSticky\", \"sticky\", booleanAttribute]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkFooterRowDef,\n        useExisting: MatFooterRowDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatFooterRowDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nlet MatRowDef = /*#__PURE__*/(() => {\n  class MatRowDef extends CdkRowDef {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatRowDef_BaseFactory;\n      return function MatRowDef_Factory(__ngFactoryType__) {\n        return (ɵMatRowDef_BaseFactory || (ɵMatRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatRowDef)))(__ngFactoryType__ || MatRowDef);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRowDef,\n      selectors: [[\"\", \"matRowDef\", \"\"]],\n      inputs: {\n        columns: [0, \"matRowDefColumns\", \"columns\"],\n        when: [0, \"matRowDefWhen\", \"when\"]\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkRowDef,\n        useExisting: MatRowDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatRowDef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nlet MatHeaderRow = /*#__PURE__*/(() => {\n  class MatHeaderRow extends CdkHeaderRow {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatHeaderRow_BaseFactory;\n      return function MatHeaderRow_Factory(__ngFactoryType__) {\n        return (ɵMatHeaderRow_BaseFactory || (ɵMatHeaderRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderRow)))(__ngFactoryType__ || MatHeaderRow);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatHeaderRow,\n      selectors: [[\"mat-header-row\"], [\"tr\", \"mat-header-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-header-row\", \"mdc-data-table__header-row\"],\n      exportAs: [\"matHeaderRow\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkHeaderRow,\n        useExisting: MatHeaderRow\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function MatHeaderRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n  return MatHeaderRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nlet MatFooterRow = /*#__PURE__*/(() => {\n  class MatFooterRow extends CdkFooterRow {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatFooterRow_BaseFactory;\n      return function MatFooterRow_Factory(__ngFactoryType__) {\n        return (ɵMatFooterRow_BaseFactory || (ɵMatFooterRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterRow)))(__ngFactoryType__ || MatFooterRow);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatFooterRow,\n      selectors: [[\"mat-footer-row\"], [\"tr\", \"mat-footer-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-footer-row\", \"mdc-data-table__row\"],\n      exportAs: [\"matFooterRow\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkFooterRow,\n        useExisting: MatFooterRow\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function MatFooterRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n  return MatFooterRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nlet MatRow = /*#__PURE__*/(() => {\n  class MatRow extends CdkRow {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatRow_BaseFactory;\n      return function MatRow_Factory(__ngFactoryType__) {\n        return (ɵMatRow_BaseFactory || (ɵMatRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatRow)))(__ngFactoryType__ || MatRow);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatRow,\n      selectors: [[\"mat-row\"], [\"tr\", \"mat-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-row\", \"mdc-data-table__row\"],\n      exportAs: [\"matRow\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkRow,\n        useExisting: MatRow\n      }]), i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function MatRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n  return MatRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nlet MatNoDataRow = /*#__PURE__*/(() => {\n  class MatNoDataRow extends CdkNoDataRow {\n    _contentClassName = 'mat-mdc-no-data-row';\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatNoDataRow_BaseFactory;\n      return function MatNoDataRow_Factory(__ngFactoryType__) {\n        return (ɵMatNoDataRow_BaseFactory || (ɵMatNoDataRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatNoDataRow)))(__ngFactoryType__ || MatNoDataRow);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatNoDataRow,\n      selectors: [[\"ng-template\", \"matNoDataRow\", \"\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkNoDataRow,\n        useExisting: MatNoDataRow\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatNoDataRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nlet MatTextColumn = /*#__PURE__*/(() => {\n  class MatTextColumn extends CdkTextColumn {\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatTextColumn_BaseFactory;\n      return function MatTextColumn_Factory(__ngFactoryType__) {\n        return (ɵMatTextColumn_BaseFactory || (ɵMatTextColumn_BaseFactory = i0.ɵɵgetInheritedFactory(MatTextColumn)))(__ngFactoryType__ || MatTextColumn);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTextColumn,\n      selectors: [[\"mat-text-column\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 3,\n      vars: 0,\n      consts: [[\"matColumnDef\", \"\"], [\"mat-header-cell\", \"\", 3, \"text-align\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 3, \"text-align\", 4, \"matCellDef\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"]],\n      template: function MatTextColumn_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainerStart(0, 0);\n          i0.ɵɵtemplate(1, MatTextColumn_th_1_Template, 2, 3, \"th\", 1)(2, MatTextColumn_td_2_Template, 2, 3, \"td\", 2);\n          i0.ɵɵelementContainerEnd();\n        }\n      },\n      dependencies: [MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell],\n      encapsulation: 2\n    });\n  }\n  return MatTextColumn;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst EXPORTED_DECLARATIONS = [\n// Table\nMatTable, MatRecycleRows,\n// Template defs\nMatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatFooterCellDef, MatFooterRowDef,\n// Cell directives\nMatHeaderCell, MatCell, MatFooterCell,\n// Row directives\nMatHeaderRow, MatRow, MatFooterRow, MatNoDataRow, MatTextColumn];\nlet MatTableModule = /*#__PURE__*/(() => {\n  class MatTableModule {\n    static ɵfac = function MatTableModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatTableModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatTableModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CdkTableModule, MatCommonModule]\n    });\n  }\n  return MatTableModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nclass MatTableDataSource extends DataSource {\n  /** Stream that emits when a new data array is set on the data source. */\n  _data;\n  /** Stream emitting render data to the table (depends on ordered data changes). */\n  _renderData = /*#__PURE__*/new BehaviorSubject([]);\n  /** Stream that emits when a new filter string is set on the data source. */\n  _filter = /*#__PURE__*/new BehaviorSubject('');\n  /** Used to react to internal changes of the paginator that are made by the data source itself. */\n  _internalPageChanges = /*#__PURE__*/new Subject();\n  /**\n   * Subscription to the changes that should trigger an update to the table's rendered rows, such\n   * as filtering, sorting, pagination, or base data changes.\n   */\n  _renderChangesSubscription = null;\n  /**\n   * The filtered set of data that has been matched by the filter string, or all the data if there\n   * is no filter. Useful for knowing the set of data the table represents.\n   * For example, a 'selectAll()' function would likely want to select the set of filtered data\n   * shown to the user rather than all the data.\n   */\n  filteredData;\n  /** Array of data that should be rendered by the table, where each object represents one row. */\n  get data() {\n    return this._data.value;\n  }\n  set data(data) {\n    data = Array.isArray(data) ? data : [];\n    this._data.next(data);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(data);\n    }\n  }\n  /**\n   * Filter term that should be used to filter out objects from the data array. To override how\n   * data objects match to this filter string, provide a custom function for filterPredicate.\n   */\n  get filter() {\n    return this._filter.value;\n  }\n  set filter(filter) {\n    this._filter.next(filter);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(this.data);\n    }\n  }\n  /**\n   * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n   * emitted by the MatSort will trigger an update to the table's rendered data.\n   */\n  get sort() {\n    return this._sort;\n  }\n  set sort(sort) {\n    this._sort = sort;\n    this._updateChangeSubscription();\n  }\n  _sort;\n  /**\n   * Instance of the paginator component used by the table to control what page of the data is\n   * displayed. Page changes emitted by the paginator will trigger an update to the\n   * table's rendered data.\n   *\n   * Note that the data source uses the paginator's properties to calculate which page of data\n   * should be displayed. If the paginator receives its properties as template inputs,\n   * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n   * initialized before assigning it to this data source.\n   */\n  get paginator() {\n    return this._paginator;\n  }\n  set paginator(paginator) {\n    this._paginator = paginator;\n    this._updateChangeSubscription();\n  }\n  _paginator;\n  /**\n   * Data accessor function that is used for accessing data properties for sorting through\n   * the default sortData function.\n   * This default function assumes that the sort header IDs (which defaults to the column name)\n   * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n   * May be set to a custom function for different behavior.\n   * @param data Data object that is being accessed.\n   * @param sortHeaderId The name of the column that represents the data.\n   */\n  sortingDataAccessor = (data, sortHeaderId) => {\n    const value = data[sortHeaderId];\n    if (_isNumberValue(value)) {\n      const numberValue = Number(value);\n      // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we leave them as strings.\n      // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER\n      return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n    }\n    return value;\n  };\n  /**\n   * Gets a sorted copy of the data array based on the state of the MatSort. Called\n   * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n   * By default, the function retrieves the active sort and its direction and compares data\n   * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n   * of data ordering.\n   * @param data The array of data that should be sorted.\n   * @param sort The connected MatSort that holds the current sort state.\n   */\n  sortData = (data, sort) => {\n    const active = sort.active;\n    const direction = sort.direction;\n    if (!active || direction == '') {\n      return data;\n    }\n    return data.sort((a, b) => {\n      let valueA = this.sortingDataAccessor(a, active);\n      let valueB = this.sortingDataAccessor(b, active);\n      // If there are data in the column that can be converted to a number,\n      // it must be ensured that the rest of the data\n      // is of the same type so as not to order incorrectly.\n      const valueAType = typeof valueA;\n      const valueBType = typeof valueB;\n      if (valueAType !== valueBType) {\n        if (valueAType === 'number') {\n          valueA += '';\n        }\n        if (valueBType === 'number') {\n          valueB += '';\n        }\n      }\n      // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n      // one value exists while the other doesn't. In this case, existing value should come last.\n      // This avoids inconsistent results when comparing values to undefined/null.\n      // If neither value exists, return 0 (equal).\n      let comparatorResult = 0;\n      if (valueA != null && valueB != null) {\n        // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n        if (valueA > valueB) {\n          comparatorResult = 1;\n        } else if (valueA < valueB) {\n          comparatorResult = -1;\n        }\n      } else if (valueA != null) {\n        comparatorResult = 1;\n      } else if (valueB != null) {\n        comparatorResult = -1;\n      }\n      return comparatorResult * (direction == 'asc' ? 1 : -1);\n    });\n  };\n  /**\n   * Checks if a data object matches the data source's filter string. By default, each data object\n   * is converted to a string of its properties and returns true if the filter has\n   * at least one occurrence in that string. By default, the filter string has its whitespace\n   * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n   * filter matching.\n   * @param data Data object used to check against the filter.\n   * @param filter Filter string that has been set on the data source.\n   * @returns Whether the filter matches against the data\n   */\n  filterPredicate = (data, filter) => {\n    // Transform the filter by converting it to lowercase and removing whitespace.\n    const transformedFilter = filter.trim().toLowerCase();\n    // Loops over the values in the array and returns true if any of them match the filter string\n    return Object.values(data).some(value => `${value}`.toLowerCase().includes(transformedFilter));\n  };\n  constructor(initialData = []) {\n    super();\n    this._data = new BehaviorSubject(initialData);\n    this._updateChangeSubscription();\n  }\n  /**\n   * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n   * changes occur, process the current state of the filter, sort, and pagination along with\n   * the provided base data and send it to the table for rendering.\n   */\n  _updateChangeSubscription() {\n    // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n    // The events should emit whenever the component emits a change or initializes, or if no\n    // component is provided, a stream with just a null event should be provided.\n    // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n    // pipeline can progress to the next step. Note that the value from these streams are not used,\n    // they purely act as a signal to progress in the pipeline.\n    const sortChange = this._sort ? merge(this._sort.sortChange, this._sort.initialized) : of(null);\n    const pageChange = this._paginator ? merge(this._paginator.page, this._internalPageChanges, this._paginator.initialized) : of(null);\n    const dataStream = this._data;\n    // Watch for base data or filter changes to provide a filtered set of data.\n    const filteredData = combineLatest([dataStream, this._filter]).pipe(map(([data]) => this._filterData(data)));\n    // Watch for filtered data or sort changes to provide an ordered set of data.\n    const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n    // Watch for ordered data or page changes to provide a paged set of data.\n    const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n    // Watched for paged data changes and send the result to the table to render.\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n  }\n  /**\n   * Returns a filtered data array where each filter object contains the filter string within\n   * the result of the filterPredicate function. If no filter is set, returns the data array\n   * as provided.\n   */\n  _filterData(data) {\n    // If there is a filter string, filter out data that does not contain it.\n    // Each data object is converted to a string using the function defined by filterPredicate.\n    // May be overridden for customization.\n    this.filteredData = this.filter == null || this.filter === '' ? data : data.filter(obj => this.filterPredicate(obj, this.filter));\n    if (this.paginator) {\n      this._updatePaginator(this.filteredData.length);\n    }\n    return this.filteredData;\n  }\n  /**\n   * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n   * data array as provided. Uses the default data accessor for data lookup, unless a\n   * sortDataAccessor function is defined.\n   */\n  _orderData(data) {\n    // If there is no active sort or direction, return the data without trying to sort.\n    if (!this.sort) {\n      return data;\n    }\n    return this.sortData(data.slice(), this.sort);\n  }\n  /**\n   * Returns a paged slice of the provided data array according to the provided paginator's page\n   * index and length. If there is no paginator provided, returns the data array as provided.\n   */\n  _pageData(data) {\n    if (!this.paginator) {\n      return data;\n    }\n    const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n    return data.slice(startIndex, startIndex + this.paginator.pageSize);\n  }\n  /**\n   * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n   * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n   * guard against making property changes within a round of change detection.\n   */\n  _updatePaginator(filteredDataLength) {\n    Promise.resolve().then(() => {\n      const paginator = this.paginator;\n      if (!paginator) {\n        return;\n      }\n      paginator.length = filteredDataLength;\n      // If the page index is set beyond the page, reduce it to the last page.\n      if (paginator.pageIndex > 0) {\n        const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n        const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n        if (newPageIndex !== paginator.pageIndex) {\n          paginator.pageIndex = newPageIndex;\n          // Since the paginator only emits after user-generated changes,\n          // we need our own stream so we know to should re-render the data.\n          this._internalPageChanges.next();\n        }\n      }\n    });\n  }\n  /**\n   * Used by the MatTable. Called when it connects to the data source.\n   * @docs-private\n   */\n  connect() {\n    if (!this._renderChangesSubscription) {\n      this._updateChangeSubscription();\n    }\n    return this._renderData;\n  }\n  /**\n   * Used by the MatTable. Called when it disconnects from the data source.\n   * @docs-private\n   */\n  disconnect() {\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = null;\n  }\n}\nexport { MatCell, MatCellDef, MatColumnDef, MatFooterCell, MatFooterCellDef, MatFooterRow, MatFooterRowDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatNoDataRow, MatRecycleRows, MatRow, MatRowDef, MatTable, MatTableDataSource, MatTableModule, MatTextColumn };\n//# sourceMappingURL=table.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}