{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function pairwise() {\n  return operate((source, subscriber) => {\n    let prev;\n    let hasPrev = false;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const p = prev;\n      prev = value;\n      hasPrev && subscriber.next([p, value]);\n      hasPrev = true;\n    }));\n  });\n}\n//# sourceMappingURL=pairwise.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}