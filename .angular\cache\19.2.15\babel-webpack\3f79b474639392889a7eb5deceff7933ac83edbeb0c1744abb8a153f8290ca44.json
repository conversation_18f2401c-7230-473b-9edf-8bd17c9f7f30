{"ast": null, "code": "import { ObserversModule } from '@angular/cdk/observers';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { j as <PERSON><PERSON><PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON><PERSON>, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-DqPi4knt.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nlet MatFormFieldModule = /*#__PURE__*/(() => {\n  class MatFormFieldModule {\n    static ɵfac = function MatFormFieldModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatFormFieldModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatFormFieldModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, ObserversModule, MatCommonModule]\n    });\n  }\n  return MatFormFieldModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatFormFieldModule as M };\n//# sourceMappingURL=module-BXZhw7pQ.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}