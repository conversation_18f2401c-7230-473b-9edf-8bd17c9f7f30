{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport let FooterComponent = /*#__PURE__*/(() => {\n  class FooterComponent {\n    constructor() {\n      this.currentYear = new Date().getFullYear();\n    }\n    static {\n      this.ɵfac = function FooterComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FooterComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FooterComponent,\n        selectors: [[\"app-footer\"]],\n        decls: 4,\n        vars: 1,\n        consts: [[1, \"app-footer\"], [1, \"footer-content\"]],\n        template: function FooterComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"p\");\n            i0.ɵɵtext(3);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\\u00A9 \", ctx.currentYear, \" HRMS Promotion System. All rights reserved.\");\n          }\n        },\n        dependencies: [CommonModule],\n        styles: [\".app-footer[_ngcontent-%COMP%]{background-color:#283163;color:#fff;padding:20px 0;margin-top:auto}.footer-content[_ngcontent-%COMP%]{text-align:center;padding:0 20px}.footer-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px}\"]\n      });\n    }\n  }\n  return FooterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}