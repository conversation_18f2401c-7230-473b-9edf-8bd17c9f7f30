{"ast": null, "code": "import { _ as _resolveDirectionality, D as Directionality } from './directionality-CBXD4hga.mjs';\nexport { a as DIR_DOCUMENT } from './directionality-CBXD4hga.mjs';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport '@angular/common';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nlet Dir = /*#__PURE__*/(() => {\n  class Dir {\n    /** Normalized direction that accounts for invalid/unsupported values. */\n    _dir = 'ltr';\n    /** Whether the `value` has been set to its initial value. */\n    _isInitialized = false;\n    /** Direction as passed in by the consumer. */\n    _rawDir;\n    /** Event emitted when the direction changes. */\n    change = new EventEmitter();\n    /** @docs-private */\n    get dir() {\n      return this._dir;\n    }\n    set dir(value) {\n      const previousValue = this._dir;\n      // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n      // whereas the browser does it based on the content of the element. Since doing so based\n      // on the content can be expensive, for now we're doing the simpler matching.\n      this._dir = _resolveDirectionality(value);\n      this._rawDir = value;\n      if (previousValue !== this._dir && this._isInitialized) {\n        this.change.emit(this._dir);\n      }\n    }\n    /** Current layout direction of the element. */\n    get value() {\n      return this.dir;\n    }\n    /** Initialize once default value has been set. */\n    ngAfterContentInit() {\n      this._isInitialized = true;\n    }\n    ngOnDestroy() {\n      this.change.complete();\n    }\n    static ɵfac = function Dir_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Dir)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: Dir,\n      selectors: [[\"\", \"dir\", \"\"]],\n      hostVars: 1,\n      hostBindings: function Dir_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"dir\", ctx._rawDir);\n        }\n      },\n      inputs: {\n        dir: \"dir\"\n      },\n      outputs: {\n        change: \"dirChange\"\n      },\n      exportAs: [\"dir\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: Directionality,\n        useExisting: Dir\n      }])]\n    });\n  }\n  return Dir;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BidiModule = /*#__PURE__*/(() => {\n  class BidiModule {\n    static ɵfac = function BidiModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BidiModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BidiModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return BidiModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { BidiModule, Dir, Directionality };\n//# sourceMappingURL=bidi.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}