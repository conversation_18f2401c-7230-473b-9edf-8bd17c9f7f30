{"ast": null, "code": "import { InjectionToken } from '@angular/core';\n\n/**\n * This token is used to inject the object whose value should be set into `MatInput`. If none is\n * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide\n * themselves for this token, in order to make `MatInput` delegate the getting and setting of the\n * value to them.\n */\nconst MAT_INPUT_VALUE_ACCESSOR = /*#__PURE__*/new InjectionToken('MAT_INPUT_VALUE_ACCESSOR');\nexport { MAT_INPUT_VALUE_ACCESSOR as M };\n//# sourceMappingURL=input-value-accessor-D1GvPuqO.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}