{"ast": null, "code": "export const intervalProvider = {\n  setInterval(handler, timeout, ...args) {\n    const {\n      delegate\n    } = intervalProvider;\n    if (delegate === null || delegate === void 0 ? void 0 : delegate.setInterval) {\n      return delegate.setInterval(handler, timeout, ...args);\n    }\n    return setInterval(handler, timeout, ...args);\n  },\n  clearInterval(handle) {\n    const {\n      delegate\n    } = intervalProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearInterval) || clearInterval)(handle);\n  },\n  delegate: undefined\n};\n//# sourceMappingURL=intervalProvider.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}