{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    constructor(router) {\n      this.router = router;\n    }\n    canActivate() {\n      // Implementation for authentication check\n      const isAuthenticated = this.checkAuthentication();\n      if (!isAuthenticated) {\n        this.router.navigate(['/login']);\n        return false;\n      }\n      return true;\n    }\n    checkAuthentication() {\n      // Check if user is authenticated\n      // This could check localStorage, sessionStorage, or call an auth service\n      return localStorage.getItem('authToken') !== null;\n    }\n    static {\n      this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthGuard,\n        factory: AuthGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}