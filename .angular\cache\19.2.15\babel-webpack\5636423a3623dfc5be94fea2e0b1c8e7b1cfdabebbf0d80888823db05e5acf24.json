{"ast": null, "code": "import { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { refCount as higherOrderRefCount } from '../operators/refCount';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { hasLift } from '../util/lift';\nexport class ConnectableObservable extends Observable {\n  constructor(source, subjectFactory) {\n    super();\n    this.source = source;\n    this.subjectFactory = subjectFactory;\n    this._subject = null;\n    this._refCount = 0;\n    this._connection = null;\n    if (hasLift(source)) {\n      this.lift = source.lift;\n    }\n  }\n  _subscribe(subscriber) {\n    return this.getSubject().subscribe(subscriber);\n  }\n  getSubject() {\n    const subject = this._subject;\n    if (!subject || subject.isStopped) {\n      this._subject = this.subjectFactory();\n    }\n    return this._subject;\n  }\n  _teardown() {\n    this._refCount = 0;\n    const {\n      _connection\n    } = this;\n    this._subject = this._connection = null;\n    _connection === null || _connection === void 0 ? void 0 : _connection.unsubscribe();\n  }\n  connect() {\n    let connection = this._connection;\n    if (!connection) {\n      connection = this._connection = new Subscription();\n      const subject = this.getSubject();\n      connection.add(this.source.subscribe(createOperatorSubscriber(subject, undefined, () => {\n        this._teardown();\n        subject.complete();\n      }, err => {\n        this._teardown();\n        subject.error(err);\n      }, () => this._teardown())));\n      if (connection.closed) {\n        this._connection = null;\n        connection = Subscription.EMPTY;\n      }\n    }\n    return connection;\n  }\n  refCount() {\n    return higherOrderRefCount()(this);\n  }\n}\n//# sourceMappingURL=ConnectableObservable.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}