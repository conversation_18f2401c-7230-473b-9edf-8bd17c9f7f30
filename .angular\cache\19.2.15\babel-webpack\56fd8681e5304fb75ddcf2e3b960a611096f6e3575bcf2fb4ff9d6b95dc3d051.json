{"ast": null, "code": "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { InjectionToken } from '@angular/core';\n\n/**\n * A DI Token representing the main rendering context.\n * In a browser and SSR this is the DOM Document.\n * When using SSR, that document is created by [<PERSON><PERSON>](https://github.com/angular/domino).\n *\n * @publicApi\n */\nconst DOCUMENT = /*#__PURE__*/new InjectionToken(ngDevMode ? 'DocumentToken' : '');\nexport { DOCUMENT };\n//# sourceMappingURL=dom_tokens-rA0ACyx7.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}