{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatIconModule } from '@angular/material/icon';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/promotion.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/select\";\nimport * as i9 from \"@angular/material/datepicker\";\nimport * as i10 from \"@angular/material/checkbox\";\nfunction PromotionRequestComponent_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Employee ID is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromotionRequestComponent_mat_error_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Employee Name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromotionRequestComponent_div_190_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function PromotionRequestComponent_div_190_div_4_Template_button_click_3_listener() {\n      const file_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.removeFile(file_r4));\n    });\n    i0.ɵɵtext(4, \" \\u2715 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r4.name);\n  }\n}\nfunction PromotionRequestComponent_div_190_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"h5\");\n    i0.ɵɵtext(2, \"Uploaded Files:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 72);\n    i0.ɵɵtemplate(4, PromotionRequestComponent_div_190_div_4_Template, 5, 1, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.uploadedFiles);\n  }\n}\nexport let PromotionRequestComponent = /*#__PURE__*/(() => {\n  class PromotionRequestComponent {\n    constructor(fb, promotionService) {\n      this.fb = fb;\n      this.promotionService = promotionService;\n      this.uploadedFiles = [];\n      this.promotionForm = this.fb.group({\n        employeeId: ['', Validators.required],\n        employeeName: ['', Validators.required],\n        currentDesignation: ['', Validators.required],\n        department: ['', Validators.required],\n        class: ['', Validators.required],\n        dateOfJoining: ['', Validators.required],\n        promotionType: ['', Validators.required],\n        proposedDesignation: ['', Validators.required],\n        panelType: ['', Validators.required],\n        crucialDate: ['', Validators.required],\n        drawalDate: ['', Validators.required],\n        serviceQualification: [false],\n        probationStatus: ['', Validators.required]\n      });\n    }\n    ngOnInit() {}\n    onFileSelected(event) {\n      const files = Array.from(event.target.files);\n      this.uploadedFiles.push(...files);\n    }\n    removeFile(file) {\n      const index = this.uploadedFiles.indexOf(file);\n      if (index > -1) {\n        this.uploadedFiles.splice(index, 1);\n      }\n    }\n    viewGuidelines() {\n      // Implementation for viewing guidelines\n      console.log('View Guidelines clicked');\n    }\n    onSubmit() {\n      if (this.promotionForm.valid) {\n        const formData = {\n          ...this.promotionForm.value,\n          supportingDocuments: this.uploadedFiles\n        };\n        this.promotionService.submitPromotionRequest(formData).subscribe({\n          next: response => {\n            console.log('Promotion Request Submitted Successfully:', response);\n            // Handle success (show notification, redirect, etc.)\n          },\n          error: error => {\n            console.error('Error submitting promotion request:', error);\n            // Handle error (show error message)\n          }\n        });\n      }\n    }\n    onCancel() {\n      this.promotionForm.reset();\n      this.uploadedFiles = [];\n    }\n    static {\n      this.ɵfac = function PromotionRequestComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PromotionRequestComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.PromotionService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PromotionRequestComponent,\n        selectors: [[\"app-promotion-request\"]],\n        decls: 196,\n        vars: 9,\n        consts: [[\"joiningPicker\", \"\"], [\"drawalPicker\", \"\"], [\"fileInput\", \"\"], [1, \"promotion-request-container\"], [1, \"header-section\"], [1, \"page-header\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"request-form-card\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-section\"], [1, \"form-row\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"employeeId\", \"placeholder\", \"Enter Employee ID\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"employeeName\", \"placeholder\", \"Enter Employee Name\"], [\"formControlName\", \"currentDesignation\"], [\"value\", \"\"], [\"value\", \"seasonal\"], [\"value\", \"junior-assistant\"], [\"value\", \"assistant\"], [\"value\", \"superintendent\"], [\"value\", \"assistant-manager\"], [\"formControlName\", \"department\"], [\"value\", \"administration\"], [\"value\", \"finance\"], [\"value\", \"operations\"], [\"value\", \"technical\"], [\"value\", \"hr\"], [\"formControlName\", \"class\"], [\"value\", \"I\"], [\"value\", \"II\"], [\"value\", \"III\"], [\"value\", \"IV\"], [\"matInput\", \"\", \"formControlName\", \"dateOfJoining\", 3, \"matDatepicker\"], [\"matIconSuffix\", \"\", 3, \"for\"], [\"formControlName\", \"promotionType\"], [\"value\", \"seasonal-to-regular\"], [\"value\", \"cadre-wise\"], [\"formControlName\", \"proposedDesignation\"], [\"value\", \"regular\"], [\"value\", \"deputy-manager\"], [\"formControlName\", \"panelType\"], [\"value\", \"dpc\"], [\"value\", \"selection\"], [\"value\", \"review\"], [\"formControlName\", \"crucialDate\"], [\"value\", \"1/6\"], [\"value\", \"1/7\"], [\"value\", \"1/8\"], [\"value\", \"1/9\"], [\"value\", \"1/10\"], [\"value\", \"1/11\"], [\"value\", \"1/12\"], [\"value\", \"1/1\"], [\"matInput\", \"\", \"formControlName\", \"drawalDate\", 3, \"matDatepicker\"], [1, \"checkbox-section\"], [\"formControlName\", \"serviceQualification\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"formControlName\", \"probationStatus\"], [\"value\", \"completed\"], [\"value\", \"extended\"], [\"value\", \"under-review\"], [\"value\", \"not-applicable\"], [1, \"file-upload-section\"], [1, \"file-upload-area\", 3, \"click\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \".pdf,.doc,.docx\", 2, \"display\", \"none\", 3, \"change\"], [1, \"upload-content\"], [\"class\", \"uploaded-files\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [1, \"uploaded-files\"], [1, \"file-list\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [\"mat-icon-button\", \"\", 3, \"click\"]],\n        template: function PromotionRequestComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"h1\");\n            i0.ɵɵtext(4, \"Promotion Request Initiation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Start the promotion process for eligible employees\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function PromotionRequestComponent_Template_button_click_7_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.viewGuidelines());\n            });\n            i0.ɵɵtext(8, \" View Guidelines \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"mat-card\", 7)(10, \"mat-card-header\")(11, \"mat-card-title\");\n            i0.ɵɵtext(12, \"Employee Information\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-card-subtitle\");\n            i0.ɵɵtext(14, \"Enter employee details for promotion consideration\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"mat-card-content\")(16, \"form\", 8);\n            i0.ɵɵlistener(\"ngSubmit\", function PromotionRequestComponent_Template_form_ngSubmit_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementStart(17, \"div\", 9)(18, \"h3\");\n            i0.ɵɵtext(19, \"Basic Information\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 10)(21, \"mat-form-field\", 11)(22, \"mat-label\");\n            i0.ɵɵtext(23, \"Employee ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(24, \"input\", 12);\n            i0.ɵɵtemplate(25, PromotionRequestComponent_mat_error_25_Template, 2, 0, \"mat-error\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"mat-form-field\", 11)(27, \"mat-label\");\n            i0.ɵɵtext(28, \"Employee Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(29, \"input\", 14);\n            i0.ɵɵtemplate(30, PromotionRequestComponent_mat_error_30_Template, 2, 0, \"mat-error\", 13);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 10)(32, \"mat-form-field\", 11)(33, \"mat-label\");\n            i0.ɵɵtext(34, \"Current Designation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"mat-select\", 15)(36, \"mat-option\", 16);\n            i0.ɵɵtext(37, \"Select Current Designation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"mat-option\", 17);\n            i0.ɵɵtext(39, \"Seasonal Employee\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"mat-option\", 18);\n            i0.ɵɵtext(41, \"Junior Assistant\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"mat-option\", 19);\n            i0.ɵɵtext(43, \"Assistant\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"mat-option\", 20);\n            i0.ɵɵtext(45, \"Superintendent\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"mat-option\", 21);\n            i0.ɵɵtext(47, \"Assistant Manager\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(48, \"mat-form-field\", 11)(49, \"mat-label\");\n            i0.ɵɵtext(50, \"Department\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"mat-select\", 22)(52, \"mat-option\", 16);\n            i0.ɵɵtext(53, \"Select Department\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"mat-option\", 23);\n            i0.ɵɵtext(55, \"Administration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"mat-option\", 24);\n            i0.ɵɵtext(57, \"Finance\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"mat-option\", 25);\n            i0.ɵɵtext(59, \"Operations\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"mat-option\", 26);\n            i0.ɵɵtext(61, \"Technical\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"mat-option\", 27);\n            i0.ɵɵtext(63, \"Human Resources\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(64, \"div\", 10)(65, \"mat-form-field\", 11)(66, \"mat-label\");\n            i0.ɵɵtext(67, \"Class\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(68, \"mat-select\", 28)(69, \"mat-option\", 16);\n            i0.ɵɵtext(70, \"Select Class\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"mat-option\", 29);\n            i0.ɵɵtext(72, \"Class I\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"mat-option\", 30);\n            i0.ɵɵtext(74, \"Class II\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(75, \"mat-option\", 31);\n            i0.ɵɵtext(76, \"Class III\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"mat-option\", 32);\n            i0.ɵɵtext(78, \"Class IV\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(79, \"mat-form-field\", 11)(80, \"mat-label\");\n            i0.ɵɵtext(81, \"Date of Joining\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(82, \"input\", 33)(83, \"mat-datepicker-toggle\", 34)(84, \"mat-datepicker\", null, 0);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(86, \"div\", 9)(87, \"h3\");\n            i0.ɵɵtext(88, \"Promotion Details\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"div\", 10)(90, \"mat-form-field\", 11)(91, \"mat-label\");\n            i0.ɵɵtext(92, \"Promotion Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"mat-select\", 35)(94, \"mat-option\", 16);\n            i0.ɵɵtext(95, \"Select Promotion Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(96, \"mat-option\", 36);\n            i0.ɵɵtext(97, \"Seasonal to Regular\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"mat-option\", 37);\n            i0.ɵɵtext(99, \"Cadre-wise Promotion\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(100, \"mat-form-field\", 11)(101, \"mat-label\");\n            i0.ɵɵtext(102, \"Proposed Designation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(103, \"mat-select\", 38)(104, \"mat-option\", 16);\n            i0.ɵɵtext(105, \"Select Proposed Designation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(106, \"mat-option\", 39);\n            i0.ɵɵtext(107, \"Regular Employee\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(108, \"mat-option\", 19);\n            i0.ɵɵtext(109, \"Assistant\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"mat-option\", 20);\n            i0.ɵɵtext(111, \"Superintendent\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(112, \"mat-option\", 21);\n            i0.ɵɵtext(113, \"Assistant Manager\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(114, \"mat-option\", 40);\n            i0.ɵɵtext(115, \"Deputy Manager\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(116, \"div\", 10)(117, \"mat-form-field\", 11)(118, \"mat-label\");\n            i0.ɵɵtext(119, \"Panel Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(120, \"mat-select\", 41)(121, \"mat-option\", 16);\n            i0.ɵɵtext(122, \"Select Panel Type\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(123, \"mat-option\", 42);\n            i0.ɵɵtext(124, \"Departmental Promotion Committee\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(125, \"mat-option\", 43);\n            i0.ɵɵtext(126, \"Selection Committee\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(127, \"mat-option\", 44);\n            i0.ɵɵtext(128, \"Review Committee\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(129, \"mat-form-field\", 11)(130, \"mat-label\");\n            i0.ɵɵtext(131, \"Crucial Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(132, \"mat-select\", 45)(133, \"mat-option\", 16);\n            i0.ɵɵtext(134, \"Select Crucial Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(135, \"mat-option\", 46);\n            i0.ɵɵtext(136, \"1/6\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(137, \"mat-option\", 47);\n            i0.ɵɵtext(138, \"1/7\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(139, \"mat-option\", 48);\n            i0.ɵɵtext(140, \"1/8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(141, \"mat-option\", 49);\n            i0.ɵɵtext(142, \"1/9\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(143, \"mat-option\", 50);\n            i0.ɵɵtext(144, \"1/10\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(145, \"mat-option\", 51);\n            i0.ɵɵtext(146, \"1/11\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(147, \"mat-option\", 52);\n            i0.ɵɵtext(148, \"1/12\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(149, \"mat-option\", 53);\n            i0.ɵɵtext(150, \"1/1\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(151, \"div\", 10)(152, \"mat-form-field\", 11)(153, \"mat-label\");\n            i0.ɵɵtext(154, \"Drawal Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(155, \"input\", 54)(156, \"mat-datepicker-toggle\", 34)(157, \"mat-datepicker\", null, 1);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(159, \"div\", 55)(160, \"mat-checkbox\", 56);\n            i0.ɵɵtext(161, \" Employee meets service qualification requirements \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(162, \"div\", 9)(163, \"h3\");\n            i0.ɵɵtext(164, \"Additional Information\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(165, \"mat-form-field\", 57)(166, \"mat-label\");\n            i0.ɵɵtext(167, \"Probation Declaration Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(168, \"mat-select\", 58)(169, \"mat-option\", 16);\n            i0.ɵɵtext(170, \"Select Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(171, \"mat-option\", 59);\n            i0.ɵɵtext(172, \"Successfully Completed\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(173, \"mat-option\", 60);\n            i0.ɵɵtext(174, \"Extended\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(175, \"mat-option\", 61);\n            i0.ɵɵtext(176, \"Under Review\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(177, \"mat-option\", 62);\n            i0.ɵɵtext(178, \"Not Applicable\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(179, \"div\", 63)(180, \"h4\");\n            i0.ɵɵtext(181, \"Upload Supporting Documents\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(182, \"div\", 64);\n            i0.ɵɵlistener(\"click\", function PromotionRequestComponent_Template_div_click_182_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const fileInput_r2 = i0.ɵɵreference(184);\n              return i0.ɵɵresetView(fileInput_r2.click());\n            });\n            i0.ɵɵelementStart(183, \"input\", 65, 2);\n            i0.ɵɵlistener(\"change\", function PromotionRequestComponent_Template_input_change_183_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileSelected($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(185, \"div\", 66)(186, \"p\");\n            i0.ɵɵtext(187, \"\\uD83D\\uDCC4 Click to upload or drag and drop\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(188, \"small\");\n            i0.ɵɵtext(189, \"PDF, DOC, DOCX files only\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(190, PromotionRequestComponent_div_190_Template, 5, 1, \"div\", 67);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(191, \"div\", 68)(192, \"button\", 69);\n            i0.ɵɵlistener(\"click\", function PromotionRequestComponent_Template_button_click_192_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onCancel());\n            });\n            i0.ɵɵtext(193, \"Cancel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(194, \"button\", 70);\n            i0.ɵɵtext(195, \" Initiate Promotion Process \");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            let tmp_4_0;\n            let tmp_5_0;\n            const joiningPicker_r6 = i0.ɵɵreference(85);\n            const drawalPicker_r7 = i0.ɵɵreference(158);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"formGroup\", ctx.promotionForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.promotionForm.get(\"employeeId\")) == null ? null : tmp_4_0.hasError(\"required\"));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.promotionForm.get(\"employeeName\")) == null ? null : tmp_5_0.hasError(\"required\"));\n            i0.ɵɵadvance(52);\n            i0.ɵɵproperty(\"matDatepicker\", joiningPicker_r6);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"for\", joiningPicker_r6);\n            i0.ɵɵadvance(72);\n            i0.ɵɵproperty(\"matDatepicker\", drawalPicker_r7);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"for\", drawalPicker_r7);\n            i0.ɵɵadvance(34);\n            i0.ɵɵproperty(\"ngIf\", ctx.uploadedFiles.length > 0);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.promotionForm.invalid);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, MatCardModule, i4.MatCard, i4.MatCardContent, i4.MatCardHeader, i4.MatCardSubtitle, i4.MatCardTitle, MatButtonModule, i5.MatButton, i5.MatIconButton, MatFormFieldModule, i6.MatFormField, i6.MatLabel, i6.MatError, i6.MatSuffix, MatInputModule, i7.MatInput, MatSelectModule, i8.MatSelect, i8.MatOption, MatDatepickerModule, i9.MatDatepicker, i9.MatDatepickerInput, i9.MatDatepickerToggle, MatNativeDateModule, MatCheckboxModule, i10.MatCheckbox, MatIconModule, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName, RouterModule],\n        styles: [\".promotion-request-container[_ngcontent-%COMP%]{padding:20px;background-color:#fff;min-height:100vh}.header-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:30px}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:28px;font-weight:600;color:#333}.page-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0 0;color:#666;font-size:16px}.request-form-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 20px #0000001a}.form-section[_ngcontent-%COMP%]{margin-bottom:30px;padding-bottom:20px;border-bottom:1px solid #e0e0e0}.form-section[_ngcontent-%COMP%]:last-child{border-bottom:none}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 20px;font-size:18px;font-weight:500;color:#333}.form-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:20px;margin-bottom:20px}.full-width[_ngcontent-%COMP%]{width:100%}.checkbox-section[_ngcontent-%COMP%]{display:flex;align-items:center;height:56px}.file-upload-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 15px;font-size:16px;font-weight:500;color:#333}.file-upload-area[_ngcontent-%COMP%]{border:2px dashed #e0e0e0;border-radius:8px;padding:30px;text-align:center;cursor:pointer;transition:all .3s ease;background-color:#fafafa}.file-upload-area[_ngcontent-%COMP%]:hover{border-color:#ccc;background-color:#f5f5f5}.upload-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 5px;font-size:16px;color:#666}.upload-content[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:#999}.uploaded-files[_ngcontent-%COMP%]{margin-top:20px}.uploaded-files[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 10px;font-size:14px;font-weight:500;color:#333}.file-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.file-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px 12px;background-color:#f5f5f5;border-radius:4px;font-size:14px}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:15px;margin-top:30px;padding-top:20px;border-top:1px solid #e0e0e0}@media (max-width: 768px){.form-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.header-section[_ngcontent-%COMP%]{flex-direction:column;gap:20px}}\"]\n      });\n    }\n  }\n  return PromotionRequestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}