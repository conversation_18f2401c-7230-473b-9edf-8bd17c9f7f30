{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideAnimationsAsync } from '@angular/platform-browser/animations/async';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { routes } from './app.routes';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideAnimationsAsync(), importProvidersFrom([MatToolbarModule, MatButtonModule, MatIconModule, MatCardModule, MatTableModule, MatPaginatorModule, MatSortModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatDialogModule, MatStepperModule, MatCheckboxModule, MatRadioModule, MatProgressBarModule, MatChipsModule, MatBadgeModule, MatMenuModule, MatSidenavModule, MatListModule, MatTabsModule, MatExpansionModule, MatSnackBarModule, ReactiveFormsModule, FormsModule])]\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}