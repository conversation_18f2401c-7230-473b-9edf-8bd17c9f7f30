{"ast": null, "code": "let nextHandle = 1;\nlet resolved;\nconst activeHandles = {};\nfunction findAndClearHandle(handle) {\n  if (handle in activeHandles) {\n    delete activeHandles[handle];\n    return true;\n  }\n  return false;\n}\nexport const Immediate = {\n  setImmediate(cb) {\n    const handle = nextHandle++;\n    activeHandles[handle] = true;\n    if (!resolved) {\n      resolved = Promise.resolve();\n    }\n    resolved.then(() => findAndClearHandle(handle) && cb());\n    return handle;\n  },\n  clearImmediate(handle) {\n    findAndClearHandle(handle);\n  }\n};\nexport const TestTools = {\n  pending() {\n    return Object.keys(activeHandles).length;\n  }\n};\n//# sourceMappingURL=Immediate.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}