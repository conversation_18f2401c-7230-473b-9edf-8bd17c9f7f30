{"ast": null, "code": "import { Overlay, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, Renderer2, ANIMATION_MODULE_TYPE, EventEmitter, HostAttributeToken, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, Input, ViewChild, Output, Directive, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _IdGenerator, LiveAnnouncer, removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { hasModifier<PERSON>ey, ENTER, SPACE, A, ESCAPE, DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW } from '@angular/cdk/keycodes';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, filter, map, takeUntil, take } from 'rxjs/operators';\nimport { NgClass } from '@angular/common';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-DqPi4knt.mjs';\nimport { _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition, c as MAT_OPTION_PARENT_COMPONENT, M as MatOption, d as MAT_OPTGROUP } from './option-ChV6uQgD.mjs';\nimport { E as ErrorStateMatcher } from './error-options-Dm2JJUbF.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatOptionModule } from './index-DOxJc1m4.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatFormFieldModule } from './module-BXZhw7pQ.mjs';\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nconst _c0 = [\"trigger\"];\nconst _c1 = [\"panel\"];\nconst _c2 = [[[\"mat-select-trigger\"]], \"*\"];\nconst _c3 = [\"mat-select-trigger\", \"*\"];\nfunction MatSelect_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.placeholder);\n  }\n}\nfunction MatSelect_Conditional_5_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction MatSelect_Conditional_5_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.triggerValue);\n  }\n}\nfunction MatSelect_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtemplate(1, MatSelect_Conditional_5_Conditional_1_Template, 1, 0)(2, MatSelect_Conditional_5_Conditional_2_Template, 2, 1, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.customTrigger ? 1 : 2);\n  }\n}\nfunction MatSelect_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 1);\n    i0.ɵɵlistener(\"keydown\", function MatSelect_ng_template_10_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    });\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open \", ctx_r1._getPanelTheme(), \"\");\n    i0.ɵɵclassProp(\"mat-select-panel-animations-enabled\", !ctx_r1._animationsDisabled);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.panelClass);\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"-panel\")(\"aria-multiselectable\", ctx_r1.multiple)(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1._getPanelAriaLabelledby());\n  }\n}\nfunction getMatSelectDynamicMultipleError() {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n  return Error('`compareWith` must be a function.');\n}\n\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('mat-select-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = /*#__PURE__*/new InjectionToken('MAT_SELECT_CONFIG');\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = /*#__PURE__*/new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n  source;\n  value;\n  constructor(/** Reference to the select that emitted the change event. */\n  source, /** Current value of the select that emitted the event. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\nlet MatSelect = /*#__PURE__*/(() => {\n  class MatSelect {\n    _viewportRuler = inject(ViewportRuler);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _dir = inject(Directionality, {\n      optional: true\n    });\n    _idGenerator = inject(_IdGenerator);\n    _renderer = inject(Renderer2);\n    _parentFormField = inject(MAT_FORM_FIELD, {\n      optional: true\n    });\n    ngControl = inject(NgControl, {\n      self: true,\n      optional: true\n    });\n    _liveAnnouncer = inject(LiveAnnouncer);\n    _defaultOptions = inject(MAT_SELECT_CONFIG, {\n      optional: true\n    });\n    _animationsDisabled = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }) === 'NoopAnimations';\n    _initialized = new Subject();\n    _cleanupDetach;\n    /** All of the defined select options. */\n    options;\n    // TODO(crisbeto): this is only necessary for the non-MDC select, but it's technically a\n    // public API so we have to keep it. It should be deprecated and removed eventually.\n    /** All of the defined groups of options. */\n    optionGroups;\n    /** User-supplied override of the trigger element. */\n    customTrigger;\n    /**\n     * This position config ensures that the top \"start\" corner of the overlay\n     * is aligned with with the top \"start\" of the origin by default (overlapping\n     * the trigger completely). If the panel cannot fit below the trigger, it\n     * will fall back to a position above the trigger.\n     */\n    _positions = [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    }, {\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }, {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass: 'mat-mdc-select-panel-above'\n    }];\n    /** Scrolls a particular option into the view. */\n    _scrollOptionIntoView(index) {\n      const option = this.options.toArray()[index];\n      if (option) {\n        const panel = this.panel.nativeElement;\n        const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n        const element = option._getHostElement();\n        if (index === 0 && labelCount === 1) {\n          // If we've got one group label before the option and we're at the top option,\n          // scroll the list to the top. This is better UX than scrolling the list to the\n          // top of the option, because it allows the user to read the top group's label.\n          panel.scrollTop = 0;\n        } else {\n          panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n        }\n      }\n    }\n    /** Called when the panel has been opened and the overlay has settled on its final position. */\n    _positioningSettled() {\n      this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n    }\n    /** Creates a change event object that should be emitted by the select. */\n    _getChangeEvent(value) {\n      return new MatSelectChange(this, value);\n    }\n    /** Factory function used to create a scroll strategy for this select. */\n    _scrollStrategyFactory = inject(MAT_SELECT_SCROLL_STRATEGY);\n    /** Whether or not the overlay panel is open. */\n    _panelOpen = false;\n    /** Comparison function to specify which option is displayed. Defaults to object equality. */\n    _compareWith = (o1, o2) => o1 === o2;\n    /** Unique id for this input. */\n    _uid = this._idGenerator.getId('mat-select-');\n    /** Current `aria-labelledby` value for the select trigger. */\n    _triggerAriaLabelledBy = null;\n    /**\n     * Keeps track of the previous form control assigned to the select.\n     * Used to detect if it has changed.\n     */\n    _previousControl;\n    /** Emits whenever the component is destroyed. */\n    _destroy = new Subject();\n    /** Tracks the error state of the select. */\n    _errorStateTracker;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    stateChanges = new Subject();\n    /**\n     * Disable the automatic labeling to avoid issues like #27241.\n     * @docs-private\n     */\n    disableAutomaticLabeling = true;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    userAriaDescribedBy;\n    /** Deals with the selection logic. */\n    _selectionModel;\n    /** Manages keyboard events for options in the panel. */\n    _keyManager;\n    /** Ideal origin for the overlay panel. */\n    _preferredOverlayOrigin;\n    /** Width of the overlay panel. */\n    _overlayWidth;\n    /** `View -> model callback called when value changes` */\n    _onChange = () => {};\n    /** `View -> model callback called when select has been touched` */\n    _onTouched = () => {};\n    /** ID for the DOM node containing the select's value. */\n    _valueId = this._idGenerator.getId('mat-select-value-');\n    /** Strategy that will be used to handle scrolling while the select panel is open. */\n    _scrollStrategy;\n    _overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n    /** Whether the select is focused. */\n    get focused() {\n      return this._focused || this._panelOpen;\n    }\n    _focused = false;\n    /** A name for this control that can be used by `mat-form-field`. */\n    controlType = 'mat-select';\n    /** Trigger that opens the select. */\n    trigger;\n    /** Panel containing the select options. */\n    panel;\n    /** Overlay pane containing the options. */\n    _overlayDir;\n    /** Classes to be passed to the select panel. Supports the same syntax as `ngClass`. */\n    panelClass;\n    /** Whether the select is disabled. */\n    disabled = false;\n    /** Whether ripples in the select are disabled. */\n    disableRipple = false;\n    /** Tab index of the select. */\n    tabIndex = 0;\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n      return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n      this._hideSingleSelectionIndicator = value;\n      this._syncParentProperties();\n    }\n    _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /** Placeholder to be shown if no value has been selected. */\n    get placeholder() {\n      return this._placeholder;\n    }\n    set placeholder(value) {\n      this._placeholder = value;\n      this.stateChanges.next();\n    }\n    _placeholder;\n    /** Whether the component is required. */\n    get required() {\n      return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n      this._required = value;\n      this.stateChanges.next();\n    }\n    _required;\n    /** Whether the user should be allowed to select multiple options. */\n    get multiple() {\n      return this._multiple;\n    }\n    set multiple(value) {\n      if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectDynamicMultipleError();\n      }\n      this._multiple = value;\n    }\n    _multiple = false;\n    /** Whether to center the active option over the trigger. */\n    disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n    /**\n     * Function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n      return this._compareWith;\n    }\n    set compareWith(fn) {\n      if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonFunctionValueError();\n      }\n      this._compareWith = fn;\n      if (this._selectionModel) {\n        // A different comparator means the selection could change.\n        this._initializeSelection();\n      }\n    }\n    /** Value of the select control. */\n    get value() {\n      return this._value;\n    }\n    set value(newValue) {\n      const hasAssigned = this._assignValue(newValue);\n      if (hasAssigned) {\n        this._onChange(newValue);\n      }\n    }\n    _value;\n    /** Aria label of the select. */\n    ariaLabel = '';\n    /** Input that can be used to specify the `aria-labelledby` attribute. */\n    ariaLabelledby;\n    /** Object used to control when error messages are shown. */\n    get errorStateMatcher() {\n      return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n      this._errorStateTracker.matcher = value;\n    }\n    /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n    typeaheadDebounceInterval;\n    /**\n     * Function used to sort the values in a select in multiple mode.\n     * Follows the same logic as `Array.prototype.sort`.\n     */\n    sortComparator;\n    /** Unique id of the element. */\n    get id() {\n      return this._id;\n    }\n    set id(value) {\n      this._id = value || this._uid;\n      this.stateChanges.next();\n    }\n    _id;\n    /** Whether the select is in an error state. */\n    get errorState() {\n      return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n      this._errorStateTracker.errorState = value;\n    }\n    /**\n     * Width of the panel. If set to `auto`, the panel will match the trigger width.\n     * If set to null or an empty string, the panel will grow to match the longest option's text.\n     */\n    panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined' ? this._defaultOptions.panelWidth : 'auto';\n    /**\n     * By default selecting an option with a `null` or `undefined` value will reset the select's\n     * value. Enable this option if the reset behavior doesn't match your requirements and instead\n     * the nullable options should become selected. The value of this input can be controlled app-wide\n     * using the `MAT_SELECT_CONFIG` injection token.\n     */\n    canSelectNullableOptions = this._defaultOptions?.canSelectNullableOptions ?? false;\n    /** Combined stream of all of the child options' change events. */\n    optionSelectionChanges = defer(() => {\n      const options = this.options;\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      }\n      return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n    });\n    /** Event emitted when the select panel has been toggled. */\n    openedChange = new EventEmitter();\n    /** Event emitted when the select has been opened. */\n    _openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n    /** Event emitted when the select has been closed. */\n    _closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n    /** Event emitted when the selected value has been changed by the user. */\n    selectionChange = new EventEmitter();\n    /**\n     * Event that emits whenever the raw value of the select changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    valueChange = new EventEmitter();\n    constructor() {\n      const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n      const parentForm = inject(NgForm, {\n        optional: true\n      });\n      const parentFormGroup = inject(FormGroupDirective, {\n        optional: true\n      });\n      const tabIndex = inject(new HostAttributeToken('tabindex'), {\n        optional: true\n      });\n      if (this.ngControl) {\n        // Note: we provide the value accessor through here, instead of\n        // the `providers` to avoid running into a circular import.\n        this.ngControl.valueAccessor = this;\n      }\n      // Note that we only want to set this when the defaults pass it in, otherwise it should\n      // stay as `undefined` so that it falls back to the default in the key manager.\n      if (this._defaultOptions?.typeaheadDebounceInterval != null) {\n        this.typeaheadDebounceInterval = this._defaultOptions.typeaheadDebounceInterval;\n      }\n      this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n      this._scrollStrategy = this._scrollStrategyFactory();\n      this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n      // Force setter to be called in case id was not specified.\n      this.id = this.id;\n    }\n    ngOnInit() {\n      this._selectionModel = new SelectionModel(this.multiple);\n      this.stateChanges.next();\n      this._viewportRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => {\n        if (this.panelOpen) {\n          this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n          this._changeDetectorRef.detectChanges();\n        }\n      });\n    }\n    ngAfterContentInit() {\n      this._initialized.next();\n      this._initialized.complete();\n      this._initKeyManager();\n      this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n        event.added.forEach(option => option.select());\n        event.removed.forEach(option => option.deselect());\n      });\n      this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n        this._resetOptions();\n        this._initializeSelection();\n      });\n    }\n    ngDoCheck() {\n      const newAriaLabelledby = this._getTriggerAriaLabelledby();\n      const ngControl = this.ngControl;\n      // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n      // is computed as a result of a content query which can cause this binding to trigger a\n      // \"changed after checked\" error.\n      if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n        const element = this._elementRef.nativeElement;\n        this._triggerAriaLabelledBy = newAriaLabelledby;\n        if (newAriaLabelledby) {\n          element.setAttribute('aria-labelledby', newAriaLabelledby);\n        } else {\n          element.removeAttribute('aria-labelledby');\n        }\n      }\n      if (ngControl) {\n        // The disabled state might go out of sync if the form group is swapped out. See #17860.\n        if (this._previousControl !== ngControl.control) {\n          if (this._previousControl !== undefined && ngControl.disabled !== null && ngControl.disabled !== this.disabled) {\n            this.disabled = ngControl.disabled;\n          }\n          this._previousControl = ngControl.control;\n        }\n        this.updateErrorState();\n      }\n    }\n    ngOnChanges(changes) {\n      // Updating the disabled state is handled by the input, but we need to additionally let\n      // the parent form field know to run change detection when the disabled state changes.\n      if (changes['disabled'] || changes['userAriaDescribedBy']) {\n        this.stateChanges.next();\n      }\n      if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n        this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n      }\n    }\n    ngOnDestroy() {\n      this._cleanupDetach?.();\n      this._keyManager?.destroy();\n      this._destroy.next();\n      this._destroy.complete();\n      this.stateChanges.complete();\n      this._clearFromModal();\n    }\n    /** Toggles the overlay panel open or closed. */\n    toggle() {\n      this.panelOpen ? this.close() : this.open();\n    }\n    /** Opens the overlay panel. */\n    open() {\n      if (!this._canOpen()) {\n        return;\n      }\n      // It's important that we read this as late as possible, because doing so earlier will\n      // return a different element since it's based on queries in the form field which may\n      // not have run yet. Also this needs to be assigned before we measure the overlay width.\n      if (this._parentFormField) {\n        this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n      }\n      this._cleanupDetach?.();\n      this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n      this._applyModalPanelOwnership();\n      this._panelOpen = true;\n      this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n        this._changeDetectorRef.detectChanges();\n        this._positioningSettled();\n      });\n      this._overlayDir.attachOverlay();\n      this._keyManager.withHorizontalOrientation(null);\n      this._highlightCorrectOption();\n      this._changeDetectorRef.markForCheck();\n      // Required for the MDC form field to pick up when the overlay has been opened.\n      this.stateChanges.next();\n      // Simulate the animation event before we moved away from `@angular/animations`.\n      Promise.resolve().then(() => this.openedChange.emit(true));\n    }\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    _trackedModal = null;\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n      // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n      // the `LiveAnnouncer` and any other usages.\n      //\n      // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n      // section of the DOM we need to look through. This should cover all the cases we support, but\n      // the selector can be expanded if it turns out to be too narrow.\n      const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n      if (!modal) {\n        // Most commonly, the autocomplete trigger is not inside a modal.\n        return;\n      }\n      const panelId = `${this.id}-panel`;\n      if (this._trackedModal) {\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      }\n      addAriaReferencedId(modal, 'aria-owns', panelId);\n      this._trackedModal = modal;\n    }\n    /** Clears the reference to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n      if (!this._trackedModal) {\n        // Most commonly, the autocomplete trigger is not used inside a modal.\n        return;\n      }\n      const panelId = `${this.id}-panel`;\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      this._trackedModal = null;\n    }\n    /** Closes the overlay panel and focuses the host element. */\n    close() {\n      if (this._panelOpen) {\n        this._panelOpen = false;\n        this._exitAndDetach();\n        this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n        this._changeDetectorRef.markForCheck();\n        this._onTouched();\n        // Required for the MDC form field to pick up when the overlay has been closed.\n        this.stateChanges.next();\n        // Simulate the animation event before we moved away from `@angular/animations`.\n        Promise.resolve().then(() => this.openedChange.emit(false));\n      }\n    }\n    /** Triggers the exit animation and detaches the overlay at the end. */\n    _exitAndDetach() {\n      if (this._animationsDisabled || !this.panel) {\n        this._detachOverlay();\n        return;\n      }\n      this._cleanupDetach?.();\n      this._cleanupDetach = () => {\n        cleanupEvent();\n        clearTimeout(exitFallbackTimer);\n        this._cleanupDetach = undefined;\n      };\n      const panel = this.panel.nativeElement;\n      const cleanupEvent = this._renderer.listen(panel, 'animationend', event => {\n        if (event.animationName === '_mat-select-exit') {\n          this._cleanupDetach?.();\n          this._detachOverlay();\n        }\n      });\n      // Since closing the overlay depends on the animation, we have a fallback in case the panel\n      // doesn't animate. This can happen in some internal tests that do `* {animation: none}`.\n      const exitFallbackTimer = setTimeout(() => {\n        this._cleanupDetach?.();\n        this._detachOverlay();\n      }, 200);\n      panel.classList.add('mat-select-panel-exit');\n    }\n    /** Detaches the current overlay directive. */\n    _detachOverlay() {\n      this._overlayDir.detachOverlay();\n      // Some of the overlay detachment logic depends on change detection.\n      // Mark for check to ensure that things get picked up in a timely manner.\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Sets the select's value. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param value New value to be written to the model.\n     */\n    writeValue(value) {\n      this._assignValue(value);\n    }\n    /**\n     * Saves a callback function to be invoked when the select's value\n     * changes from user input. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the value changes.\n     */\n    registerOnChange(fn) {\n      this._onChange = fn;\n    }\n    /**\n     * Saves a callback function to be invoked when the select is blurred\n     * by the user. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the component has been touched.\n     */\n    registerOnTouched(fn) {\n      this._onTouched = fn;\n    }\n    /**\n     * Disables the select. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param isDisabled Sets whether the component is disabled.\n     */\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n    /** Whether or not the overlay panel is open. */\n    get panelOpen() {\n      return this._panelOpen;\n    }\n    /** The currently selected option. */\n    get selected() {\n      return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The value displayed in the trigger. */\n    get triggerValue() {\n      if (this.empty) {\n        return '';\n      }\n      if (this._multiple) {\n        const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n        if (this._isRtl()) {\n          selectedOptions.reverse();\n        }\n        // TODO(crisbeto): delimiter should be configurable for proper localization.\n        return selectedOptions.join(', ');\n      }\n      return this._selectionModel.selected[0].viewValue;\n    }\n    /** Refreshes the error state of the select. */\n    updateErrorState() {\n      this._errorStateTracker.updateErrorState();\n    }\n    /** Whether the element is in RTL mode. */\n    _isRtl() {\n      return this._dir ? this._dir.value === 'rtl' : false;\n    }\n    /** Handles all keydown events on the select. */\n    _handleKeydown(event) {\n      if (!this.disabled) {\n        this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n      }\n    }\n    /** Handles keyboard events while the select is closed. */\n    _handleClosedKeydown(event) {\n      const keyCode = event.keyCode;\n      const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW;\n      const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n      const manager = this._keyManager;\n      // Open the select on ALT + arrow key to match the native <select>\n      if (!manager.isTyping() && isOpenKey && !hasModifierKey(event) || (this.multiple || event.altKey) && isArrowKey) {\n        event.preventDefault(); // prevents the page from scrolling down when pressing space\n        this.open();\n      } else if (!this.multiple) {\n        const previouslySelectedOption = this.selected;\n        manager.onKeydown(event);\n        const selectedOption = this.selected;\n        // Since the value has changed, we need to announce it ourselves.\n        if (selectedOption && previouslySelectedOption !== selectedOption) {\n          // We set a duration on the live announcement, because we want the live element to be\n          // cleared after a while so that users can't navigate to it using the arrow keys.\n          this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n        }\n      }\n    }\n    /** Handles keyboard events when the selected is open. */\n    _handleOpenKeydown(event) {\n      const manager = this._keyManager;\n      const keyCode = event.keyCode;\n      const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n      const isTyping = manager.isTyping();\n      if (isArrowKey && event.altKey) {\n        // Close the select on ALT + arrow key to match the native <select>\n        event.preventDefault();\n        this.close();\n        // Don't do anything in this case if the user is typing,\n        // because the typing sequence can include the space key.\n      } else if (!isTyping && (keyCode === ENTER || keyCode === SPACE) && manager.activeItem && !hasModifierKey(event)) {\n        event.preventDefault();\n        manager.activeItem._selectViaInteraction();\n      } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n        event.preventDefault();\n        const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n        this.options.forEach(option => {\n          if (!option.disabled) {\n            hasDeselectedOptions ? option.select() : option.deselect();\n          }\n        });\n      } else {\n        const previouslyFocusedIndex = manager.activeItemIndex;\n        manager.onKeydown(event);\n        if (this._multiple && isArrowKey && event.shiftKey && manager.activeItem && manager.activeItemIndex !== previouslyFocusedIndex) {\n          manager.activeItem._selectViaInteraction();\n        }\n      }\n    }\n    /** Handles keyboard events coming from the overlay. */\n    _handleOverlayKeydown(event) {\n      // TODO(crisbeto): prior to #30363 this was being handled inside the overlay directive, but we\n      // need control over the animation timing so we do it manually. We should remove the `keydown`\n      // listener from `.mat-mdc-select-panel` and handle all the events here. That may cause\n      // further test breakages so it's left for a follow-up.\n      if (event.keyCode === ESCAPE && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close();\n      }\n    }\n    _onFocus() {\n      if (!this.disabled) {\n        this._focused = true;\n        this.stateChanges.next();\n      }\n    }\n    /**\n     * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n     * \"blur\" to the panel when it opens, causing a false positive.\n     */\n    _onBlur() {\n      this._focused = false;\n      this._keyManager?.cancelTypeahead();\n      if (!this.disabled && !this.panelOpen) {\n        this._onTouched();\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n      }\n    }\n    /** Returns the theme to be used on the panel. */\n    _getPanelTheme() {\n      return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n    }\n    /** Whether the select has a value. */\n    get empty() {\n      return !this._selectionModel || this._selectionModel.isEmpty();\n    }\n    _initializeSelection() {\n      // Defer setting the value in order to avoid the \"Expression\n      // has changed after it was checked\" errors from Angular.\n      Promise.resolve().then(() => {\n        if (this.ngControl) {\n          this._value = this.ngControl.value;\n        }\n        this._setSelectionByValue(this._value);\n        this.stateChanges.next();\n      });\n    }\n    /**\n     * Sets the selected option based on a value. If no option can be\n     * found with the designated value, the select trigger is cleared.\n     */\n    _setSelectionByValue(value) {\n      this.options.forEach(option => option.setInactiveStyles());\n      this._selectionModel.clear();\n      if (this.multiple && value) {\n        if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw getMatSelectNonArrayValueError();\n        }\n        value.forEach(currentValue => this._selectOptionByValue(currentValue));\n        this._sortValues();\n      } else {\n        const correspondingOption = this._selectOptionByValue(value);\n        // Shift focus to the active item. Note that we shouldn't do this in multiple\n        // mode, because we don't know what option the user interacted with last.\n        if (correspondingOption) {\n          this._keyManager.updateActiveItem(correspondingOption);\n        } else if (!this.panelOpen) {\n          // Otherwise reset the highlighted option. Note that we only want to do this while\n          // closed, because doing it while open can shift the user's focus unnecessarily.\n          this._keyManager.updateActiveItem(-1);\n        }\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Finds and selects and option based on its value.\n     * @returns Option that has the corresponding value.\n     */\n    _selectOptionByValue(value) {\n      const correspondingOption = this.options.find(option => {\n        // Skip options that are already in the model. This allows us to handle cases\n        // where the same primitive value is selected multiple times.\n        if (this._selectionModel.isSelected(option)) {\n          return false;\n        }\n        try {\n          // Treat null as a special reset value.\n          return (option.value != null || this.canSelectNullableOptions) && this._compareWith(option.value, value);\n        } catch (error) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Notify developers of errors in their comparator.\n            console.warn(error);\n          }\n          return false;\n        }\n      });\n      if (correspondingOption) {\n        this._selectionModel.select(correspondingOption);\n      }\n      return correspondingOption;\n    }\n    /** Assigns a specific value to the select. Returns whether the value has changed. */\n    _assignValue(newValue) {\n      // Always re-assign an array, because it might have been mutated.\n      if (newValue !== this._value || this._multiple && Array.isArray(newValue)) {\n        if (this.options) {\n          this._setSelectionByValue(newValue);\n        }\n        this._value = newValue;\n        return true;\n      }\n      return false;\n    }\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    _skipPredicate = option => {\n      if (this.panelOpen) {\n        // Support keyboard focusing disabled options in an ARIA listbox.\n        return false;\n      }\n      // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n      // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n      // closed.\n      return option.disabled;\n    };\n    /** Gets how wide the overlay panel should be. */\n    _getOverlayWidth(preferredOrigin) {\n      if (this.panelWidth === 'auto') {\n        const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin ? preferredOrigin.elementRef : preferredOrigin || this._elementRef;\n        return refToMeasure.nativeElement.getBoundingClientRect().width;\n      }\n      return this.panelWidth === null ? '' : this.panelWidth;\n    }\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n      if (this.options) {\n        for (const option of this.options) {\n          option._changeDetectorRef.markForCheck();\n        }\n      }\n    }\n    /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n    _initKeyManager() {\n      this._keyManager = new ActiveDescendantKeyManager(this.options).withTypeAhead(this.typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr').withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(['shiftKey']).skipPredicate(this._skipPredicate);\n      this._keyManager.tabOut.subscribe(() => {\n        if (this.panelOpen) {\n          // Select the active item when tabbing away. This is consistent with how the native\n          // select behaves. Note that we only want to do this in single selection mode.\n          if (!this.multiple && this._keyManager.activeItem) {\n            this._keyManager.activeItem._selectViaInteraction();\n          }\n          // Restore focus to the trigger before closing. Ensures that the focus\n          // position won't be lost if the user got focus into the overlay.\n          this.focus();\n          this.close();\n        }\n      });\n      this._keyManager.change.subscribe(() => {\n        if (this._panelOpen && this.panel) {\n          this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n        } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        }\n      });\n    }\n    /** Drops current option subscriptions and IDs and resets from scratch. */\n    _resetOptions() {\n      const changedOrDestroyed = merge(this.options.changes, this._destroy);\n      this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n        this._onSelect(event.source, event.isUserInput);\n        if (event.isUserInput && !this.multiple && this._panelOpen) {\n          this.close();\n          this.focus();\n        }\n      });\n      // Listen to changes in the internal state of the options and react accordingly.\n      // Handles cases like the labels of the selected options changing.\n      merge(...this.options.map(option => option._stateChanges)).pipe(takeUntil(changedOrDestroyed)).subscribe(() => {\n        // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n        // be the result of an expression changing. We have to use `detectChanges` in order\n        // to avoid \"changed after checked\" errors (see #14793).\n        this._changeDetectorRef.detectChanges();\n        this.stateChanges.next();\n      });\n    }\n    /** Invoked when an option is clicked. */\n    _onSelect(option, isUserInput) {\n      const wasSelected = this._selectionModel.isSelected(option);\n      if (!this.canSelectNullableOptions && option.value == null && !this._multiple) {\n        option.deselect();\n        this._selectionModel.clear();\n        if (this.value != null) {\n          this._propagateChanges(option.value);\n        }\n      } else {\n        if (wasSelected !== option.selected) {\n          option.selected ? this._selectionModel.select(option) : this._selectionModel.deselect(option);\n        }\n        if (isUserInput) {\n          this._keyManager.setActiveItem(option);\n        }\n        if (this.multiple) {\n          this._sortValues();\n          if (isUserInput) {\n            // In case the user selected the option with their mouse, we\n            // want to restore focus back to the trigger, in order to\n            // prevent the select keyboard controls from clashing with\n            // the ones from `mat-option`.\n            this.focus();\n          }\n        }\n      }\n      if (wasSelected !== this._selectionModel.isSelected(option)) {\n        this._propagateChanges();\n      }\n      this.stateChanges.next();\n    }\n    /** Sorts the selected values in the selected based on their order in the panel. */\n    _sortValues() {\n      if (this.multiple) {\n        const options = this.options.toArray();\n        this._selectionModel.sort((a, b) => {\n          return this.sortComparator ? this.sortComparator(a, b, options) : options.indexOf(a) - options.indexOf(b);\n        });\n        this.stateChanges.next();\n      }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n      let valueToEmit;\n      if (this.multiple) {\n        valueToEmit = this.selected.map(option => option.value);\n      } else {\n        valueToEmit = this.selected ? this.selected.value : fallbackValue;\n      }\n      this._value = valueToEmit;\n      this.valueChange.emit(valueToEmit);\n      this._onChange(valueToEmit);\n      this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Highlights the selected item. If no option is selected, it will highlight\n     * the first *enabled* option.\n     */\n    _highlightCorrectOption() {\n      if (this._keyManager) {\n        if (this.empty) {\n          // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n          // because it activates the first option that passes the skip predicate, rather than the\n          // first *enabled* option.\n          let firstEnabledOptionIndex = -1;\n          for (let index = 0; index < this.options.length; index++) {\n            const option = this.options.get(index);\n            if (!option.disabled) {\n              firstEnabledOptionIndex = index;\n              break;\n            }\n          }\n          this._keyManager.setActiveItem(firstEnabledOptionIndex);\n        } else {\n          this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n        }\n      }\n    }\n    /** Whether the panel is allowed to open. */\n    _canOpen() {\n      return !this._panelOpen && !this.disabled && this.options?.length > 0 && !!this._overlayDir;\n    }\n    /** Focuses the select element. */\n    focus(options) {\n      this._elementRef.nativeElement.focus(options);\n    }\n    /** Gets the aria-labelledby for the select panel. */\n    _getPanelAriaLabelledby() {\n      if (this.ariaLabel) {\n        return null;\n      }\n      const labelId = this._parentFormField?.getLabelId() || null;\n      const labelExpression = labelId ? labelId + ' ' : '';\n      return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Determines the `aria-activedescendant` to be set on the host. */\n    _getAriaActiveDescendant() {\n      if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n        return this._keyManager.activeItem.id;\n      }\n      return null;\n    }\n    /** Gets the aria-labelledby of the select component trigger. */\n    _getTriggerAriaLabelledby() {\n      if (this.ariaLabel) {\n        return null;\n      }\n      let value = this._parentFormField?.getLabelId() || '';\n      if (this.ariaLabelledby) {\n        value += ' ' + this.ariaLabelledby;\n      }\n      // The value should not be used for the trigger's aria-labelledby,\n      // but this currently \"breaks\" accessibility tests since they complain\n      // there is no aria-labelledby. This is because they are not setting an\n      // appropriate label on the form field or select.\n      // TODO: remove this conditional after fixing clients by ensuring their\n      // selects have a label applied.\n      if (!value) {\n        value = this._valueId;\n      }\n      return value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n      if (ids.length) {\n        this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n      } else {\n        this._elementRef.nativeElement.removeAttribute('aria-describedby');\n      }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n      this.focus();\n      this.open();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n      // Since the panel doesn't overlap the trigger, we\n      // want the label to only float when there's a value.\n      return this.panelOpen || !this.empty || this.focused && !!this.placeholder;\n    }\n    static ɵfac = function MatSelect_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSelect)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSelect,\n      selectors: [[\"mat-select\"]],\n      contentQueries: function MatSelect_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_SELECT_TRIGGER, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customTrigger = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n        }\n      },\n      viewQuery: function MatSelect_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(CdkConnectedOverlay, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trigger = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._overlayDir = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"combobox\", \"aria-haspopup\", \"listbox\", 1, \"mat-mdc-select\"],\n      hostVars: 19,\n      hostBindings: function MatSelect_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function MatSelect_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"focus\", function MatSelect_focus_HostBindingHandler() {\n            return ctx._onFocus();\n          })(\"blur\", function MatSelect_blur_HostBindingHandler() {\n            return ctx._onBlur();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx.panelOpen ? ctx.id + \"-panel\" : null)(\"aria-expanded\", ctx.panelOpen)(\"aria-label\", ctx.ariaLabel || null)(\"aria-required\", ctx.required.toString())(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-activedescendant\", ctx._getAriaActiveDescendant());\n          i0.ɵɵclassProp(\"mat-mdc-select-disabled\", ctx.disabled)(\"mat-mdc-select-invalid\", ctx.errorState)(\"mat-mdc-select-required\", ctx.required)(\"mat-mdc-select-empty\", ctx.empty)(\"mat-mdc-select-multiple\", ctx.multiple);\n        }\n      },\n      inputs: {\n        userAriaDescribedBy: [0, \"aria-describedby\", \"userAriaDescribedBy\"],\n        panelClass: \"panelClass\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n        hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n        placeholder: \"placeholder\",\n        required: [2, \"required\", \"required\", booleanAttribute],\n        multiple: [2, \"multiple\", \"multiple\", booleanAttribute],\n        disableOptionCentering: [2, \"disableOptionCentering\", \"disableOptionCentering\", booleanAttribute],\n        compareWith: \"compareWith\",\n        value: \"value\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        errorStateMatcher: \"errorStateMatcher\",\n        typeaheadDebounceInterval: [2, \"typeaheadDebounceInterval\", \"typeaheadDebounceInterval\", numberAttribute],\n        sortComparator: \"sortComparator\",\n        id: \"id\",\n        panelWidth: \"panelWidth\",\n        canSelectNullableOptions: [2, \"canSelectNullableOptions\", \"canSelectNullableOptions\", booleanAttribute]\n      },\n      outputs: {\n        openedChange: \"openedChange\",\n        _openedStream: \"opened\",\n        _closedStream: \"closed\",\n        selectionChange: \"selectionChange\",\n        valueChange: \"valueChange\"\n      },\n      exportAs: [\"matSelect\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }]), i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c3,\n      decls: 11,\n      vars: 9,\n      consts: [[\"fallbackOverlayOrigin\", \"cdkOverlayOrigin\", \"trigger\", \"\"], [\"panel\", \"\"], [\"cdk-overlay-origin\", \"\", 1, \"mat-mdc-select-trigger\", 3, \"click\"], [1, \"mat-mdc-select-value\"], [1, \"mat-mdc-select-placeholder\", \"mat-mdc-select-min-line\"], [1, \"mat-mdc-select-value-text\"], [1, \"mat-mdc-select-arrow-wrapper\"], [1, \"mat-mdc-select-arrow\"], [\"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M7 10l5 5 5-5z\"], [\"cdk-connected-overlay\", \"\", \"cdkConnectedOverlayLockPosition\", \"\", \"cdkConnectedOverlayHasBackdrop\", \"\", \"cdkConnectedOverlayBackdropClass\", \"cdk-overlay-transparent-backdrop\", 3, \"detach\", \"backdropClick\", \"overlayKeydown\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayFlexibleDimensions\"], [1, \"mat-mdc-select-min-line\"], [\"role\", \"listbox\", \"tabindex\", \"-1\", 3, \"keydown\", \"ngClass\"]],\n      template: function MatSelect_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵelementStart(0, \"div\", 2, 0);\n          i0.ɵɵlistener(\"click\", function MatSelect_Template_div_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.open());\n          });\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, MatSelect_Conditional_4_Template, 2, 1, \"span\", 4)(5, MatSelect_Conditional_5_Template, 3, 1, \"span\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 8);\n          i0.ɵɵelement(9, \"path\", 9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(10, MatSelect_ng_template_10_Template, 3, 10, \"ng-template\", 10);\n          i0.ɵɵlistener(\"detach\", function MatSelect_Template_ng_template_detach_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.close());\n          })(\"backdropClick\", function MatSelect_Template_ng_template_backdropClick_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.close());\n          })(\"overlayKeydown\", function MatSelect_Template_ng_template_overlayKeydown_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleOverlayKeydown($event));\n          });\n        }\n        if (rf & 2) {\n          const fallbackOverlayOrigin_r4 = i0.ɵɵreference(1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵattribute(\"id\", ctx._valueId);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.empty ? 4 : 5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"cdkConnectedOverlayDisableClose\", true)(\"cdkConnectedOverlayPanelClass\", ctx._overlayPanelClass)(\"cdkConnectedOverlayScrollStrategy\", ctx._scrollStrategy)(\"cdkConnectedOverlayOrigin\", ctx._preferredOverlayOrigin || fallbackOverlayOrigin_r4)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayWidth\", ctx._overlayWidth)(\"cdkConnectedOverlayFlexibleDimensions\", true);\n        }\n      },\n      dependencies: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n      styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatSelect;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nlet MatSelectTrigger = /*#__PURE__*/(() => {\n  class MatSelectTrigger {\n    static ɵfac = function MatSelectTrigger_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSelectTrigger)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSelectTrigger,\n      selectors: [[\"mat-select-trigger\"]],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }])]\n    });\n  }\n  return MatSelectTrigger;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatSelectModule = /*#__PURE__*/(() => {\n  class MatSelectModule {\n    static ɵfac = function MatSelectModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatSelectModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSelectModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatFormFieldModule, MatOptionModule, MatCommonModule]\n    });\n  }\n  return MatSelectModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatSelectModule as M, MAT_SELECT_SCROLL_STRATEGY as a, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY as b, MAT_SELECT_CONFIG as c, MAT_SELECT_SCROLL_STRATEGY_PROVIDER as d, MAT_SELECT_TRIGGER as e, MatSelectChange as f, MatSelect as g, MatSelectTrigger as h };\n//# sourceMappingURL=module-Cbt8Fcmv.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}