{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';\nimport { MatSortModule, MatSort } from '@angular/material/sort';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/table\";\nimport * as i5 from \"@angular/material/paginator\";\nimport * as i6 from \"@angular/material/sort\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/chips\";\nconst _c0 = () => [10, 25, 50, 100];\nfunction EligibilitySeniorityComponent_th_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Employee ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EligibilitySeniorityComponent_td_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(element_r1.employeeId);\n  }\n}\nfunction EligibilitySeniorityComponent_th_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Name\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EligibilitySeniorityComponent_td_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51)(1, \"div\", 52)(2, \"div\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const element_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(element_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(element_r2.currentDesignation);\n  }\n}\nfunction EligibilitySeniorityComponent_th_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Department\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EligibilitySeniorityComponent_td_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(element_r3.department);\n  }\n}\nfunction EligibilitySeniorityComponent_th_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Service Start Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EligibilitySeniorityComponent_td_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, element_r4.serviceStartDate, \"dd/MM/yyyy\"));\n  }\n}\nfunction EligibilitySeniorityComponent_th_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Years in Service\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EligibilitySeniorityComponent_td_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(element_r5.yearsInService);\n  }\n}\nfunction EligibilitySeniorityComponent_th_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 55);\n    i0.ɵɵtext(1, \"Probation Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EligibilitySeniorityComponent_td_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51)(1, \"mat-chip\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const element_r6 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r6.getProbationStatusClass(element_r6.probationStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r6.probationStatus, \" \");\n  }\n}\nfunction EligibilitySeniorityComponent_th_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 55);\n    i0.ɵɵtext(1, \"Pending Charges\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EligibilitySeniorityComponent_td_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51)(1, \"mat-chip\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const element_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", element_r8.pendingCharges ? \"status-warning\" : \"status-success\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r8.pendingCharges ? \"Yes\" : \"No\", \" \");\n  }\n}\nfunction EligibilitySeniorityComponent_th_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 50);\n    i0.ɵɵtext(1, \"Seniority Rank\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EligibilitySeniorityComponent_td_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51)(1, \"div\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const element_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(element_r9.seniorityRank);\n  }\n}\nfunction EligibilitySeniorityComponent_th_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 55);\n    i0.ɵɵtext(1, \"Eligibility\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EligibilitySeniorityComponent_td_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 51)(1, \"mat-chip\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const element_r10 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r6.getEligibilityStatusClass(element_r10.promotionEligibility));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r10.promotionEligibility, \" \");\n  }\n}\nfunction EligibilitySeniorityComponent_th_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 55);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EligibilitySeniorityComponent_td_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 51)(1, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function EligibilitySeniorityComponent_td_102_Template_button_click_1_listener() {\n      const element_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.viewDetails(element_r12));\n    });\n    i0.ɵɵtext(2, \" View Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function EligibilitySeniorityComponent_td_102_Template_button_click_3_listener() {\n      const element_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.initiatePromotion(element_r12));\n    });\n    i0.ɵɵtext(4, \" Initiate Promotion \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const element_r12 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", element_r12.promotionEligibility !== \"Yes\");\n  }\n}\nfunction EligibilitySeniorityComponent_tr_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 60);\n  }\n}\nfunction EligibilitySeniorityComponent_tr_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 61);\n  }\n}\nexport let EligibilitySeniorityComponent = /*#__PURE__*/(() => {\n  class EligibilitySeniorityComponent {\n    constructor() {\n      this.displayedColumns = ['employeeId', 'name', 'department', 'serviceStartDate', 'yearsInService', 'probationStatus', 'pendingCharges', 'seniorityRank', 'promotionEligibility', 'actions'];\n      this.dataSource = new MatTableDataSource();\n      this.selectedDepartment = '';\n      this.selectedEligibility = '';\n      this.selectedClass = '';\n      this.eligibilityData = [{\n        employeeId: 'EMP001',\n        name: 'Arun Kumar',\n        department: 'Operations',\n        serviceStartDate: new Date('2018-06-15'),\n        yearsInService: 6.5,\n        probationStatus: 'Completed',\n        pendingCharges: false,\n        seniorityRank: 1,\n        promotionEligibility: 'Yes',\n        currentDesignation: 'Assistant',\n        class: 'III'\n      }, {\n        employeeId: 'EMP002',\n        name: 'Priya Sharma',\n        department: 'Finance',\n        serviceStartDate: new Date('2019-03-22'),\n        yearsInService: 5.8,\n        probationStatus: 'Completed',\n        pendingCharges: false,\n        seniorityRank: 2,\n        promotionEligibility: 'Yes',\n        currentDesignation: 'Junior Assistant',\n        class: 'III'\n      }, {\n        employeeId: 'EMP003',\n        name: 'Rajesh Patel',\n        department: 'Technical',\n        serviceStartDate: new Date('2020-11-10'),\n        yearsInService: 4.2,\n        probationStatus: 'Completed',\n        pendingCharges: true,\n        seniorityRank: 3,\n        promotionEligibility: 'No',\n        currentDesignation: 'Assistant',\n        class: 'III'\n      }, {\n        employeeId: 'EMP004',\n        name: 'Meera Krishnan',\n        department: 'HR',\n        serviceStartDate: new Date('2019-01-05'),\n        yearsInService: 6.0,\n        probationStatus: 'Extended',\n        pendingCharges: false,\n        seniorityRank: 4,\n        promotionEligibility: 'Under Review',\n        currentDesignation: 'Assistant',\n        class: 'III'\n      }];\n    }\n    ngOnInit() {\n      this.dataSource.data = this.eligibilityData;\n    }\n    ngAfterViewInit() {\n      this.dataSource.paginator = this.paginator;\n      this.dataSource.sort = this.sort;\n    }\n    applyFilter(event) {\n      const filterValue = event.target.value;\n      this.dataSource.filter = filterValue.trim().toLowerCase();\n    }\n    applyFilters() {\n      // Implementation for applying multiple filters\n      console.log('Applying filters:', {\n        department: this.selectedDepartment,\n        eligibility: this.selectedEligibility,\n        class: this.selectedClass\n      });\n    }\n    exportList() {\n      console.log('Exporting eligibility list');\n    }\n    viewDetails(employee) {\n      console.log('View details for:', employee);\n    }\n    initiatePromotion(employee) {\n      console.log('Initiate promotion for:', employee);\n    }\n    getProbationStatusClass(status) {\n      switch (status.toLowerCase()) {\n        case 'completed':\n          return 'status-completed';\n        case 'extended':\n          return 'status-extended';\n        default:\n          return 'status-warning';\n      }\n    }\n    getEligibilityStatusClass(status) {\n      switch (status) {\n        case 'Yes':\n          return 'status-eligible';\n        case 'No':\n          return 'status-not-eligible';\n        case 'Under Review':\n          return 'status-under-review';\n        default:\n          return 'status-warning';\n      }\n    }\n    getEligibleCount() {\n      return this.dataSource.data.filter(emp => emp.promotionEligibility === 'Yes').length;\n    }\n    getUnderReviewCount() {\n      return this.dataSource.data.filter(emp => emp.promotionEligibility === 'Under Review').length;\n    }\n    getNotEligibleCount() {\n      return this.dataSource.data.filter(emp => emp.promotionEligibility === 'No').length;\n    }\n    getTotalCount() {\n      return this.dataSource.data.length;\n    }\n    static {\n      this.ɵfac = function EligibilitySeniorityComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || EligibilitySeniorityComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EligibilitySeniorityComponent,\n        selectors: [[\"app-eligibility-seniority\"]],\n        viewQuery: function EligibilitySeniorityComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        decls: 131,\n        vars: 13,\n        consts: [[1, \"eligibility-container\"], [1, \"header-section\"], [1, \"page-header\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"filter-card\"], [1, \"filter-row\"], [\"appearance\", \"outline\"], [3, \"valueChange\", \"value\"], [\"value\", \"\"], [\"value\", \"administration\"], [\"value\", \"finance\"], [\"value\", \"operations\"], [\"value\", \"technical\"], [\"value\", \"hr\"], [\"value\", \"Yes\"], [\"value\", \"No\"], [\"value\", \"Under Review\"], [\"value\", \"I\"], [\"value\", \"II\"], [\"value\", \"III\"], [\"value\", \"IV\"], [\"matInput\", \"\", \"placeholder\", \"Search by name or ID\", 3, \"keyup\"], [1, \"table-card\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"eligibility-table\", 3, \"dataSource\"], [\"matColumnDef\", \"employeeId\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"name\"], [\"matColumnDef\", \"department\"], [\"matColumnDef\", \"serviceStartDate\"], [\"matColumnDef\", \"yearsInService\"], [\"matColumnDef\", \"probationStatus\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"pendingCharges\"], [\"matColumnDef\", \"seniorityRank\"], [\"matColumnDef\", \"promotionEligibility\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"showFirstLastButtons\", \"\", \"aria-label\", \"Select page of employees\", 3, \"pageSizeOptions\"], [1, \"summary-stats\"], [1, \"stat-card\", \"eligible\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"stat-card\", \"under-review\"], [1, \"stat-card\", \"not-eligible\"], [1, \"stat-card\", \"total\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [1, \"employee-info\"], [1, \"employee-name\"], [1, \"employee-designation\"], [\"mat-header-cell\", \"\"], [3, \"ngClass\"], [1, \"rank-badge\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n        template: function EligibilitySeniorityComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n            i0.ɵɵtext(4, \"Eligibility and Seniority List\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Auto-generated list based on promotion criteria\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 3)(8, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function EligibilitySeniorityComponent_Template_button_click_8_listener() {\n              return ctx.applyFilters();\n            });\n            i0.ɵɵtext(9, \" Apply Filters \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function EligibilitySeniorityComponent_Template_button_click_10_listener() {\n              return ctx.exportList();\n            });\n            i0.ɵɵtext(11, \" Export List \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(12, \"mat-card\", 6)(13, \"mat-card-header\")(14, \"mat-card-title\");\n            i0.ɵɵtext(15, \"Filter Criteria\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"mat-card-content\")(17, \"div\", 7)(18, \"mat-form-field\", 8)(19, \"mat-label\");\n            i0.ɵɵtext(20, \"Department\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"mat-select\", 9);\n            i0.ɵɵtwoWayListener(\"valueChange\", function EligibilitySeniorityComponent_Template_mat_select_valueChange_21_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedDepartment, $event) || (ctx.selectedDepartment = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(22, \"mat-option\", 10);\n            i0.ɵɵtext(23, \"All Departments\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"mat-option\", 11);\n            i0.ɵɵtext(25, \"Administration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"mat-option\", 12);\n            i0.ɵɵtext(27, \"Finance\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"mat-option\", 13);\n            i0.ɵɵtext(29, \"Operations\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"mat-option\", 14);\n            i0.ɵɵtext(31, \"Technical\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"mat-option\", 15);\n            i0.ɵɵtext(33, \"Human Resources\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(34, \"mat-form-field\", 8)(35, \"mat-label\");\n            i0.ɵɵtext(36, \"Eligibility Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"mat-select\", 9);\n            i0.ɵɵtwoWayListener(\"valueChange\", function EligibilitySeniorityComponent_Template_mat_select_valueChange_37_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedEligibility, $event) || (ctx.selectedEligibility = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(38, \"mat-option\", 10);\n            i0.ɵɵtext(39, \"All Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"mat-option\", 16);\n            i0.ɵɵtext(41, \"Eligible\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"mat-option\", 17);\n            i0.ɵɵtext(43, \"Not Eligible\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"mat-option\", 18);\n            i0.ɵɵtext(45, \"Under Review\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(46, \"mat-form-field\", 8)(47, \"mat-label\");\n            i0.ɵɵtext(48, \"Class\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"mat-select\", 9);\n            i0.ɵɵtwoWayListener(\"valueChange\", function EligibilitySeniorityComponent_Template_mat_select_valueChange_49_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedClass, $event) || (ctx.selectedClass = $event);\n              return $event;\n            });\n            i0.ɵɵelementStart(50, \"mat-option\", 10);\n            i0.ɵɵtext(51, \"All Classes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"mat-option\", 19);\n            i0.ɵɵtext(53, \"Class I\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"mat-option\", 20);\n            i0.ɵɵtext(55, \"Class II\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"mat-option\", 21);\n            i0.ɵɵtext(57, \"Class III\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"mat-option\", 22);\n            i0.ɵɵtext(59, \"Class IV\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(60, \"mat-form-field\", 8)(61, \"mat-label\");\n            i0.ɵɵtext(62, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"input\", 23);\n            i0.ɵɵlistener(\"keyup\", function EligibilitySeniorityComponent_Template_input_keyup_63_listener($event) {\n              return ctx.applyFilter($event);\n            });\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(64, \"mat-card\", 24)(65, \"mat-card-header\")(66, \"mat-card-title\");\n            i0.ɵɵtext(67);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(68, \"mat-card-subtitle\");\n            i0.ɵɵtext(69, \"Sorted by seniority rank\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(70, \"mat-card-content\")(71, \"div\", 25)(72, \"table\", 26);\n            i0.ɵɵelementContainerStart(73, 27);\n            i0.ɵɵtemplate(74, EligibilitySeniorityComponent_th_74_Template, 2, 0, \"th\", 28)(75, EligibilitySeniorityComponent_td_75_Template, 2, 1, \"td\", 29);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(76, 30);\n            i0.ɵɵtemplate(77, EligibilitySeniorityComponent_th_77_Template, 2, 0, \"th\", 28)(78, EligibilitySeniorityComponent_td_78_Template, 6, 2, \"td\", 29);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(79, 31);\n            i0.ɵɵtemplate(80, EligibilitySeniorityComponent_th_80_Template, 2, 0, \"th\", 28)(81, EligibilitySeniorityComponent_td_81_Template, 2, 1, \"td\", 29);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(82, 32);\n            i0.ɵɵtemplate(83, EligibilitySeniorityComponent_th_83_Template, 2, 0, \"th\", 28)(84, EligibilitySeniorityComponent_td_84_Template, 3, 4, \"td\", 29);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(85, 33);\n            i0.ɵɵtemplate(86, EligibilitySeniorityComponent_th_86_Template, 2, 0, \"th\", 28)(87, EligibilitySeniorityComponent_td_87_Template, 2, 1, \"td\", 29);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(88, 34);\n            i0.ɵɵtemplate(89, EligibilitySeniorityComponent_th_89_Template, 2, 0, \"th\", 35)(90, EligibilitySeniorityComponent_td_90_Template, 3, 2, \"td\", 29);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(91, 36);\n            i0.ɵɵtemplate(92, EligibilitySeniorityComponent_th_92_Template, 2, 0, \"th\", 35)(93, EligibilitySeniorityComponent_td_93_Template, 3, 2, \"td\", 29);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(94, 37);\n            i0.ɵɵtemplate(95, EligibilitySeniorityComponent_th_95_Template, 2, 0, \"th\", 28)(96, EligibilitySeniorityComponent_td_96_Template, 3, 1, \"td\", 29);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(97, 38);\n            i0.ɵɵtemplate(98, EligibilitySeniorityComponent_th_98_Template, 2, 0, \"th\", 35)(99, EligibilitySeniorityComponent_td_99_Template, 3, 2, \"td\", 29);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(100, 39);\n            i0.ɵɵtemplate(101, EligibilitySeniorityComponent_th_101_Template, 2, 0, \"th\", 35)(102, EligibilitySeniorityComponent_td_102_Template, 5, 1, \"td\", 29);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵtemplate(103, EligibilitySeniorityComponent_tr_103_Template, 1, 0, \"tr\", 40)(104, EligibilitySeniorityComponent_tr_104_Template, 1, 0, \"tr\", 41);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(105, \"mat-paginator\", 42);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(106, \"div\", 43)(107, \"mat-card\", 44)(108, \"mat-card-content\")(109, \"div\", 45);\n            i0.ɵɵtext(110);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(111, \"div\", 46);\n            i0.ɵɵtext(112, \"Eligible Employees\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(113, \"mat-card\", 47)(114, \"mat-card-content\")(115, \"div\", 45);\n            i0.ɵɵtext(116);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(117, \"div\", 46);\n            i0.ɵɵtext(118, \"Under Review\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(119, \"mat-card\", 48)(120, \"mat-card-content\")(121, \"div\", 45);\n            i0.ɵɵtext(122);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(123, \"div\", 46);\n            i0.ɵɵtext(124, \"Not Eligible\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(125, \"mat-card\", 49)(126, \"mat-card-content\")(127, \"div\", 45);\n            i0.ɵɵtext(128);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(129, \"div\", 46);\n            i0.ɵɵtext(130, \"Total Employees\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(21);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedDepartment);\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedEligibility);\n            i0.ɵɵadvance(12);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedClass);\n            i0.ɵɵadvance(18);\n            i0.ɵɵtextInterpolate1(\"Eligibility List (\", ctx.dataSource.data.length, \" employees)\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n            i0.ɵɵadvance(31);\n            i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"pageSizeOptions\", i0.ɵɵpureFunction0(12, _c0));\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.getEligibleCount());\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.getUnderReviewCount());\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.getNotEligibleCount());\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.getTotalCount());\n          }\n        },\n        dependencies: [CommonModule, i1.NgClass, i1.DatePipe, MatCardModule, i2.MatCard, i2.MatCardContent, i2.MatCardHeader, i2.MatCardSubtitle, i2.MatCardTitle, MatButtonModule, i3.MatButton, MatTableModule, i4.MatTable, i4.MatHeaderCellDef, i4.MatHeaderRowDef, i4.MatColumnDef, i4.MatCellDef, i4.MatRowDef, i4.MatHeaderCell, i4.MatCell, i4.MatHeaderRow, i4.MatRow, MatPaginatorModule, i5.MatPaginator, MatSortModule, i6.MatSort, i6.MatSortHeader, MatFormFieldModule, i7.MatFormField, i7.MatLabel, MatInputModule, i8.MatInput, MatSelectModule, i9.MatSelect, i9.MatOption, MatChipsModule, i10.MatChip, MatIconModule, FormsModule, RouterModule],\n        styles: [\".eligibility-container[_ngcontent-%COMP%]{padding:20px;background-color:#fff;min-height:100vh}.header-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:30px}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:28px;font-weight:600;color:#333}.page-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0 0;color:#666;font-size:16px}.header-actions[_ngcontent-%COMP%]{display:flex;gap:15px}.filter-card[_ngcontent-%COMP%]{margin-bottom:25px;background:#fff;border-radius:12px;box-shadow:0 4px 20px #0000001a}.filter-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px}.table-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 20px #0000001a;margin-bottom:25px}.table-container[_ngcontent-%COMP%]{overflow-x:auto}.eligibility-table[_ngcontent-%COMP%]{width:100%}.employee-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.employee-name[_ngcontent-%COMP%]{font-weight:500;color:#333}.employee-designation[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-top:2px}.rank-badge[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#333;padding:4px 8px;border-radius:12px;font-weight:500;text-align:center;min-width:30px}.status-success[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.status-warning[_ngcontent-%COMP%]{background-color:#feebc8;color:#7b341e}.status-completed[_ngcontent-%COMP%]{background-color:#bee3f8;color:#2a4365}.status-extended[_ngcontent-%COMP%]{background-color:#fbb6ce;color:#702459}.status-eligible[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.status-not-eligible[_ngcontent-%COMP%]{background-color:#fed7d7;color:#742a2a}.status-under-review[_ngcontent-%COMP%]{background-color:#feebc8;color:#7b341e}.summary-stats[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px;margin-top:25px}.stat-card[_ngcontent-%COMP%]{text-align:center;border-radius:12px;box-shadow:0 4px 20px #0000001a}.stat-card.eligible[_ngcontent-%COMP%]{border-left:4px solid #38a169}.stat-card.under-review[_ngcontent-%COMP%]{border-left:4px solid #d69e2e}.stat-card.not-eligible[_ngcontent-%COMP%]{border-left:4px solid #e53e3e}.stat-card.total[_ngcontent-%COMP%]{border-left:4px solid #3182ce}.stat-number[_ngcontent-%COMP%]{font-size:32px;font-weight:700;color:#333;margin-bottom:5px}.stat-label[_ngcontent-%COMP%]{font-size:14px;color:#666}@media (max-width: 768px){.header-section[_ngcontent-%COMP%]{flex-direction:column;gap:20px}.filter-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.summary-stats[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}\"]\n      });\n    }\n  }\n  return EligibilitySeniorityComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}