{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { Observable } from '../Observable';\nimport { mergeMap } from '../operators/mergeMap';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nconst nodeEventEmitterMethods = ['addListener', 'removeListener'];\nconst eventTargetMethods = ['addEventListener', 'removeEventListener'];\nconst jqueryMethods = ['on', 'off'];\nexport function fromEvent(target, eventName, options, resultSelector) {\n  if (isFunction(options)) {\n    resultSelector = options;\n    options = undefined;\n  }\n  if (resultSelector) {\n    return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs(resultSelector));\n  }\n  const [add, remove] = isEventTarget(target) ? eventTargetMethods.map(methodName => handler => target[methodName](eventName, handler, options)) : isNodeStyleEventEmitter(target) ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName)) : isJQueryStyleEventEmitter(target) ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName)) : [];\n  if (!add) {\n    if (isArrayLike(target)) {\n      return mergeMap(subTarget => fromEvent(subTarget, eventName, options))(innerFrom(target));\n    }\n  }\n  if (!add) {\n    throw new TypeError('Invalid event target');\n  }\n  return new Observable(subscriber => {\n    const handler = (...args) => subscriber.next(1 < args.length ? args : args[0]);\n    add(handler);\n    return () => remove(handler);\n  });\n}\nfunction toCommonHandlerRegistry(target, eventName) {\n  return methodName => handler => target[methodName](eventName, handler);\n}\nfunction isNodeStyleEventEmitter(target) {\n  return isFunction(target.addListener) && isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n  return isFunction(target.on) && isFunction(target.off);\n}\nfunction isEventTarget(target) {\n  return isFunction(target.addEventListener) && isFunction(target.removeEventListener);\n}\n//# sourceMappingURL=fromEvent.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}