{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';\nimport { MatSortModule, MatSort } from '@angular/material/sort';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/table\";\nimport * as i5 from \"@angular/material/paginator\";\nimport * as i6 from \"@angular/material/sort\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/chips\";\nimport * as i11 from \"@angular/material/datepicker\";\nimport * as i12 from \"@angular/forms\";\nconst _c0 = () => [10, 25, 50, 100];\nfunction PromotionResultsComponent_th_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 56);\n    i0.ɵɵtext(1, \"Employee\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromotionResultsComponent_td_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57)(1, \"div\", 58)(2, \"div\", 59);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 60);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const result_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(result_r2.employeeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(result_r2.employeeId);\n  }\n}\nfunction PromotionResultsComponent_th_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 61);\n    i0.ɵɵtext(1, \"Promotion\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromotionResultsComponent_td_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57)(1, \"div\", 62)(2, \"div\", 63);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 64);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const result_r3 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", result_r3.fromDesignation, \" \\u2192 \", result_r3.toDesignation, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(result_r3.department);\n  }\n}\nfunction PromotionResultsComponent_th_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 65);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromotionResultsComponent_td_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57)(1, \"mat-chip\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const result_r4 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getStatusClass(result_r4.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", result_r4.status, \" \");\n  }\n}\nfunction PromotionResultsComponent_th_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 67);\n    i0.ɵɵtext(1, \"Panel Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromotionResultsComponent_td_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, result_r6.panelDate, \"dd/MM/yyyy\"));\n  }\n}\nfunction PromotionResultsComponent_th_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 68);\n    i0.ɵɵtext(1, \"Effective Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromotionResultsComponent_td_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, result_r7.effectiveDate, \"dd/MM/yyyy\"));\n  }\n}\nfunction PromotionResultsComponent_th_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 69);\n    i0.ɵɵtext(1, \"Order Number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromotionResultsComponent_td_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(result_r8.orderNumber);\n  }\n}\nfunction PromotionResultsComponent_th_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 61);\n    i0.ɵɵtext(1, \"Approved By\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromotionResultsComponent_td_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(result_r9.approvedBy);\n  }\n}\nfunction PromotionResultsComponent_th_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 61);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromotionResultsComponent_td_132_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 57)(1, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function PromotionResultsComponent_td_132_Template_button_click_1_listener() {\n      const result_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.viewDetails(result_r11));\n    });\n    i0.ɵɵtext(2, \" View \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function PromotionResultsComponent_td_132_Template_button_click_3_listener() {\n      const result_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.downloadOrder(result_r11));\n    });\n    i0.ɵɵtext(4, \" Download PDF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function PromotionResultsComponent_td_132_Template_button_click_5_listener() {\n      const result_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.editResult(result_r11));\n    });\n    i0.ɵɵtext(6, \" Edit \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const result_r11 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !result_r11.finalOrderPdf);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", result_r11.status === \"Approved\");\n  }\n}\nfunction PromotionResultsComponent_tr_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 73);\n  }\n}\nfunction PromotionResultsComponent_tr_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 74);\n  }\n}\nexport let PromotionResultsComponent = /*#__PURE__*/(() => {\n  class PromotionResultsComponent {\n    constructor() {\n      this.dataSource = new MatTableDataSource();\n      this.displayedColumns = ['employee', 'designation', 'department', 'status', 'effectiveDate', 'orderNumber', 'actions'];\n      this.selectedStatus = '';\n      this.selectedDepartment = '';\n      this.fromDate = null;\n      this.toDate = null;\n      this.dashboardStats = {\n        totalPromotions: 156,\n        approvedPromotions: 142,\n        rejectedPromotions: 8,\n        pendingPromotions: 4,\n        onHoldPromotions: 2\n      };\n      this.promotionResults = [{\n        employeeId: 'EMP001',\n        employeeName: 'Amit Kumar',\n        fromDesignation: 'Assistant',\n        toDesignation: 'Superintendent',\n        department: 'Operations',\n        status: 'Approved',\n        effectiveDate: new Date('2024-04-01'),\n        orderNumber: 'ORD-2024-001',\n        approvalDate: new Date('2024-03-15'),\n        remarks: 'Promotion approved based on excellent performance'\n      }, {\n        employeeId: 'EMP002',\n        employeeName: 'Priya Sharma',\n        fromDesignation: 'Junior Assistant',\n        toDesignation: 'Assistant',\n        department: 'Finance',\n        status: 'Approved',\n        effectiveDate: new Date('2024-04-01'),\n        orderNumber: 'ORD-2024-002',\n        approvalDate: new Date('2024-03-15'),\n        remarks: 'Promotion approved with commendation'\n      }, {\n        employeeId: 'EMP003',\n        employeeName: 'Rajesh Patel',\n        fromDesignation: 'Clerk',\n        toDesignation: 'Senior Clerk',\n        department: 'Administration',\n        status: 'On Hold',\n        effectiveDate: new Date('2024-05-01'),\n        orderNumber: '',\n        approvalDate: new Date('2024-03-20'),\n        remarks: 'Pending documentation verification'\n      }];\n    }\n    ngOnInit() {\n      this.dataSource.data = this.promotionResults;\n    }\n    ngAfterViewInit() {\n      this.dataSource.paginator = this.paginator;\n      this.dataSource.sort = this.sort;\n    }\n    applyFilter(event) {\n      const filterValue = event.target.value;\n      this.dataSource.filter = filterValue.trim().toLowerCase();\n      if (this.dataSource.paginator) {\n        this.dataSource.paginator.firstPage();\n      }\n    }\n    applyFilters() {\n      let filteredData = this.promotionResults;\n      if (this.selectedStatus) {\n        filteredData = filteredData.filter(item => item.status === this.selectedStatus);\n      }\n      if (this.selectedDepartment) {\n        filteredData = filteredData.filter(item => item.department === this.selectedDepartment);\n      }\n      if (this.fromDate) {\n        filteredData = filteredData.filter(item => item.effectiveDate >= this.fromDate);\n      }\n      if (this.toDate) {\n        filteredData = filteredData.filter(item => item.effectiveDate <= this.toDate);\n      }\n      this.dataSource.data = filteredData;\n    }\n    clearFilters() {\n      this.selectedStatus = '';\n      this.selectedDepartment = '';\n      this.fromDate = null;\n      this.toDate = null;\n      this.dataSource.data = this.promotionResults;\n    }\n    getStatusClass(status) {\n      switch (status) {\n        case 'Approved':\n          return 'status-approved';\n        case 'Rejected':\n          return 'status-rejected';\n        case 'Pending':\n          return 'status-pending';\n        case 'On Hold':\n          return 'status-on-hold';\n        default:\n          return 'status-pending';\n      }\n    }\n    viewDetails(result) {\n      console.log('View details for:', result);\n    }\n    editResult(result) {\n      console.log('Edit result for:', result);\n    }\n    generateReport() {\n      console.log('Generate promotion results report');\n    }\n    exportResults() {\n      console.log('Export promotion results');\n    }\n    static {\n      this.ɵfac = function PromotionResultsComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PromotionResultsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PromotionResultsComponent,\n        selectors: [[\"app-promotion-results\"]],\n        viewQuery: function PromotionResultsComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        decls: 136,\n        vars: 19,\n        consts: [[\"fromPicker\", \"\"], [\"toPicker\", \"\"], [1, \"results-container\"], [1, \"header-section\"], [1, \"page-header\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", 3, \"click\"], [1, \"stats-grid\"], [1, \"stat-card\", \"total\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"stat-card\", \"approved\"], [1, \"stat-card\", \"rejected\"], [1, \"stat-card\", \"pending\"], [1, \"stat-card\", \"on-hold\"], [1, \"filter-card\"], [1, \"filter-row\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"placeholder\", \"Search by name, ID, or order number\", 3, \"keyup\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [\"value\", \"\"], [\"value\", \"Approved\"], [\"value\", \"Rejected\"], [\"value\", \"Pending\"], [\"value\", \"On Hold\"], [\"value\", \"Administration\"], [\"value\", \"Finance\"], [\"value\", \"Operations\"], [\"value\", \"Technical\"], [\"value\", \"HR\"], [\"matInput\", \"\", 3, \"ngModelChange\", \"dateChange\", \"matDatepicker\", \"ngModel\"], [\"matIconSuffix\", \"\", 3, \"for\"], [1, \"filter-actions\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"table-card\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"results-table\", 3, \"dataSource\"], [\"matColumnDef\", \"employee\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"employeeName\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"promotion\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"status\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"status\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"panelDate\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"panelDate\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"effectiveDate\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"effectiveDate\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"orderNumber\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"orderNumber\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"approvedBy\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"showFirstLastButtons\", \"\", \"aria-label\", \"Select page of promotion results\", 3, \"pageSizeOptions\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"employeeName\"], [\"mat-cell\", \"\"], [1, \"employee-info\"], [1, \"employee-name\"], [1, \"employee-id\"], [\"mat-header-cell\", \"\"], [1, \"promotion-info\"], [1, \"from-to\"], [1, \"department\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"status\"], [3, \"ngClass\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"panelDate\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"effectiveDate\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"orderNumber\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"mat-button\", \"\", 3, \"click\", \"disabled\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n        template: function PromotionResultsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"h1\");\n            i0.ɵɵtext(4, \"Promotion Results Dashboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"View and manage promotion outcomes and final orders\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 5)(8, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function PromotionResultsComponent_Template_button_click_8_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.generateReport());\n            });\n            i0.ɵɵtext(9, \" Generate Report \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function PromotionResultsComponent_Template_button_click_10_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportResults());\n            });\n            i0.ɵɵtext(11, \" Export Results \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(12, \"div\", 8)(13, \"mat-card\", 9)(14, \"mat-card-content\")(15, \"div\", 10);\n            i0.ɵɵtext(16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 11);\n            i0.ɵɵtext(18, \"Total Promotions\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"mat-card\", 12)(20, \"mat-card-content\")(21, \"div\", 10);\n            i0.ɵɵtext(22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\", 11);\n            i0.ɵɵtext(24, \"Approved\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(25, \"mat-card\", 13)(26, \"mat-card-content\")(27, \"div\", 10);\n            i0.ɵɵtext(28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 11);\n            i0.ɵɵtext(30, \"Rejected\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"mat-card\", 14)(32, \"mat-card-content\")(33, \"div\", 10);\n            i0.ɵɵtext(34);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 11);\n            i0.ɵɵtext(36, \"Pending\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"mat-card\", 15)(38, \"mat-card-content\")(39, \"div\", 10);\n            i0.ɵɵtext(40);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"div\", 11);\n            i0.ɵɵtext(42, \"On Hold\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(43, \"mat-card\", 16)(44, \"mat-card-header\")(45, \"mat-card-title\");\n            i0.ɵɵtext(46, \"Filter Results\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(47, \"mat-card-content\")(48, \"div\", 17)(49, \"mat-form-field\", 18)(50, \"mat-label\");\n            i0.ɵɵtext(51, \"Search\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"input\", 19);\n            i0.ɵɵlistener(\"keyup\", function PromotionResultsComponent_Template_input_keyup_52_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.applyFilter($event));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"mat-form-field\", 18)(54, \"mat-label\");\n            i0.ɵɵtext(55, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"mat-select\", 20);\n            i0.ɵɵtwoWayListener(\"valueChange\", function PromotionResultsComponent_Template_mat_select_valueChange_56_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedStatus, $event) || (ctx.selectedStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectionChange\", function PromotionResultsComponent_Template_mat_select_selectionChange_56_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.filterByStatus());\n            });\n            i0.ɵɵelementStart(57, \"mat-option\", 21);\n            i0.ɵɵtext(58, \"All Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"mat-option\", 22);\n            i0.ɵɵtext(60, \"Approved\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"mat-option\", 23);\n            i0.ɵɵtext(62, \"Rejected\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"mat-option\", 24);\n            i0.ɵɵtext(64, \"Pending\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"mat-option\", 25);\n            i0.ɵɵtext(66, \"On Hold\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(67, \"mat-form-field\", 18)(68, \"mat-label\");\n            i0.ɵɵtext(69, \"Department\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"mat-select\", 20);\n            i0.ɵɵtwoWayListener(\"valueChange\", function PromotionResultsComponent_Template_mat_select_valueChange_70_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedDepartment, $event) || (ctx.selectedDepartment = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectionChange\", function PromotionResultsComponent_Template_mat_select_selectionChange_70_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.filterByDepartment());\n            });\n            i0.ɵɵelementStart(71, \"mat-option\", 21);\n            i0.ɵɵtext(72, \"All Departments\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"mat-option\", 26);\n            i0.ɵɵtext(74, \"Administration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(75, \"mat-option\", 27);\n            i0.ɵɵtext(76, \"Finance\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"mat-option\", 28);\n            i0.ɵɵtext(78, \"Operations\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"mat-option\", 29);\n            i0.ɵɵtext(80, \"Technical\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(81, \"mat-option\", 30);\n            i0.ɵɵtext(82, \"Human Resources\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(83, \"mat-form-field\", 18)(84, \"mat-label\");\n            i0.ɵɵtext(85, \"From Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"input\", 31);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PromotionResultsComponent_Template_input_ngModelChange_86_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.fromDate, $event) || (ctx.fromDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"dateChange\", function PromotionResultsComponent_Template_input_dateChange_86_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.filterByDateRange());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(87, \"mat-datepicker-toggle\", 32)(88, \"mat-datepicker\", null, 0);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(90, \"mat-form-field\", 18)(91, \"mat-label\");\n            i0.ɵɵtext(92, \"To Date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"input\", 31);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function PromotionResultsComponent_Template_input_ngModelChange_93_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.toDate, $event) || (ctx.toDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"dateChange\", function PromotionResultsComponent_Template_input_dateChange_93_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.filterByDateRange());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(94, \"mat-datepicker-toggle\", 32)(95, \"mat-datepicker\", null, 1);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(97, \"div\", 33)(98, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function PromotionResultsComponent_Template_button_click_98_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.clearFilters());\n            });\n            i0.ɵɵtext(99, \"Clear Filters\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(100, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function PromotionResultsComponent_Template_button_click_100_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.applyFilters());\n            });\n            i0.ɵɵtext(101, \"Apply Filters\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(102, \"mat-card\", 35)(103, \"mat-card-header\")(104, \"mat-card-title\");\n            i0.ɵɵtext(105);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(106, \"mat-card-content\")(107, \"div\", 36)(108, \"table\", 37);\n            i0.ɵɵelementContainerStart(109, 38);\n            i0.ɵɵtemplate(110, PromotionResultsComponent_th_110_Template, 2, 0, \"th\", 39)(111, PromotionResultsComponent_td_111_Template, 6, 2, \"td\", 40);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(112, 41);\n            i0.ɵɵtemplate(113, PromotionResultsComponent_th_113_Template, 2, 0, \"th\", 42)(114, PromotionResultsComponent_td_114_Template, 6, 3, \"td\", 40);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(115, 43);\n            i0.ɵɵtemplate(116, PromotionResultsComponent_th_116_Template, 2, 0, \"th\", 44)(117, PromotionResultsComponent_td_117_Template, 3, 2, \"td\", 40);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(118, 45);\n            i0.ɵɵtemplate(119, PromotionResultsComponent_th_119_Template, 2, 0, \"th\", 46)(120, PromotionResultsComponent_td_120_Template, 3, 4, \"td\", 40);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(121, 47);\n            i0.ɵɵtemplate(122, PromotionResultsComponent_th_122_Template, 2, 0, \"th\", 48)(123, PromotionResultsComponent_td_123_Template, 3, 4, \"td\", 40);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(124, 49);\n            i0.ɵɵtemplate(125, PromotionResultsComponent_th_125_Template, 2, 0, \"th\", 50)(126, PromotionResultsComponent_td_126_Template, 2, 1, \"td\", 40);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(127, 51);\n            i0.ɵɵtemplate(128, PromotionResultsComponent_th_128_Template, 2, 0, \"th\", 42)(129, PromotionResultsComponent_td_129_Template, 2, 1, \"td\", 40);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(130, 52);\n            i0.ɵɵtemplate(131, PromotionResultsComponent_th_131_Template, 2, 0, \"th\", 42)(132, PromotionResultsComponent_td_132_Template, 7, 2, \"td\", 40);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵtemplate(133, PromotionResultsComponent_tr_133_Template, 1, 0, \"tr\", 53)(134, PromotionResultsComponent_tr_134_Template, 1, 0, \"tr\", 54);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(135, \"mat-paginator\", 55);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            const fromPicker_r12 = i0.ɵɵreference(89);\n            const toPicker_r13 = i0.ɵɵreference(96);\n            i0.ɵɵadvance(16);\n            i0.ɵɵtextInterpolate(ctx.dashboardStats.totalPromotions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.dashboardStats.approvedPromotions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.dashboardStats.rejectedPromotions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.dashboardStats.pendingPromotions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.dashboardStats.onHoldPromotions);\n            i0.ɵɵadvance(16);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedStatus);\n            i0.ɵɵadvance(14);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedDepartment);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"matDatepicker\", fromPicker_r12);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.fromDate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"for\", fromPicker_r12);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"matDatepicker\", toPicker_r13);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.toDate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"for\", toPicker_r13);\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate1(\"Promotion Results (\", ctx.dataSource.filteredData.length, \" records)\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n            i0.ɵɵadvance(25);\n            i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"pageSizeOptions\", i0.ɵɵpureFunction0(18, _c0));\n          }\n        },\n        dependencies: [CommonModule, i1.NgClass, i1.DatePipe, MatCardModule, i2.MatCard, i2.MatCardContent, i2.MatCardHeader, i2.MatCardTitle, MatButtonModule, i3.MatButton, MatTableModule, i4.MatTable, i4.MatHeaderCellDef, i4.MatHeaderRowDef, i4.MatColumnDef, i4.MatCellDef, i4.MatRowDef, i4.MatHeaderCell, i4.MatCell, i4.MatHeaderRow, i4.MatRow, MatPaginatorModule, i5.MatPaginator, MatSortModule, i6.MatSort, i6.MatSortHeader, MatFormFieldModule, i7.MatFormField, i7.MatLabel, i7.MatSuffix, MatInputModule, i8.MatInput, MatSelectModule, i9.MatSelect, i9.MatOption, MatChipsModule, i10.MatChip, MatIconModule, MatDatepickerModule, i11.MatDatepicker, i11.MatDatepickerInput, i11.MatDatepickerToggle, MatNativeDateModule, FormsModule, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgModel, RouterModule],\n        styles: [\".results-container[_ngcontent-%COMP%]{padding:20px;background-color:#fff;min-height:100vh}.header-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:30px}.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:28px;font-weight:600;color:#333}.page-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0 0;color:#666;font-size:16px}.header-actions[_ngcontent-%COMP%]{display:flex;gap:15px}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px;margin-bottom:30px}.stat-card[_ngcontent-%COMP%]{text-align:center;border-radius:12px;box-shadow:0 4px 20px #0000001a}.stat-card.total[_ngcontent-%COMP%]{border-left:4px solid #3182ce}.stat-card.approved[_ngcontent-%COMP%]{border-left:4px solid #38a169}.stat-card.rejected[_ngcontent-%COMP%]{border-left:4px solid #e53e3e}.stat-card.pending[_ngcontent-%COMP%]{border-left:4px solid #d69e2e}.stat-card.on-hold[_ngcontent-%COMP%]{border-left:4px solid #805ad5}.stat-number[_ngcontent-%COMP%]{font-size:32px;font-weight:700;color:#333;margin-bottom:5px}.stat-label[_ngcontent-%COMP%]{font-size:14px;color:#666}.filter-card[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 20px #0000001a;margin-bottom:25px}.filter-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:20px;margin-bottom:20px}.filter-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:15px}.table-container[_ngcontent-%COMP%]{overflow-x:auto}.results-table[_ngcontent-%COMP%]{width:100%}.employee-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.employee-name[_ngcontent-%COMP%]{font-weight:500;color:#333}.employee-id[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-top:2px}.promotion-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.from-to[_ngcontent-%COMP%]{font-weight:500;color:#333;font-size:14px}.department[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-top:2px}.status-approved[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.status-rejected[_ngcontent-%COMP%]{background-color:#fed7d7;color:#742a2a}.status-pending[_ngcontent-%COMP%]{background-color:#feebc8;color:#7b341e}.status-on-hold[_ngcontent-%COMP%]{background-color:#e9d8fd;color:#553c9a}@media (max-width: 768px){.header-section[_ngcontent-%COMP%]{flex-direction:column;gap:20px}.filter-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.stats-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}\"]\n      });\n    }\n  }\n  return PromotionResultsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}