{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatListModule } from '@angular/material/list';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/list\";\nfunction SidebarComponent_mat_list_item_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-list-item\", 4)(1, \"span\", 5);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r1.route);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.label);\n  }\n}\nexport let SidebarComponent = /*#__PURE__*/(() => {\n  class SidebarComponent {\n    constructor() {\n      this.isOpen = true;\n      this.menuItems = [{\n        label: 'Dashboard',\n        route: '/dashboard'\n      }, {\n        label: 'Promotion Request',\n        route: '/promotion-request'\n      }, {\n        label: 'Eligibility List',\n        route: '/eligibility-seniority'\n      }, {\n        label: 'Employee List',\n        route: '/employee-list'\n      }, {\n        label: 'Suitability Report',\n        route: '/suitability-report'\n      }, {\n        label: 'Disciplinary Status',\n        route: '/disciplinary-status'\n      }, {\n        label: 'Panel Approval',\n        route: '/panel-approval'\n      }, {\n        label: 'Promotion Results',\n        route: '/promotion-results'\n      }, {\n        label: 'Promotion Workflow',\n        route: '/promotion-workflow'\n      }];\n    }\n    static {\n      this.ɵfac = function SidebarComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SidebarComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SidebarComponent,\n        selectors: [[\"app-sidebar\"]],\n        inputs: {\n          isOpen: \"isOpen\"\n        },\n        decls: 5,\n        vars: 3,\n        consts: [[1, \"sidebar\"], [1, \"sidebar-content\"], [1, \"sidebar-nav\"], [\"routerLinkActive\", \"active\", \"class\", \"nav-item\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [\"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"routerLink\"], [1, \"nav-label\"]],\n        template: function SidebarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"nav\", 2)(3, \"mat-nav-list\");\n            i0.ɵɵtemplate(4, SidebarComponent_mat_list_item_4_Template, 3, 2, \"mat-list-item\", 3);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"open\", ctx.isOpen);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, RouterModule, i2.RouterLink, i2.RouterLinkActive, MatListModule, i3.MatNavList, i3.MatListItem],\n        styles: [\".sidebar[_ngcontent-%COMP%]{position:fixed;top:64px;left:-250px;width:250px;height:calc(100vh - 64px);background-color:#283163;transition:left .3s ease;z-index:999;overflow-y:auto}.sidebar.open[_ngcontent-%COMP%]{left:0}.sidebar-content[_ngcontent-%COMP%]{padding:20px 0}.sidebar-nav[_ngcontent-%COMP%]   .mat-mdc-nav-list[_ngcontent-%COMP%]{padding:0}.sidebar-nav[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%]{color:#fff;border-radius:0;margin:0;padding:0}.sidebar-nav[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%]:hover{background-color:#ffffff1a}.sidebar-nav[_ngcontent-%COMP%]   .mat-mdc-list-item.active[_ngcontent-%COMP%]{background-color:#fff3;border-right:3px solid white}.sidebar-nav[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%]   .mdc-list-item__content[_ngcontent-%COMP%]{padding:16px 24px}.sidebar-nav[_ngcontent-%COMP%]   .nav-label[_ngcontent-%COMP%]{font-size:14px;font-weight:400}@media (max-width: 768px){.sidebar[_ngcontent-%COMP%]{width:100%;left:-100%}.sidebar.open[_ngcontent-%COMP%]{left:0}}\"]\n      });\n    }\n  }\n  return SidebarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}