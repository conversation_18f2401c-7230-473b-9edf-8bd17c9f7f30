{"ast": null, "code": "import { AuthGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  loadComponent: () => import('./layout/layout.component').then(m => m.LayoutComponent),\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    redirectTo: '/dashboard',\n    pathMatch: 'full'\n  }, {\n    path: 'dashboard',\n    loadComponent: () => import('./components/promotion-dashboard/promotion-dashboard.component').then(m => m.PromotionDashboardComponent)\n  }, {\n    path: 'promotion-request',\n    loadComponent: () => import('./components/promotion-request/promotion-request.component').then(m => m.PromotionRequestComponent)\n  }, {\n    path: 'eligibility-seniority',\n    loadComponent: () => import('./components/eligibility-seniority/eligibility-seniority.component').then(m => m.EligibilitySeniorityComponent)\n  }, {\n    path: 'employee-list',\n    loadComponent: () => import('./components/employee-list/employee-list.component').then(m => m.EmployeeListComponent)\n  }, {\n    path: 'suitability-report',\n    loadComponent: () => import('./components/suitability-report/suitability-report.component').then(m => m.SuitabilityReportComponent)\n  }, {\n    path: 'suitability-report/:id',\n    loadComponent: () => import('./components/suitability-report/suitability-report.component').then(m => m.SuitabilityReportComponent)\n  }, {\n    path: 'disciplinary-status',\n    loadComponent: () => import('./components/disciplinary-status/disciplinary-status.component').then(m => m.DisciplinaryStatusComponent)\n  }, {\n    path: 'panel-approval',\n    loadComponent: () => import('./components/panel-approval/panel-approval.component').then(m => m.PanelApprovalComponent)\n  }, {\n    path: 'promotion-results',\n    loadComponent: () => import('./components/promotion-results/promotion-results.component').then(m => m.PromotionResultsComponent)\n  }, {\n    path: 'promotion-workflow',\n    loadComponent: () => import('./components/promotion-workflow/promotion-workflow.component').then(m => m.PromotionWorkflowComponent)\n  }]\n}, {\n  path: '**',\n  redirectTo: '/dashboard'\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}