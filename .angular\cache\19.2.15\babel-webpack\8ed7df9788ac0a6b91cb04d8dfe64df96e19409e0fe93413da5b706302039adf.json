{"ast": null, "code": "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injector, ɵRuntimeError as _RuntimeError, ɵChangeDetectionScheduler as _ChangeDetectionScheduler, Injectable, ɵperformanceMarkFeature as _performanceMarkFeature, makeEnvironmentProviders, NgZone, RendererFactory2, ANIMATION_MODULE_TYPE } from '@angular/core';\nimport { DomRendererFactory2 } from '../dom_renderer-DGKzginR.mjs';\nconst ANIMATION_PREFIX = '@';\nlet AsyncAnimationRendererFactory = /*#__PURE__*/(() => {\n  class AsyncAnimationRendererFactory {\n    doc;\n    delegate;\n    zone;\n    animationType;\n    moduleImpl;\n    _rendererFactoryPromise = null;\n    scheduler = null;\n    injector = inject(Injector);\n    loadingSchedulerFn = inject(ɵASYNC_ANIMATION_LOADING_SCHEDULER_FN, {\n      optional: true\n    });\n    _engine;\n    /**\n     *\n     * @param moduleImpl allows to provide a mock implmentation (or will load the animation module)\n     */\n    constructor(doc, delegate, zone, animationType, moduleImpl) {\n      this.doc = doc;\n      this.delegate = delegate;\n      this.zone = zone;\n      this.animationType = animationType;\n      this.moduleImpl = moduleImpl;\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n      // When the root view is removed, the renderer defers the actual work to the\n      // `TransitionAnimationEngine` to do this, and the `TransitionAnimationEngine` doesn't actually\n      // remove the DOM node, but just calls `markElementAsRemoved()`. The actual DOM node is not\n      // removed until `TransitionAnimationEngine` \"flushes\".\n      // Note: we already flush on destroy within the `InjectableAnimationEngine`. The injectable\n      // engine is not provided when async animations are used.\n      this._engine?.flush();\n    }\n    /**\n     * @internal\n     */\n    loadImpl() {\n      // Note on the `.then(m => m)` part below: Closure compiler optimizations in g3 require\n      // `.then` to be present for a dynamic import (or an import should be `await`ed) to detect\n      // the set of imported symbols.\n      const loadFn = () => this.moduleImpl ?? import('@angular/animations/browser').then(m => m);\n      let moduleImplPromise;\n      if (this.loadingSchedulerFn) {\n        moduleImplPromise = this.loadingSchedulerFn(loadFn);\n      } else {\n        moduleImplPromise = loadFn();\n      }\n      return moduleImplPromise.catch(e => {\n        throw new _RuntimeError(5300 /* RuntimeErrorCode.ANIMATION_RENDERER_ASYNC_LOADING_FAILURE */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Async loading for animations package was ' + 'enabled, but loading failed. Angular falls back to using regular rendering. ' + \"No animations will be displayed and their styles won't be applied.\");\n      }).then(({\n        ɵcreateEngine,\n        ɵAnimationRendererFactory\n      }) => {\n        // We can't create the renderer yet because we might need the hostElement and the type\n        // Both are provided in createRenderer().\n        this._engine = ɵcreateEngine(this.animationType, this.doc);\n        const rendererFactory = new ɵAnimationRendererFactory(this.delegate, this._engine, this.zone);\n        this.delegate = rendererFactory;\n        return rendererFactory;\n      });\n    }\n    /**\n     * This method is delegating the renderer creation to the factories.\n     * It uses default factory while the animation factory isn't loaded\n     * and will rely on the animation factory once it is loaded.\n     *\n     * Calling this method will trigger as side effect the loading of the animation module\n     * if the renderered component uses animations.\n     */\n    createRenderer(hostElement, rendererType) {\n      const renderer = this.delegate.createRenderer(hostElement, rendererType);\n      if (renderer.ɵtype === 0 /* AnimationRendererType.Regular */) {\n        // The factory is already loaded, this is an animation renderer\n        return renderer;\n      }\n      // We need to prevent the DomRenderer to throw an error because of synthetic properties\n      if (typeof renderer.throwOnSyntheticProps === 'boolean') {\n        renderer.throwOnSyntheticProps = false;\n      }\n      // Using a dynamic renderer to switch the renderer implementation once the module is loaded.\n      const dynamicRenderer = new DynamicDelegationRenderer(renderer);\n      // Kick off the module loading if the component uses animations but the module hasn't been\n      // loaded yet.\n      if (rendererType?.data?.['animation'] && !this._rendererFactoryPromise) {\n        this._rendererFactoryPromise = this.loadImpl();\n      }\n      this._rendererFactoryPromise?.then(animationRendererFactory => {\n        const animationRenderer = animationRendererFactory.createRenderer(hostElement, rendererType);\n        dynamicRenderer.use(animationRenderer);\n        this.scheduler ??= this.injector.get(_ChangeDetectionScheduler, null, {\n          optional: true\n        });\n        this.scheduler?.notify(10 /* NotificationSource.AsyncAnimationsLoaded */);\n      }).catch(e => {\n        // Permanently use regular renderer when loading fails.\n        dynamicRenderer.use(renderer);\n      });\n      return dynamicRenderer;\n    }\n    begin() {\n      this.delegate.begin?.();\n    }\n    end() {\n      this.delegate.end?.();\n    }\n    whenRenderingDone() {\n      return this.delegate.whenRenderingDone?.() ?? Promise.resolve();\n    }\n    /**\n     * Used during HMR to clear any cached data about a component.\n     * @param componentId ID of the component that is being replaced.\n     */\n    componentReplaced(componentId) {\n      // Flush the engine since the renderer destruction waits for animations to be done.\n      this._engine?.flush();\n      this.delegate.componentReplaced?.(componentId);\n    }\n    static ɵfac = function AsyncAnimationRendererFactory_Factory(__ngFactoryType__) {\n      i0.ɵɵinvalidFactory();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AsyncAnimationRendererFactory,\n      factory: AsyncAnimationRendererFactory.ɵfac\n    });\n  }\n  return AsyncAnimationRendererFactory;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * The class allows to dynamicly switch between different renderer implementations\n * by changing the delegate renderer.\n */\nclass DynamicDelegationRenderer {\n  delegate;\n  // List of callbacks that need to be replayed on the animation renderer once its loaded\n  replay = [];\n  ɵtype = 1 /* AnimationRendererType.Delegated */;\n  constructor(delegate) {\n    this.delegate = delegate;\n  }\n  use(impl) {\n    this.delegate = impl;\n    if (this.replay !== null) {\n      // Replay queued actions using the animation renderer to apply\n      // all events and properties collected while loading was in progress.\n      for (const fn of this.replay) {\n        fn(impl);\n      }\n      // Set to `null` to indicate that the queue was processed\n      // and we no longer need to collect events and properties.\n      this.replay = null;\n    }\n  }\n  get data() {\n    return this.delegate.data;\n  }\n  destroy() {\n    this.replay = null;\n    this.delegate.destroy();\n  }\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n  get destroyNode() {\n    return this.delegate.destroyNode;\n  }\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n  }\n  insertBefore(parent, newChild, refChild, isMove) {\n    this.delegate.insertBefore(parent, newChild, refChild, isMove);\n  }\n  removeChild(parent, oldChild, isHostElement) {\n    this.delegate.removeChild(parent, oldChild, isHostElement);\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n  setProperty(el, name, value) {\n    // We need to keep track of animation properties set on default renderer\n    // So we can also set them also on the animation renderer\n    if (this.shouldReplay(name)) {\n      this.replay.push(renderer => renderer.setProperty(el, name, value));\n    }\n    this.delegate.setProperty(el, name, value);\n  }\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n  listen(target, eventName, callback, options) {\n    // We need to keep track of animation events registred by the default renderer\n    // So we can also register them against the animation renderer\n    if (this.shouldReplay(eventName)) {\n      this.replay.push(renderer => renderer.listen(target, eventName, callback, options));\n    }\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n  shouldReplay(propOrEventName) {\n    //`null` indicates that we no longer need to collect events and properties\n    return this.replay !== null && propOrEventName.startsWith(ANIMATION_PREFIX);\n  }\n}\n/**\n * Provides a custom scheduler function for the async loading of the animation package.\n *\n * Private token for investigation purposes\n */\nconst ɵASYNC_ANIMATION_LOADING_SCHEDULER_FN = /*#__PURE__*/new InjectionToken(ngDevMode ? 'async_animation_loading_scheduler_fn' : '');\n\n/**\n * Returns the set of dependency-injection providers\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * When you use this function instead of the eager `provideAnimations()`, animations won't be\n * rendered until the renderer is loaded.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```ts\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimationsAsync()\n *   ]\n * });\n * ```\n *\n * @param type pass `'noop'` as argument to disable animations.\n *\n * @publicApi\n */\nfunction provideAnimationsAsync(type = 'animations') {\n  _performanceMarkFeature('NgAsyncAnimations');\n  // Animations don't work on the server so we switch them over to no-op automatically.\n  if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n    type = 'noop';\n  }\n  return makeEnvironmentProviders([{\n    provide: RendererFactory2,\n    useFactory: (doc, renderer, zone) => {\n      return new AsyncAnimationRendererFactory(doc, renderer, zone, type);\n    },\n    deps: [DOCUMENT, DomRendererFactory2, NgZone]\n  }, {\n    provide: ANIMATION_MODULE_TYPE,\n    useValue: type === 'noop' ? 'NoopAnimations' : 'BrowserAnimations'\n  }]);\n}\nexport { provideAnimationsAsync, ɵASYNC_ANIMATION_LOADING_SCHEDULER_FN, AsyncAnimationRendererFactory as ɵAsyncAnimationRendererFactory };\n//# sourceMappingURL=async.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}