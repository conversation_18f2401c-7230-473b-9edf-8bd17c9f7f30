{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/stepper\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/chips\";\nimport * as i6 from \"@angular/material/progress-bar\";\nimport * as i7 from \"@angular/material/tabs\";\nfunction PromotionWorkflowComponent_mat_card_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 24)(1, \"mat-card-header\")(2, \"div\", 25)(3, \"div\", 26)(4, \"div\", 27);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 28)(7, \"mat-card-title\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-card-subtitle\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"mat-chip\", 29);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"uppercase\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"div\", 30)(16, \"div\", 31)(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(21, \"mat-progress-bar\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 33)(23, \"div\", 34)(24, \"span\", 35);\n    i0.ɵɵtext(25, \"Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 36);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 34)(29, \"span\", 35);\n    i0.ɵɵtext(30, \"Started:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 36);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 34)(35, \"span\", 35);\n    i0.ɵɵtext(36, \"Expected:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 36);\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(40, \"mat-card-actions\")(41, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PromotionWorkflowComponent_mat_card_16_Template_button_click_41_listener() {\n      const case_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewCaseDetails(case_r2));\n    });\n    i0.ɵɵtext(42, \" View Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PromotionWorkflowComponent_mat_card_16_Template_button_click_43_listener() {\n      const case_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.updateCase(case_r2));\n    });\n    i0.ɵɵtext(44, \" Update \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getInitials(case_r2.employeeName), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(case_r2.employeeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(case_r2.employeeId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getPriorityClass(case_r2.priority));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 13, case_r2.priority), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"Step \", case_r2.currentStep, \" of \", case_r2.totalSteps, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getProgressPercentage(case_r2), \"%\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r2.getProgressPercentage(case_r2))(\"ngClass\", ctx_r2.getProgressBarClass(case_r2.priority));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.formatCaseType(case_r2.type));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(33, 15, case_r2.startDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(39, 18, case_r2.expectedCompletion, \"dd/MM/yyyy\"));\n  }\n}\nfunction PromotionWorkflowComponent_mat_step_22_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate(step_r4.title);\n  }\n}\nfunction PromotionWorkflowComponent_mat_step_22_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"span\", 46);\n    i0.ɵɵtext(2, \"Completed on:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, step_r4.completedDate, \"dd/MM/yyyy\"));\n  }\n}\nfunction PromotionWorkflowComponent_mat_step_22_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function PromotionWorkflowComponent_mat_step_22_div_23_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const step_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.completeStep(step_r4));\n    });\n    i0.ɵɵtext(2, \" Mark Complete \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PromotionWorkflowComponent_mat_step_22_div_23_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const step_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addComment(step_r4));\n    });\n    i0.ɵɵtext(4, \" Add Comment \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PromotionWorkflowComponent_mat_step_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-step\", 39);\n    i0.ɵɵtemplate(1, PromotionWorkflowComponent_mat_step_22_ng_template_1_Template, 1, 1, \"ng-template\", 40);\n    i0.ɵɵelementStart(2, \"div\", 41)(3, \"div\", 42)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-chip\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"uppercase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"p\", 43);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 44)(12, \"div\", 45)(13, \"span\", 46);\n    i0.ɵɵtext(14, \"Assigned to:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 47);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 45)(18, \"span\", 46);\n    i0.ɵɵtext(19, \"Estimated Duration:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 47);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, PromotionWorkflowComponent_mat_step_22_div_22_Template, 6, 4, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, PromotionWorkflowComponent_mat_step_22_div_23_Template, 5, 0, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"completed\", step_r4.status === \"completed\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(step_r4.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStepStatusClass(step_r4.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 9, step_r4.status), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(step_r4.assignedTo);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", step_r4.estimatedDays, \" days\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.completedDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.status === \"current\");\n  }\n}\nexport let PromotionWorkflowComponent = /*#__PURE__*/(() => {\n  class PromotionWorkflowComponent {\n    constructor() {\n      this.Math = Math; // Make Math available in template\n      this.workflowSteps = [{\n        id: 1,\n        title: 'Application Submission',\n        description: 'Employee submits promotion application with required documents',\n        status: 'completed',\n        assignedTo: 'Employee',\n        estimatedDays: 1,\n        completedDate: new Date('2024-01-15')\n      }, {\n        id: 2,\n        title: 'Initial Review',\n        description: 'HR department reviews application for completeness',\n        status: 'completed',\n        assignedTo: 'HR Department',\n        estimatedDays: 3,\n        completedDate: new Date('2024-01-18')\n      }, {\n        id: 3,\n        title: 'Eligibility Verification',\n        description: 'Verify employee meets promotion criteria and seniority requirements',\n        status: 'current',\n        assignedTo: 'HR Officer',\n        estimatedDays: 5,\n        completedDate: null\n      }, {\n        id: 4,\n        title: 'Suitability Assessment',\n        description: 'Evaluate employee performance and suitability for promotion',\n        status: 'pending',\n        assignedTo: 'Reporting Manager',\n        estimatedDays: 7,\n        completedDate: null\n      }, {\n        id: 5,\n        title: 'Panel Review',\n        description: 'Promotion panel reviews and makes recommendation',\n        status: 'pending',\n        assignedTo: 'Promotion Panel',\n        estimatedDays: 10,\n        completedDate: null\n      }];\n      this.activeCases = [{\n        id: 'CASE-001',\n        employeeId: 'EMP001',\n        employeeName: 'Amit Kumar',\n        type: 'seasonal-to-regular',\n        priority: 'high',\n        currentStep: 3,\n        totalSteps: 10,\n        startDate: new Date('2024-01-15'),\n        expectedCompletion: new Date('2024-03-15'),\n        status: 'in-progress'\n      }, {\n        id: 'CASE-002',\n        employeeId: 'EMP002',\n        employeeName: 'Priya Sharma',\n        type: 'cadre-wise',\n        priority: 'medium',\n        currentStep: 5,\n        totalSteps: 12,\n        startDate: new Date('2024-01-10'),\n        expectedCompletion: new Date('2024-03-25'),\n        status: 'in-progress'\n      }, {\n        id: 'CASE-003',\n        employeeId: 'EMP003',\n        employeeName: 'Rajesh Patel',\n        type: 'seasonal-to-regular',\n        priority: 'low',\n        currentStep: 2,\n        totalSteps: 10,\n        startDate: new Date('2024-01-20'),\n        expectedCompletion: new Date('2024-03-20'),\n        status: 'in-progress'\n      }];\n    }\n    ngOnInit() {}\n    getStepState(status) {\n      switch (status) {\n        case 'completed':\n          return 'done';\n        case 'current':\n          return 'edit';\n        case 'pending':\n          return 'number';\n        default:\n          return 'number';\n      }\n    }\n    getInitials(name) {\n      return name.split(' ').map(n => n[0]).join('').toUpperCase();\n    }\n    getPriorityClass(priority) {\n      switch (priority) {\n        case 'high':\n          return 'priority-high';\n        case 'medium':\n          return 'priority-medium';\n        case 'low':\n          return 'priority-low';\n        default:\n          return 'priority-medium';\n      }\n    }\n    getTypeLabel(type) {\n      switch (type) {\n        case 'seasonal-to-regular':\n          return 'Seasonal to Regular';\n        case 'cadre-wise':\n          return 'Cadre-wise Promotion';\n        case 'special':\n          return 'Special Promotion';\n        default:\n          return 'Standard Promotion';\n      }\n    }\n    viewCaseDetails(caseItem) {\n      console.log('View case details:', caseItem);\n    }\n    updateCaseStatus(caseItem) {\n      console.log('Update case status:', caseItem);\n    }\n    startStep(step) {\n      console.log('Start step:', step);\n    }\n    viewStepDetails(step) {\n      console.log('View step details:', step);\n    }\n    useTemplate(templateType) {\n      console.log('Use template:', templateType);\n    }\n    createNewCase() {\n      console.log('Create new promotion case');\n    }\n    static {\n      this.ɵfac = function PromotionWorkflowComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PromotionWorkflowComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PromotionWorkflowComponent,\n        selectors: [[\"app-promotion-workflow\"]],\n        decls: 120,\n        vars: 3,\n        consts: [[1, \"workflow-container\"], [1, \"promotion-header\"], [1, \"navy-card\", \"workflow-tabs-card\"], [1, \"workflow-tabs\"], [\"label\", \"Active Cases\"], [1, \"tab-content\"], [1, \"cases-header\"], [\"mat-raised-button\", \"\", 1, \"navy-button\", 3, \"click\"], [1, \"cases-grid\"], [\"class\", \"case-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Workflow Steps\"], [\"orientation\", \"vertical\", 1, \"workflow-stepper\"], [3, \"completed\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Workflow Templates\"], [1, \"templates-grid\"], [1, \"template-section\"], [1, \"template-cards\"], [1, \"template-card\"], [1, \"template-stats\"], [1, \"stat\"], [1, \"stat-number\"], [1, \"stat-label\"], [\"mat-raised-button\", \"\", 1, \"navy-button\"], [\"mat-button\", \"\"], [1, \"case-card\"], [1, \"case-header\"], [1, \"employee-info\"], [1, \"employee-avatar\"], [1, \"employee-details\"], [3, \"ngClass\"], [1, \"case-progress\"], [1, \"progress-info\"], [\"mode\", \"determinate\", 3, \"value\", \"ngClass\"], [1, \"case-details\"], [1, \"detail-row\"], [1, \"label\"], [1, \"value\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"], [3, \"completed\"], [\"matStepLabel\", \"\"], [1, \"step-content\"], [1, \"step-header\"], [1, \"step-description\"], [1, \"step-details\"], [1, \"detail-item\"], [1, \"detail-label\"], [1, \"detail-value\"], [\"class\", \"detail-item\", 4, \"ngIf\"], [\"class\", \"step-actions\", 4, \"ngIf\"], [1, \"step-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n        template: function PromotionWorkflowComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"Promotion Workflow Management\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"Track and manage promotion processes across all stages\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"mat-card\", 2)(7, \"mat-tab-group\", 3)(8, \"mat-tab\", 4)(9, \"div\", 5)(10, \"div\", 6)(11, \"h3\");\n            i0.ɵɵtext(12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function PromotionWorkflowComponent_Template_button_click_13_listener() {\n              return ctx.createNewCase();\n            });\n            i0.ɵɵtext(14, \" New Case \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 8);\n            i0.ɵɵtemplate(16, PromotionWorkflowComponent_mat_card_16_Template, 45, 21, \"mat-card\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"mat-tab\", 10)(18, \"div\", 5)(19, \"h3\");\n            i0.ɵɵtext(20, \"Standard Promotion Workflow\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"mat-stepper\", 11);\n            i0.ɵɵtemplate(22, PromotionWorkflowComponent_mat_step_22_Template, 24, 11, \"mat-step\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"mat-tab\", 13)(24, \"div\", 5)(25, \"h3\");\n            i0.ɵɵtext(26, \"Promotion Workflow Templates\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"div\", 14)(28, \"div\", 15)(29, \"h4\");\n            i0.ɵɵtext(30, \"Seasonal to Regular\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"div\", 16)(32, \"mat-card\", 17)(33, \"mat-card-header\")(34, \"mat-card-title\");\n            i0.ɵɵtext(35, \"Standard Template\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"mat-card-subtitle\");\n            i0.ɵɵtext(37, \"Regular seasonal conversion\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"mat-card-content\")(39, \"div\", 18)(40, \"div\", 19)(41, \"span\", 20);\n            i0.ɵɵtext(42, \"8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"span\", 21);\n            i0.ɵɵtext(44, \"Steps\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"div\", 19)(46, \"span\", 20);\n            i0.ɵɵtext(47, \"30\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"span\", 21);\n            i0.ɵɵtext(49, \"Days\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(50, \"div\", 19)(51, \"span\", 20);\n            i0.ɵɵtext(52, \"5\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"span\", 21);\n            i0.ɵɵtext(54, \"Approvers\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(55, \"mat-card-actions\")(56, \"button\", 22);\n            i0.ɵɵtext(57, \" Use Template \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"button\", 23);\n            i0.ɵɵtext(59, \" View Details \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(60, \"mat-card\", 17)(61, \"mat-card-header\")(62, \"mat-card-title\");\n            i0.ɵɵtext(63, \"Fast Track\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"mat-card-subtitle\");\n            i0.ɵɵtext(65, \"Expedited process\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(66, \"mat-card-content\")(67, \"div\", 18)(68, \"div\", 19)(69, \"span\", 20);\n            i0.ɵɵtext(70, \"6\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"span\", 21);\n            i0.ɵɵtext(72, \"Steps\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(73, \"div\", 19)(74, \"span\", 20);\n            i0.ɵɵtext(75, \"15\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(76, \"span\", 21);\n            i0.ɵɵtext(77, \"Days\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(78, \"div\", 19)(79, \"span\", 20);\n            i0.ɵɵtext(80, \"3\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(81, \"span\", 21);\n            i0.ɵɵtext(82, \"Approvers\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(83, \"mat-card-actions\")(84, \"button\", 22);\n            i0.ɵɵtext(85, \" Use Template \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"button\", 23);\n            i0.ɵɵtext(87, \" View Details \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(88, \"div\", 15)(89, \"h4\");\n            i0.ɵɵtext(90, \"Cadre-wise Promotion\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"div\", 16)(92, \"mat-card\", 17)(93, \"mat-card-header\")(94, \"mat-card-title\");\n            i0.ɵɵtext(95, \"Standard Cadre\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(96, \"mat-card-subtitle\");\n            i0.ɵɵtext(97, \"Position-based promotion\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(98, \"mat-card-content\")(99, \"div\", 18)(100, \"div\", 19)(101, \"span\", 20);\n            i0.ɵɵtext(102, \"12\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(103, \"span\", 21);\n            i0.ɵɵtext(104, \"Steps\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(105, \"div\", 19)(106, \"span\", 20);\n            i0.ɵɵtext(107, \"45\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(108, \"span\", 21);\n            i0.ɵɵtext(109, \"Days\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(110, \"div\", 19)(111, \"span\", 20);\n            i0.ɵɵtext(112, \"7\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(113, \"span\", 21);\n            i0.ɵɵtext(114, \"Approvers\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(115, \"mat-card-actions\")(116, \"button\", 22);\n            i0.ɵɵtext(117, \" Use Template \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(118, \"button\", 23);\n            i0.ɵɵtext(119, \" View Details \");\n            i0.ɵɵelementEnd()()()()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate1(\"Active Promotion Cases (\", ctx.activeCases.length, \")\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.activeCases);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngForOf\", ctx.workflowSteps);\n          }\n        },\n        dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.UpperCasePipe, i1.DatePipe, MatCardModule, i2.MatCard, i2.MatCardActions, i2.MatCardContent, i2.MatCardHeader, i2.MatCardSubtitle, i2.MatCardTitle, MatStepperModule, i3.MatStep, i3.MatStepLabel, i3.MatStepper, MatButtonModule, i4.MatButton, MatIconModule, MatChipsModule, i5.MatChip, MatProgressBarModule, i6.MatProgressBar, MatTabsModule, i7.MatTab, i7.MatTabGroup, MatListModule, MatDividerModule, RouterModule],\n        styles: [\".workflow-container[_ngcontent-%COMP%]{padding:20px;max-width:1400px;margin:0 auto}.promotion-header[_ngcontent-%COMP%]{margin-bottom:30px}.promotion-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:28px;font-weight:600;color:#333}.promotion-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0 0;color:#666;font-size:16px}.navy-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 4px 20px #0000001a}.workflow-tabs[_ngcontent-%COMP%]   .mat-mdc-tab-body-wrapper[_ngcontent-%COMP%]{padding:20px 0}.tab-content[_ngcontent-%COMP%]{padding:20px}.cases-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.cases-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;color:#283163}.navy-button[_ngcontent-%COMP%]{background-color:#283163;color:#fff}.navy-button[_ngcontent-%COMP%]:hover{background-color:#1e2555}.cases-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(400px,1fr));gap:20px}.case-card[_ngcontent-%COMP%]{transition:transform .3s ease}.case-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.case-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%}.employee-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.employee-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:linear-gradient(45deg,#283163,#384173);display:flex;align-items:center;justify-content:center;color:#fff;font-weight:500;font-size:14px}.employee-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.case-progress[_ngcontent-%COMP%]{margin:16px 0}.progress-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:8px;font-size:14px;color:#666}.case-details[_ngcontent-%COMP%]{margin-top:16px}.detail-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:8px;font-size:14px}.label[_ngcontent-%COMP%]{color:#666}.value[_ngcontent-%COMP%]{color:#333;font-weight:500}.workflow-stepper[_ngcontent-%COMP%]{margin-top:20px}.step-content[_ngcontent-%COMP%]{padding:16px 0}.step-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px}.step-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#333}.step-description[_ngcontent-%COMP%]{color:#666;margin-bottom:16px}.step-details[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:12px;margin-bottom:16px}.detail-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.detail-label[_ngcontent-%COMP%]{font-size:12px;color:#666;text-transform:uppercase;letter-spacing:.5px}.detail-value[_ngcontent-%COMP%]{font-size:14px;color:#333;font-weight:500}.step-actions[_ngcontent-%COMP%]{display:flex;gap:12px}.templates-grid[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:30px}.template-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 16px;color:#283163;font-size:18px}.template-cards[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:20px}.template-card[_ngcontent-%COMP%]{border:1px solid #e0e0e0;transition:transform .3s ease}.template-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #00000026}.template-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-around;text-align:center}.stat[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.stat-number[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#283163}.stat-label[_ngcontent-%COMP%]{font-size:12px;color:#666;text-transform:uppercase;letter-spacing:.5px}.priority-high[_ngcontent-%COMP%]{background-color:#fed7d7;color:#742a2a}.priority-medium[_ngcontent-%COMP%]{background-color:#feebc8;color:#7b341e}.priority-low[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.progress-high[_ngcontent-%COMP%]   .mdc-linear-progress__bar-inner[_ngcontent-%COMP%]{border-color:#e53e3e}.progress-medium[_ngcontent-%COMP%]   .mdc-linear-progress__bar-inner[_ngcontent-%COMP%]{border-color:#d69e2e}.progress-low[_ngcontent-%COMP%]   .mdc-linear-progress__bar-inner[_ngcontent-%COMP%]{border-color:#38a169}.step-completed[_ngcontent-%COMP%]{background-color:#c6f6d5;color:#22543d}.step-current[_ngcontent-%COMP%]{background-color:#bee3f8;color:#2a4365}.step-pending[_ngcontent-%COMP%]{background-color:#f7fafc;color:#4a5568}@media (max-width: 768px){.workflow-container[_ngcontent-%COMP%]{padding:10px}.cases-grid[_ngcontent-%COMP%], .template-cards[_ngcontent-%COMP%]{grid-template-columns:1fr}.cases-header[_ngcontent-%COMP%]{flex-direction:column;gap:16px;align-items:flex-start}.case-header[_ngcontent-%COMP%]{flex-direction:column;gap:12px;align-items:flex-start}.step-details[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return PromotionWorkflowComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}