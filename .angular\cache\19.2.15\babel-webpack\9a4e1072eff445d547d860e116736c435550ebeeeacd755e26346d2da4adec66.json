{"ast": null, "code": "export { U as UniqueSelectionDispatcher } from './unique-selection-dispatcher-DtHZDqyJ.mjs';\nexport { A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy, b as _VIEW_REPEATER_STRATEGY, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nexport { D as DataSource, i as isDataSource } from './data-source-D34wiQZj.mjs';\nexport { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-D_JReLI1.mjs';\nexport { S as SelectionModel, g as getMultipleValuesInSingleSelectionError } from './selection-model-CeeHVIcP.mjs';\nimport '@angular/core';\nimport 'rxjs';\n//# sourceMappingURL=collections.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}