{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/chips\";\nimport * as i6 from \"@angular/material/tabs\";\nimport * as i7 from \"@angular/material/table\";\nimport * as i8 from \"@angular/material/checkbox\";\nimport * as i9 from \"@angular/material/radio\";\nimport * as i10 from \"@angular/forms\";\nfunction SuitabilityReportComponent_div_6_div_32_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1, \"*Required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"span\", 47);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-chip\", 48);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 49)(7, \"mat-radio-group\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_div_32_Template_mat_radio_group_ngModelChange_7_listener($event) {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r3.status, $event) || (item_r3.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(8, \"mat-radio-button\", 51);\n    i0.ɵɵtext(9, \"YES\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"mat-radio-button\", 52);\n    i0.ɵɵtext(11, \"NO\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-radio-button\", 53);\n    i0.ɵɵtext(13, \"PENDING\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, SuitabilityReportComponent_div_6_div_32_span_14_Template, 2, 0, \"span\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getChecklistStatusClass(item_r3.status, item_r3.required));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r3.status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r3.status);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", item_r3.required);\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const record_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, record_r5.date, \"dd/MM/yyyy\"));\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const record_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(record_r6.type);\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const record_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(record_r7.description);\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 66);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"mat-chip\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", record_r8.active ? \"status-active\" : \"status-resolved\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", record_r8.active ? \"Active\" : \"Resolved\", \" \");\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 68);\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 69);\n  }\n}\nfunction SuitabilityReportComponent_div_6_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"h4\");\n    i0.ɵɵtext(2, \"Punishment Records\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"table\", 57);\n    i0.ɵɵelementContainerStart(4, 58);\n    i0.ɵɵtemplate(5, SuitabilityReportComponent_div_6_div_104_th_5_Template, 2, 0, \"th\", 59)(6, SuitabilityReportComponent_div_6_div_104_td_6_Template, 3, 4, \"td\", 60);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(7, 61);\n    i0.ɵɵtemplate(8, SuitabilityReportComponent_div_6_div_104_th_8_Template, 2, 0, \"th\", 59)(9, SuitabilityReportComponent_div_6_div_104_td_9_Template, 2, 1, \"td\", 60);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(10, 62);\n    i0.ɵɵtemplate(11, SuitabilityReportComponent_div_6_div_104_th_11_Template, 2, 0, \"th\", 59)(12, SuitabilityReportComponent_div_6_div_104_td_12_Template, 2, 1, \"td\", 60);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(13, 63);\n    i0.ɵɵtemplate(14, SuitabilityReportComponent_div_6_div_104_th_14_Template, 2, 0, \"th\", 59)(15, SuitabilityReportComponent_div_6_div_104_td_15_Template, 3, 2, \"td\", 60);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(16, SuitabilityReportComponent_div_6_div_104_tr_16_Template, 1, 0, \"tr\", 64)(17, SuitabilityReportComponent_div_6_div_104_tr_17_Template, 1, 0, \"tr\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dataSource\", ctx_r3.punishmentRecords);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r3.punishmentColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r3.punishmentColumns);\n  }\n}\nfunction SuitabilityReportComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-card\", 4)(2, \"mat-card-header\")(3, \"div\", 5)(4, \"div\", 6);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 7)(7, \"mat-card-title\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-card-subtitle\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 8)(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(19, \"mat-card\", 9)(20, \"mat-tab-group\", 10)(21, \"mat-tab\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h3\");\n    i0.ɵɵtext(25, \"Required Documents Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 14)(27, \"mat-chip\", 15);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"mat-chip\", 16);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 17);\n    i0.ɵɵtemplate(32, SuitabilityReportComponent_div_6_div_32_Template, 15, 5, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"mat-tab\", 19)(34, \"div\", 12)(35, \"h3\");\n    i0.ɵɵtext(36, \"Performance Assessment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 20)(38, \"h4\");\n    i0.ɵɵtext(39, \"Overall Performance Rating\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"mat-radio-group\", 21);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_radio_group_ngModelChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.performanceRating, $event) || (ctx_r3.performanceRating = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(41, \"mat-radio-button\", 22);\n    i0.ɵɵtext(42, \"Excellent (90-100%)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"mat-radio-button\", 23);\n    i0.ɵɵtext(44, \"Good (75-89%)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"mat-radio-button\", 24);\n    i0.ɵɵtext(46, \"Satisfactory (60-74%)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"mat-radio-button\", 25);\n    i0.ɵɵtext(48, \"Needs Improvement (Below 60%)\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 20)(50, \"h4\");\n    i0.ɵɵtext(51, \"Key Performance Areas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 26)(53, \"div\", 27)(54, \"span\");\n    i0.ɵɵtext(55, \"Technical Competency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"mat-chip\", 28);\n    i0.ɵɵtext(57, \"85%\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 27)(59, \"span\");\n    i0.ɵɵtext(60, \"Communication Skills\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"mat-chip\", 29);\n    i0.ɵɵtext(62, \"78%\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 27)(64, \"span\");\n    i0.ɵɵtext(65, \"Leadership Qualities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"mat-chip\", 29);\n    i0.ɵɵtext(67, \"82%\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 27)(69, \"span\");\n    i0.ɵɵtext(70, \"Problem Solving\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"mat-chip\", 28);\n    i0.ɵɵtext(72, \"88%\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 27)(74, \"span\");\n    i0.ɵɵtext(75, \"Team Collaboration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"mat-chip\", 28);\n    i0.ɵɵtext(77, \"90%\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(78, \"div\", 20)(79, \"h4\");\n    i0.ɵɵtext(80, \"Behavioral Assessment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"div\", 30)(82, \"mat-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_82_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.behavioralTraits.punctuality, $event) || (ctx_r3.behavioralTraits.punctuality = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(83, \"Punctuality and Attendance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"mat-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_84_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.behavioralTraits.integrity, $event) || (ctx_r3.behavioralTraits.integrity = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(85, \"Integrity and Ethics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"mat-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_86_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.behavioralTraits.initiative, $event) || (ctx_r3.behavioralTraits.initiative = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(87, \"Initiative and Proactiveness\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"mat-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_88_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.behavioralTraits.adaptability, $event) || (ctx_r3.behavioralTraits.adaptability = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(89, \"Adaptability to Change\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"mat-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_90_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.behavioralTraits.customerFocus, $event) || (ctx_r3.behavioralTraits.customerFocus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(91, \"Customer/Stakeholder Focus\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(92, \"mat-tab\", 32)(93, \"div\", 12)(94, \"h3\");\n    i0.ɵɵtext(95, \"Disciplinary History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"div\", 33)(97, \"div\", 34)(98, \"h4\");\n    i0.ɵɵtext(99, \"Clean Disciplinary Record\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"p\");\n    i0.ɵɵtext(101, \"No disciplinary actions or punishments recorded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"mat-chip\", 35);\n    i0.ɵɵtext(103, \"CLEAN RECORD\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(104, SuitabilityReportComponent_div_6_div_104_Template, 18, 3, \"div\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(105, \"mat-tab\", 37)(106, \"div\", 12)(107, \"div\", 38)(108, \"div\", 39)(109, \"h3\");\n    i0.ɵɵtext(110, \"Promotion Recommendation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(111, \"div\", 40)(112, \"div\", 41)(113, \"span\");\n    i0.ɵɵtext(114, \"RECOMMENDED FOR PROMOTION\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(115, \"p\");\n    i0.ɵɵtext(116, \"Based on the comprehensive evaluation, the employee meets all criteria for promotion from Seasonal to Regular status.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(117, \"div\", 42)(118, \"button\", 43);\n    i0.ɵɵtext(119, \" Print Report \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(120, \"button\", 43);\n    i0.ɵɵtext(121, \" Download PDF \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(122, \"button\", 44);\n    i0.ɵɵtext(123, \" Submit for Approval \");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getInitials(ctx_r3.employee.employeeName), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.employee.employeeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.employee.designation, \" | \", ctx_r3.employee.employeeId, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Region: \", ctx_r3.employee.region, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Joined: \", i0.ɵɵpipeBind2(16, 17, ctx_r3.employee.joiningDate, \"dd/MM/yyyy\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.employee.workingDays, \" working days\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getCompletedCount(), \" Completed\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getPendingCount(), \" Pending\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.employee.checklist);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.performanceRating);\n    i0.ɵɵadvance(42);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.behavioralTraits.punctuality);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.behavioralTraits.integrity);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.behavioralTraits.initiative);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.behavioralTraits.adaptability);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.behavioralTraits.customerFocus);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.punishmentRecords.length > 0);\n  }\n}\nexport class SuitabilityReportComponent {\n  constructor(route) {\n    this.route = route;\n    this.employee = null;\n    this.punishmentColumns = ['description', 'status'];\n    this.punishmentData = [{\n      description: 'Delinquencies/irregularities prior to 5 years',\n      status: 'Eligible'\n    }, {\n      description: 'Award of punishment of Censure within one year',\n      status: 'Eligible'\n    }, {\n      description: 'Stoppage of increments within check period',\n      status: 'Eligible'\n    }];\n  }\n  ngOnInit() {\n    const employeeId = this.route.snapshot.paramMap.get('id');\n    this.loadEmployeeReport(employeeId);\n  }\n  loadEmployeeReport(id) {\n    // Mock data - in real app, this would come from a service\n    this.employee = {\n      id: 1,\n      employeeName: 'Saravanan M',\n      employeeId: 'EMP001',\n      designation: 'Software Engineer',\n      region: 'Tamil Nadu Civil Supplies Corporation',\n      joiningDate: new Date('2023-01-15'),\n      workingDays: 365,\n      dateOfBirth: new Date('1995-06-10'),\n      checklist: [{\n        name: 'First Page Record Sheet',\n        status: 'YES',\n        required: true\n      }, {\n        name: 'Degree Certificate',\n        status: 'YES',\n        required: true\n      }, {\n        name: 'Driving Certificate',\n        status: 'YES',\n        required: false\n      }, {\n        name: '10th Mark Sheet',\n        status: 'YES',\n        required: true\n      }, {\n        name: 'Transfer Certificate',\n        status: 'YES',\n        required: false\n      }, {\n        name: 'Community Certificate',\n        status: 'YES',\n        required: false\n      }, {\n        name: 'Employment Card',\n        status: 'YES',\n        required: false\n      }, {\n        name: 'Aadhar Card',\n        status: 'YES',\n        required: true\n      }, {\n        name: 'Family Card',\n        status: 'YES',\n        required: false\n      }, {\n        name: 'Duty Certificate',\n        status: 'YES',\n        required: false\n      }, {\n        name: 'Photo',\n        status: 'YES',\n        required: true\n      }, {\n        name: 'Bank Account Details',\n        status: 'YES',\n        required: true\n      }]\n    };\n  }\n  getInitials(name) {\n    return name.split(' ').map(n => n[0]).join('').toUpperCase();\n  }\n  getCompletedCount() {\n    return this.employee?.checklist.filter(item => item.status === 'YES').length || 0;\n  }\n  getPendingCount() {\n    return this.employee?.checklist.filter(item => item.status === 'PENDING').length || 0;\n  }\n  getItemStatusClass(status) {\n    return `status-${status.toLowerCase()}`;\n  }\n  getStatusIcon(status) {\n    switch (status) {\n      case 'YES':\n        return 'check_circle';\n      case 'NO':\n        return 'cancel';\n      case 'PENDING':\n        return 'schedule';\n      default:\n        return 'help';\n    }\n  }\n  getStatusIconClass(status) {\n    return `status-${status.toLowerCase()}`;\n  }\n  getStatusChipClass(status) {\n    switch (status) {\n      case 'YES':\n        return 'status-eligible';\n      case 'NO':\n        return 'status-not-eligible';\n      case 'PENDING':\n        return 'status-pending';\n      default:\n        return '';\n    }\n  }\n  updateItemStatus(index, status) {\n    if (this.employee) {\n      this.employee.checklist[index].status = status;\n    }\n  }\n  static {\n    this.ɵfac = function SuitabilityReportComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SuitabilityReportComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuitabilityReportComponent,\n      selectors: [[\"app-suitability-report\"]],\n      decls: 7,\n      vars: 1,\n      consts: [[1, \"report-container\"], [1, \"promotion-header\"], [\"class\", \"report-content\", 4, \"ngIf\"], [1, \"report-content\"], [1, \"navy-card\", \"employee-info-card\"], [1, \"employee-header\"], [1, \"employee-avatar-large\"], [1, \"employee-info\"], [1, \"employee-meta\"], [1, \"navy-card\", \"report-tabs-card\"], [1, \"report-tabs\"], [\"label\", \"Document Checklist\"], [1, \"tab-content\"], [1, \"checklist-header\"], [1, \"checklist-stats\"], [1, \"status-completed\"], [1, \"status-pending\"], [1, \"checklist-grid\"], [\"class\", \"checklist-item\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Performance Evaluation\"], [1, \"evaluation-section\"], [1, \"rating-group\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"excellent\"], [\"value\", \"good\"], [\"value\", \"satisfactory\"], [\"value\", \"needs-improvement\"], [1, \"performance-areas\"], [1, \"performance-item\"], [1, \"score-excellent\"], [1, \"score-good\"], [1, \"behavioral-checklist\"], [3, \"ngModelChange\", \"ngModel\"], [\"label\", \"Disciplinary Records\"], [1, \"disciplinary-status\"], [1, \"status-card\", \"clean-record\"], [1, \"status-clean\"], [\"class\", \"punishment-history\", 4, \"ngIf\"], [\"label\", \"Final Report\"], [1, \"final-report\"], [1, \"report-summary\"], [1, \"recommendation-card\"], [1, \"recommendation-status\", \"eligible\"], [1, \"report-actions\"], [\"mat-raised-button\", \"\", 1, \"navy-button\"], [\"mat-raised-button\", \"\", \"color\", \"accent\"], [1, \"checklist-item\"], [1, \"item-header\"], [1, \"item-name\"], [3, \"ngClass\"], [1, \"item-controls\"], [1, \"status-radio-group\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"YES\", 1, \"status-yes\"], [\"value\", \"NO\", 1, \"status-no\"], [\"value\", \"PENDING\", 1, \"status-pending\"], [\"class\", \"required-indicator\", 4, \"ngIf\"], [1, \"required-indicator\"], [1, \"punishment-history\"], [\"mat-table\", \"\", 1, \"punishment-table\", 3, \"dataSource\"], [\"matColumnDef\", \"date\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"description\"], [\"matColumnDef\", \"status\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function SuitabilityReportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Suitability Report Input\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5, \"Comprehensive evaluation for promotion eligibility\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, SuitabilityReportComponent_div_6_Template, 124, 20, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.employee);\n        }\n      },\n      dependencies: [CommonModule, i2.NgClass, i2.NgForOf, i2.NgIf, i2.DatePipe, MatCardModule, i3.MatCard, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatButtonModule, i4.MatButton, MatIconModule, MatChipsModule, i5.MatChip, MatTabsModule, i6.MatTab, i6.MatTabGroup, MatTableModule, i7.MatTable, i7.MatHeaderCellDef, i7.MatHeaderRowDef, i7.MatColumnDef, i7.MatCellDef, i7.MatRowDef, i7.MatHeaderCell, i7.MatCell, i7.MatHeaderRow, i7.MatRow, MatCheckboxModule, i8.MatCheckbox, MatRadioModule, i9.MatRadioGroup, i9.MatRadioButton, MatDialogModule, MatExpansionModule, RouterModule, FormsModule, i10.NgControlStatus, i10.NgModel],\n      styles: [\".report-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n\\n.promotion-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.promotion-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.promotion-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 0;\\n  color: #666;\\n  font-size: 16px;\\n}\\n\\n.navy-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 25px;\\n}\\n\\n.employee-info-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #283163 0%, #384173 100%);\\n  color: white;\\n}\\n\\n.employee-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  width: 100%;\\n}\\n\\n.employee-avatar-large[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 24px;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n}\\n\\n.employee-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.employee-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  margin-top: 8px;\\n  flex-wrap: wrap;\\n}\\n.employee-meta[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  color: rgba(255, 255, 255, 0.8);\\n  font-size: 14px;\\n}\\n\\n.report-tabs-card[_ngcontent-%COMP%] {\\n  overflow: visible;\\n}\\n\\n.report-tabs[_ngcontent-%COMP%]   .mat-mdc-tab-body-wrapper[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.checklist-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n}\\n\\n.checklist-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.checklist-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 20px;\\n}\\n\\n.checklist-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 20px;\\n  background: #fafafa;\\n}\\n\\n.item-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.item-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.item-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.status-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n\\n.required-indicator[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #d32f2f;\\n  font-weight: 500;\\n}\\n\\n.evaluation-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  padding-bottom: 20px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.evaluation-section[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.evaluation-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.rating-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n}\\n\\n.performance-areas[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 15px;\\n}\\n\\n.performance-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.behavioral-checklist[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 15px;\\n}\\n\\n.disciplinary-status[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.status-card[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border-radius: 8px;\\n  text-align: center;\\n}\\n\\n.status-card.clean-record[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  border: 2px solid #4caf50;\\n}\\n\\n.status-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #2e7d32;\\n}\\n\\n.status-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  color: #2e7d32;\\n}\\n\\n.punishment-table[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.final-report[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.recommendation-card[_ngcontent-%COMP%] {\\n  background: rgba(76, 175, 80, 0.1);\\n  border: 2px solid #4caf50;\\n  border-radius: 12px;\\n  padding: 24px;\\n  margin: 20px 0;\\n}\\n.recommendation-card[_ngcontent-%COMP%]   .recommendation-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #2e7d32;\\n  margin-bottom: 12px;\\n}\\n.recommendation-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  margin: 0;\\n}\\n\\n.report-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  justify-content: center;\\n  margin-top: 30px;\\n}\\n\\n.navy-button[_ngcontent-%COMP%] {\\n  background-color: #283163;\\n  color: white;\\n}\\n\\n.navy-button[_ngcontent-%COMP%]:hover {\\n  background-color: #1e2555;\\n}\\n\\n\\n\\n.status-completed[_ngcontent-%COMP%] {\\n  background-color: #c6f6d5;\\n  color: #22543d;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #feebc8;\\n  color: #7b341e;\\n}\\n\\n.status-clean[_ngcontent-%COMP%] {\\n  background-color: #c6f6d5;\\n  color: #22543d;\\n}\\n\\n.status-active[_ngcontent-%COMP%] {\\n  background-color: #fed7d7;\\n  color: #742a2a;\\n}\\n\\n.status-resolved[_ngcontent-%COMP%] {\\n  background-color: #c6f6d5;\\n  color: #22543d;\\n}\\n\\n.score-excellent[_ngcontent-%COMP%] {\\n  background-color: #c6f6d5;\\n  color: #22543d;\\n}\\n\\n.score-good[_ngcontent-%COMP%] {\\n  background-color: #bee3f8;\\n  color: #2a4365;\\n}\\n\\n@media (max-width: 768px) {\\n  .report-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .checklist-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .performance-areas[_ngcontent-%COMP%], .behavioral-checklist[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .employee-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .checklist-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n    align-items: flex-start;\\n  }\\n  .report-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "MatTabsModule", "MatTableModule", "MatCheckboxModule", "MatRadioModule", "MatDialogModule", "MatExpansionModule", "RouterModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "SuitabilityReportComponent_div_6_div_32_Template_mat_radio_group_ngModelChange_7_listener", "$event", "item_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵtwoWayBindingSet", "status", "ɵɵresetView", "ɵɵtemplate", "SuitabilityReportComponent_div_6_div_32_span_14_Template", "ɵɵadvance", "ɵɵtextInterpolate", "name", "ɵɵproperty", "ctx_r3", "getChecklistStatusClass", "required", "ɵɵtextInterpolate1", "ɵɵtwoWayProperty", "ɵɵpipeBind2", "record_r5", "date", "record_r6", "type", "record_r7", "description", "record_r8", "active", "ɵɵelement", "ɵɵelementContainerStart", "SuitabilityReportComponent_div_6_div_104_th_5_Template", "SuitabilityReportComponent_div_6_div_104_td_6_Template", "SuitabilityReportComponent_div_6_div_104_th_8_Template", "SuitabilityReportComponent_div_6_div_104_td_9_Template", "SuitabilityReportComponent_div_6_div_104_th_11_Template", "SuitabilityReportComponent_div_6_div_104_td_12_Template", "SuitabilityReportComponent_div_6_div_104_th_14_Template", "SuitabilityReportComponent_div_6_div_104_td_15_Template", "SuitabilityReportComponent_div_6_div_104_tr_16_Template", "SuitabilityReportComponent_div_6_div_104_tr_17_Template", "punishmentRecords", "punishmentColumns", "SuitabilityReportComponent_div_6_div_32_Template", "SuitabilityReportComponent_div_6_Template_mat_radio_group_ngModelChange_40_listener", "_r1", "ɵɵnextContext", "performanceRating", "SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_82_listener", "behavioralTraits", "punctuality", "SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_84_listener", "integrity", "SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_86_listener", "initiative", "SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_88_listener", "adaptability", "SuitabilityReportComponent_div_6_Template_mat_checkbox_ngModelChange_90_listener", "customerFocus", "SuitabilityReportComponent_div_6_div_104_Template", "getInitials", "employee", "employeeName", "ɵɵtextInterpolate2", "designation", "employeeId", "region", "joiningDate", "workingDays", "getCompletedCount", "getPendingCount", "checklist", "length", "SuitabilityReportComponent", "constructor", "route", "punishmentData", "ngOnInit", "snapshot", "paramMap", "get", "loadEmployeeReport", "id", "Date", "dateOfBirth", "split", "map", "n", "join", "toUpperCase", "filter", "item", "getItemStatusClass", "toLowerCase", "getStatusIcon", "getStatusIconClass", "getStatusChipClass", "updateItemStatus", "index", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "SuitabilityReportComponent_Template", "rf", "ctx", "SuitabilityReportComponent_div_6_Template", "i2", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i3", "MatCard", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i4", "MatButton", "i5", "MatChip", "i6", "Mat<PERSON><PERSON>", "MatTabGroup", "i7", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i8", "MatCheckbox", "i9", "MatRadioGroup", "MatRadioButton", "i10", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\promotion module\\src\\app\\components\\suitability-report\\suitability-report.component.ts", "C:\\Users\\<USER>\\Desktop\\promotion module\\src\\app\\components\\suitability-report\\suitability-report.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ActivatedRoute } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDialogModule, MatDialog } from '@angular/material/dialog';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { ChecklistItem, EmployeeReport } from '../../models/promotion.model';\n\n@Component({\n  selector: 'app-suitability-report',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    MatTabsModule,\n    MatTableModule,\n    MatCheckboxModule,\n    MatRadioModule,\n    MatDialogModule,\n    MatExpansionModule,\n    RouterModule,\n    FormsModule\n  ],\n  templateUrl: './suitability-report.component.html',\n  styleUrls: ['./suitability-report.component.scss']\n})\nexport class SuitabilityReportComponent implements OnInit {\n  employee: EmployeeReport | null = null;\n  punishmentColumns: string[] = ['description', 'status'];\n  punishmentData = [\n    { description: 'Delinquencies/irregularities prior to 5 years', status: 'Eligible' },\n    { description: 'Award of punishment of Censure within one year', status: 'Eligible' },\n    { description: 'Stoppage of increments within check period', status: 'Eligible' }\n  ];\n\n  constructor(private route: ActivatedRoute) {}\n\n  ngOnInit(): void {\n    const employeeId = this.route.snapshot.paramMap.get('id');\n    this.loadEmployeeReport(employeeId);\n  }\n\n  loadEmployeeReport(id: string | null): void {\n    // Mock data - in real app, this would come from a service\n    this.employee = {\n      id: 1,\n      employeeName: 'Saravanan M',\n      employeeId: 'EMP001',\n      designation: 'Software Engineer',\n      region: 'Tamil Nadu Civil Supplies Corporation',\n      joiningDate: new Date('2023-01-15'),\n      workingDays: 365,\n      dateOfBirth: new Date('1995-06-10'),\n      checklist: [\n        { name: 'First Page Record Sheet', status: 'YES', required: true },\n        { name: 'Degree Certificate', status: 'YES', required: true },\n        { name: 'Driving Certificate', status: 'YES', required: false },\n        { name: '10th Mark Sheet', status: 'YES', required: true },\n        { name: 'Transfer Certificate', status: 'YES', required: false },\n        { name: 'Community Certificate', status: 'YES', required: false },\n        { name: 'Employment Card', status: 'YES', required: false },\n        { name: 'Aadhar Card', status: 'YES', required: true },\n        { name: 'Family Card', status: 'YES', required: false },\n        { name: 'Duty Certificate', status: 'YES', required: false },\n        { name: 'Photo', status: 'YES', required: true },\n        { name: 'Bank Account Details', status: 'YES', required: true }\n      ]\n    };\n  }\n\n  getInitials(name: string): string {\n    return name.split(' ').map(n => n[0]).join('').toUpperCase();\n  }\n\n  getCompletedCount(): number {\n    return this.employee?.checklist.filter(item => item.status === 'YES').length || 0;\n  }\n\n  getPendingCount(): number {\n    return this.employee?.checklist.filter(item => item.status === 'PENDING').length || 0;\n  }\n\n  getItemStatusClass(status: string): string {\n    return `status-${status.toLowerCase()}`;\n  }\n\n  getStatusIcon(status: string): string {\n    switch (status) {\n      case 'YES': return 'check_circle';\n      case 'NO': return 'cancel';\n      case 'PENDING': return 'schedule';\n      default: return 'help';\n    }\n  }\n\n  getStatusIconClass(status: string): string {\n    return `status-${status.toLowerCase()}`;\n  }\n\n  getStatusChipClass(status: string): string {\n    switch (status) {\n      case 'YES': return 'status-eligible';\n      case 'NO': return 'status-not-eligible';\n      case 'PENDING': return 'status-pending';\n      default: return '';\n    }\n  }\n\n  updateItemStatus(index: number, status: string): void {\n    if (this.employee) {\n      this.employee.checklist[index].status = status as 'YES' | 'NO' | 'PENDING';\n    }\n  }\n}\n", "<div class=\"report-container\">\n  <div class=\"promotion-header\">\n    <h1>Suitability Report Input</h1>\n    <p>Comprehensive evaluation for promotion eligibility</p>\n  </div>\n\n  <div class=\"report-content\" *ngIf=\"employee\">\n    <!-- Employee Information Card -->\n    <mat-card class=\"navy-card employee-info-card\">\n      <mat-card-header>\n        <div class=\"employee-header\">\n          <div class=\"employee-avatar-large\">\n            {{getInitials(employee.employeeName)}}\n          </div>\n          <div class=\"employee-info\">\n            <mat-card-title>{{employee.employeeName}}</mat-card-title>\n            <mat-card-subtitle>{{employee.designation}} | {{employee.employeeId}}</mat-card-subtitle>\n            <div class=\"employee-meta\">\n              <span>Region: {{employee.region}}</span>\n              <span>Joined: {{employee.joiningDate | date:'dd/MM/yyyy'}}</span>\n              <span>{{employee.workingDays}} working days</span>\n            </div>\n          </div>\n        </div>\n      </mat-card-header>\n    </mat-card>\n\n    <!-- Report Tabs -->\n    <mat-card class=\"navy-card report-tabs-card\">\n      <mat-tab-group class=\"report-tabs\">\n        <!-- Document Checklist Tab -->\n        <mat-tab label=\"Document Checklist\">\n          <div class=\"tab-content\">\n            <div class=\"checklist-header\">\n              <h3>Required Documents Verification</h3>\n              <div class=\"checklist-stats\">\n                <mat-chip class=\"status-completed\">{{getCompletedCount()}} Completed</mat-chip>\n                <mat-chip class=\"status-pending\">{{getPendingCount()}} Pending</mat-chip>\n              </div>\n            </div>\n\n            <div class=\"checklist-grid\">\n              <div *ngFor=\"let item of employee.checklist\" class=\"checklist-item\">\n                <div class=\"item-header\">\n                  <span class=\"item-name\">{{item.name}}</span>\n                  <mat-chip [ngClass]=\"getChecklistStatusClass(item.status, item.required)\">\n                    {{item.status}}\n                  </mat-chip>\n                </div>\n                <div class=\"item-controls\">\n                  <mat-radio-group [(ngModel)]=\"item.status\" class=\"status-radio-group\">\n                    <mat-radio-button value=\"YES\" class=\"status-yes\">YES</mat-radio-button>\n                    <mat-radio-button value=\"NO\" class=\"status-no\">NO</mat-radio-button>\n                    <mat-radio-button value=\"PENDING\" class=\"status-pending\">PENDING</mat-radio-button>\n                  </mat-radio-group>\n                  <span class=\"required-indicator\" *ngIf=\"item.required\">*Required</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Performance Evaluation Tab -->\n        <mat-tab label=\"Performance Evaluation\">\n          <div class=\"tab-content\">\n            <h3>Performance Assessment</h3>\n            \n            <div class=\"evaluation-section\">\n              <h4>Overall Performance Rating</h4>\n              <mat-radio-group [(ngModel)]=\"performanceRating\" class=\"rating-group\">\n                <mat-radio-button value=\"excellent\">Excellent (90-100%)</mat-radio-button>\n                <mat-radio-button value=\"good\">Good (75-89%)</mat-radio-button>\n                <mat-radio-button value=\"satisfactory\">Satisfactory (60-74%)</mat-radio-button>\n                <mat-radio-button value=\"needs-improvement\">Needs Improvement (Below 60%)</mat-radio-button>\n              </mat-radio-group>\n            </div>\n\n            <div class=\"evaluation-section\">\n              <h4>Key Performance Areas</h4>\n              <div class=\"performance-areas\">\n                <div class=\"performance-item\">\n                  <span>Technical Competency</span>\n                  <mat-chip class=\"score-excellent\">85%</mat-chip>\n                </div>\n                <div class=\"performance-item\">\n                  <span>Communication Skills</span>\n                  <mat-chip class=\"score-good\">78%</mat-chip>\n                </div>\n                <div class=\"performance-item\">\n                  <span>Leadership Qualities</span>\n                  <mat-chip class=\"score-good\">82%</mat-chip>\n                </div>\n                <div class=\"performance-item\">\n                  <span>Problem Solving</span>\n                  <mat-chip class=\"score-excellent\">88%</mat-chip>\n                </div>\n                <div class=\"performance-item\">\n                  <span>Team Collaboration</span>\n                  <mat-chip class=\"score-excellent\">90%</mat-chip>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"evaluation-section\">\n              <h4>Behavioral Assessment</h4>\n              <div class=\"behavioral-checklist\">\n                <mat-checkbox [(ngModel)]=\"behavioralTraits.punctuality\">Punctuality and Attendance</mat-checkbox>\n                <mat-checkbox [(ngModel)]=\"behavioralTraits.integrity\">Integrity and Ethics</mat-checkbox>\n                <mat-checkbox [(ngModel)]=\"behavioralTraits.initiative\">Initiative and Proactiveness</mat-checkbox>\n                <mat-checkbox [(ngModel)]=\"behavioralTraits.adaptability\">Adaptability to Change</mat-checkbox>\n                <mat-checkbox [(ngModel)]=\"behavioralTraits.customerFocus\">Customer/Stakeholder Focus</mat-checkbox>\n              </div>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Disciplinary Records Tab -->\n        <mat-tab label=\"Disciplinary Records\">\n          <div class=\"tab-content\">\n            <h3>Disciplinary History</h3>\n            \n            <div class=\"disciplinary-status\">\n              <div class=\"status-card clean-record\">\n                <h4>Clean Disciplinary Record</h4>\n                <p>No disciplinary actions or punishments recorded</p>\n                <mat-chip class=\"status-clean\">CLEAN RECORD</mat-chip>\n              </div>\n            </div>\n\n            <div class=\"punishment-history\" *ngIf=\"punishmentRecords.length > 0\">\n              <h4>Punishment Records</h4>\n              <table mat-table [dataSource]=\"punishmentRecords\" class=\"punishment-table\">\n                <ng-container matColumnDef=\"date\">\n                  <th mat-header-cell *matHeaderCellDef>Date</th>\n                  <td mat-cell *matCellDef=\"let record\">{{record.date | date:'dd/MM/yyyy'}}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"type\">\n                  <th mat-header-cell *matHeaderCellDef>Type</th>\n                  <td mat-cell *matCellDef=\"let record\">{{record.type}}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"description\">\n                  <th mat-header-cell *matHeaderCellDef>Description</th>\n                  <td mat-cell *matCellDef=\"let record\">{{record.description}}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"status\">\n                  <th mat-header-cell *matHeaderCellDef>Status</th>\n                  <td mat-cell *matCellDef=\"let record\">\n                    <mat-chip [ngClass]=\"record.active ? 'status-active' : 'status-resolved'\">\n                      {{record.active ? 'Active' : 'Resolved'}}\n                    </mat-chip>\n                  </td>\n                </ng-container>\n\n                <tr mat-header-row *matHeaderRowDef=\"punishmentColumns\"></tr>\n                <tr mat-row *matRowDef=\"let row; columns: punishmentColumns;\"></tr>\n              </table>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Final Report Tab -->\n        <mat-tab label=\"Final Report\">\n          <div class=\"tab-content\">\n            <div class=\"final-report\">\n              <div class=\"report-summary\">\n                <h3>Promotion Recommendation</h3>\n                <div class=\"recommendation-card\">\n                  <div class=\"recommendation-status eligible\">\n                    <span>RECOMMENDED FOR PROMOTION</span>\n                  </div>\n                  <p>Based on the comprehensive evaluation, the employee meets all criteria for promotion from Seasonal to Regular status.</p>\n                </div>\n              </div>\n\n              <div class=\"report-actions\">\n                <button mat-raised-button class=\"navy-button\">\n                  Print Report\n                </button>\n                <button mat-raised-button class=\"navy-button\">\n                  Download PDF\n                </button>\n                <button mat-raised-button color=\"accent\">\n                  Submit for Approval\n                </button>\n              </div>\n            </div>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAmB,0BAA0B;AACrE,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;ICyC1BC,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAXvEH,EAFJ,CAAAC,cAAA,cAAoE,cACzC,eACC;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,mBAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAA2B,0BAC6C;IAArDD,EAAA,CAAAI,gBAAA,2BAAAC,0FAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAJ,OAAA,CAAAK,MAAA,EAAAN,MAAA,MAAAC,OAAA,CAAAK,MAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAyB;IACxCN,EAAA,CAAAC,cAAA,2BAAiD;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAmB;IACvEH,EAAA,CAAAC,cAAA,4BAA+C;IAAAD,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAG,YAAA,EAAmB;IACpEH,EAAA,CAAAC,cAAA,4BAAyD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAClEF,EADkE,CAAAG,YAAA,EAAmB,EACnE;IAClBH,EAAA,CAAAc,UAAA,KAAAC,wDAAA,mBAAuD;IAE3Df,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAbsBH,EAAA,CAAAgB,SAAA,GAAa;IAAbhB,EAAA,CAAAiB,iBAAA,CAAAV,OAAA,CAAAW,IAAA,CAAa;IAC3BlB,EAAA,CAAAgB,SAAA,EAA+D;IAA/DhB,EAAA,CAAAmB,UAAA,YAAAC,MAAA,CAAAC,uBAAA,CAAAd,OAAA,CAAAK,MAAA,EAAAL,OAAA,CAAAe,QAAA,EAA+D;IACvEtB,EAAA,CAAAgB,SAAA,EACF;IADEhB,EAAA,CAAAuB,kBAAA,MAAAhB,OAAA,CAAAK,MAAA,MACF;IAGiBZ,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAwB,gBAAA,YAAAjB,OAAA,CAAAK,MAAA,CAAyB;IAKRZ,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAmB,UAAA,SAAAZ,OAAA,CAAAe,QAAA,CAAmB;;;;;IA8ErDtB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC/CH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAmC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAxCH,EAAA,CAAAgB,SAAA,EAAmC;IAAnChB,EAAA,CAAAiB,iBAAA,CAAAjB,EAAA,CAAAyB,WAAA,OAAAC,SAAA,CAAAC,IAAA,gBAAmC;;;;;IAIzE3B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC/CH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAApBH,EAAA,CAAAgB,SAAA,EAAe;IAAfhB,EAAA,CAAAiB,iBAAA,CAAAW,SAAA,CAAAC,IAAA,CAAe;;;;;IAIrD7B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACtDH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA3BH,EAAA,CAAAgB,SAAA,EAAsB;IAAtBhB,EAAA,CAAAiB,iBAAA,CAAAa,SAAA,CAAAC,WAAA,CAAsB;;;;;IAI5D/B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAsC,mBACsC;IACxED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACR;;;;IAHOH,EAAA,CAAAgB,SAAA,EAA+D;IAA/DhB,EAAA,CAAAmB,UAAA,YAAAa,SAAA,CAAAC,MAAA,uCAA+D;IACvEjC,EAAA,CAAAgB,SAAA,EACF;IADEhB,EAAA,CAAAuB,kBAAA,MAAAS,SAAA,CAAAC,MAAA,8BACF;;;;;IAIJjC,EAAA,CAAAkC,SAAA,aAA6D;;;;;IAC7DlC,EAAA,CAAAkC,SAAA,aAAmE;;;;;IA3BrElC,EADF,CAAAC,cAAA,cAAqE,SAC/D;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAmC,uBAAA,OAAkC;IAEhCnC,EADA,CAAAc,UAAA,IAAAsB,sDAAA,iBAAsC,IAAAC,sDAAA,iBACA;;IAGxCrC,EAAA,CAAAmC,uBAAA,OAAkC;IAEhCnC,EADA,CAAAc,UAAA,IAAAwB,sDAAA,iBAAsC,IAAAC,sDAAA,iBACA;;IAGxCvC,EAAA,CAAAmC,uBAAA,QAAyC;IAEvCnC,EADA,CAAAc,UAAA,KAAA0B,uDAAA,iBAAsC,KAAAC,uDAAA,iBACA;;IAGxCzC,EAAA,CAAAmC,uBAAA,QAAoC;IAElCnC,EADA,CAAAc,UAAA,KAAA4B,uDAAA,iBAAsC,KAAAC,uDAAA,iBACA;;IAQxC3C,EADA,CAAAc,UAAA,KAAA8B,uDAAA,iBAAwD,KAAAC,uDAAA,iBACM;IAElE7C,EADE,CAAAG,YAAA,EAAQ,EACJ;;;;IA5BaH,EAAA,CAAAgB,SAAA,GAAgC;IAAhChB,EAAA,CAAAmB,UAAA,eAAAC,MAAA,CAAA0B,iBAAA,CAAgC;IAyB3B9C,EAAA,CAAAgB,SAAA,IAAkC;IAAlChB,EAAA,CAAAmB,UAAA,oBAAAC,MAAA,CAAA2B,iBAAA,CAAkC;IACrB/C,EAAA,CAAAgB,SAAA,EAA2B;IAA3BhB,EAAA,CAAAmB,UAAA,qBAAAC,MAAA,CAAA2B,iBAAA,CAA2B;;;;;;IAlJlE/C,EALR,CAAAC,cAAA,aAA6C,kBAEI,sBAC5B,aACc,aACQ;IACjCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,aAA2B,qBACT;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAC1DH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAoB;IAEvFH,EADF,CAAAC,cAAA,cAA2B,YACnB;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAoD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjEH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAKrDF,EALqD,CAAAG,YAAA,EAAO,EAC9C,EACF,EACF,EACU,EACT;IASDH,EANV,CAAAC,cAAA,mBAA6C,yBACR,mBAEG,eACT,eACO,UACxB;IAAAD,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEtCH,EADF,CAAAC,cAAA,eAA6B,oBACQ;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/EH,EAAA,CAAAC,cAAA,oBAAiC;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAElEF,EAFkE,CAAAG,YAAA,EAAW,EACrE,EACF;IAENH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAc,UAAA,KAAAkC,gDAAA,mBAAoE;IAkB1EhD,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;IAKNH,EAFJ,CAAAC,cAAA,mBAAwC,eACb,UACnB;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG7BH,EADF,CAAAC,cAAA,eAAgC,UAC1B;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,2BAAsE;IAArDD,EAAA,CAAAI,gBAAA,2BAAA6C,oFAAA3C,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA0C,GAAA;MAAA,MAAA9B,MAAA,GAAApB,EAAA,CAAAmD,aAAA;MAAAnD,EAAA,CAAAW,kBAAA,CAAAS,MAAA,CAAAgC,iBAAA,EAAA9C,MAAA,MAAAc,MAAA,CAAAgC,iBAAA,GAAA9C,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAC9CN,EAAA,CAAAC,cAAA,4BAAoC;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAmB;IAC1EH,EAAA,CAAAC,cAAA,4BAA+B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAmB;IAC/DH,EAAA,CAAAC,cAAA,4BAAuC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAmB;IAC/EH,EAAA,CAAAC,cAAA,4BAA4C;IAAAD,EAAA,CAAAE,MAAA,qCAA6B;IAE7EF,EAF6E,CAAAG,YAAA,EAAmB,EAC5E,EACd;IAGJH,EADF,CAAAC,cAAA,eAAgC,UAC1B;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG1BH,EAFJ,CAAAC,cAAA,eAA+B,eACC,YACtB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,oBAAkC;IAAAD,EAAA,CAAAE,MAAA,WAAG;IACvCF,EADuC,CAAAG,YAAA,EAAW,EAC5C;IAEJH,EADF,CAAAC,cAAA,eAA8B,YACtB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,oBAA6B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAClCF,EADkC,CAAAG,YAAA,EAAW,EACvC;IAEJH,EADF,CAAAC,cAAA,eAA8B,YACtB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,oBAA6B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAClCF,EADkC,CAAAG,YAAA,EAAW,EACvC;IAEJH,EADF,CAAAC,cAAA,eAA8B,YACtB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAC,cAAA,oBAAkC;IAAAD,EAAA,CAAAE,MAAA,WAAG;IACvCF,EADuC,CAAAG,YAAA,EAAW,EAC5C;IAEJH,EADF,CAAAC,cAAA,eAA8B,YACtB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/BH,EAAA,CAAAC,cAAA,oBAAkC;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAG3CF,EAH2C,CAAAG,YAAA,EAAW,EAC5C,EACF,EACF;IAGJH,EADF,CAAAC,cAAA,eAAgC,UAC1B;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5BH,EADF,CAAAC,cAAA,eAAkC,wBACyB;IAA3CD,EAAA,CAAAI,gBAAA,2BAAAiD,iFAAA/C,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA0C,GAAA;MAAA,MAAA9B,MAAA,GAAApB,EAAA,CAAAmD,aAAA;MAAAnD,EAAA,CAAAW,kBAAA,CAAAS,MAAA,CAAAkC,gBAAA,CAAAC,WAAA,EAAAjD,MAAA,MAAAc,MAAA,CAAAkC,gBAAA,CAAAC,WAAA,GAAAjD,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAA0C;IAACN,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAe;IAClGH,EAAA,CAAAC,cAAA,wBAAuD;IAAzCD,EAAA,CAAAI,gBAAA,2BAAAoD,iFAAAlD,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA0C,GAAA;MAAA,MAAA9B,MAAA,GAAApB,EAAA,CAAAmD,aAAA;MAAAnD,EAAA,CAAAW,kBAAA,CAAAS,MAAA,CAAAkC,gBAAA,CAAAG,SAAA,EAAAnD,MAAA,MAAAc,MAAA,CAAAkC,gBAAA,CAAAG,SAAA,GAAAnD,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAwC;IAACN,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAe;IAC1FH,EAAA,CAAAC,cAAA,wBAAwD;IAA1CD,EAAA,CAAAI,gBAAA,2BAAAsD,iFAAApD,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA0C,GAAA;MAAA,MAAA9B,MAAA,GAAApB,EAAA,CAAAmD,aAAA;MAAAnD,EAAA,CAAAW,kBAAA,CAAAS,MAAA,CAAAkC,gBAAA,CAAAK,UAAA,EAAArD,MAAA,MAAAc,MAAA,CAAAkC,gBAAA,CAAAK,UAAA,GAAArD,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAyC;IAACN,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAe;IACnGH,EAAA,CAAAC,cAAA,wBAA0D;IAA5CD,EAAA,CAAAI,gBAAA,2BAAAwD,iFAAAtD,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA0C,GAAA;MAAA,MAAA9B,MAAA,GAAApB,EAAA,CAAAmD,aAAA;MAAAnD,EAAA,CAAAW,kBAAA,CAAAS,MAAA,CAAAkC,gBAAA,CAAAO,YAAA,EAAAvD,MAAA,MAAAc,MAAA,CAAAkC,gBAAA,CAAAO,YAAA,GAAAvD,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAA2C;IAACN,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAe;IAC/FH,EAAA,CAAAC,cAAA,wBAA2D;IAA7CD,EAAA,CAAAI,gBAAA,2BAAA0D,iFAAAxD,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA0C,GAAA;MAAA,MAAA9B,MAAA,GAAApB,EAAA,CAAAmD,aAAA;MAAAnD,EAAA,CAAAW,kBAAA,CAAAS,MAAA,CAAAkC,gBAAA,CAAAS,aAAA,EAAAzD,MAAA,MAAAc,MAAA,CAAAkC,gBAAA,CAAAS,aAAA,GAAAzD,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAA4C;IAACN,EAAA,CAAAE,MAAA,kCAA0B;IAI7FF,EAJ6F,CAAAG,YAAA,EAAe,EAChG,EACF,EACF,EACE;IAKNH,EAFJ,CAAAC,cAAA,mBAAsC,eACX,UACnB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIzBH,EAFJ,CAAAC,cAAA,eAAiC,eACO,UAChC;IAAAD,EAAA,CAAAE,MAAA,iCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,UAAG;IAAAD,EAAA,CAAAE,MAAA,wDAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,qBAA+B;IAAAD,EAAA,CAAAE,MAAA,qBAAY;IAE/CF,EAF+C,CAAAG,YAAA,EAAW,EAClD,EACF;IAENH,EAAA,CAAAc,UAAA,MAAAkD,iDAAA,mBAAqE;IAgCzEhE,EADE,CAAAG,YAAA,EAAM,EACE;IAOFH,EAJR,CAAAC,cAAA,oBAA8B,gBACH,gBACG,gBACI,WACtB;IAAAD,EAAA,CAAAE,MAAA,iCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG7BH,EAFJ,CAAAC,cAAA,gBAAiC,gBACa,aACpC;IAAAD,EAAA,CAAAE,MAAA,kCAAyB;IACjCF,EADiC,CAAAG,YAAA,EAAO,EAClC;IACNH,EAAA,CAAAC,cAAA,UAAG;IAAAD,EAAA,CAAAE,MAAA,8HAAqH;IAE5HF,EAF4H,CAAAG,YAAA,EAAI,EACxH,EACF;IAGJH,EADF,CAAAC,cAAA,gBAA4B,mBACoB;IAC5CD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAA8C;IAC5CD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAyC;IACvCD,EAAA,CAAAE,MAAA,8BACF;IAOdF,EAPc,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACE,EACI,EACP,EACP;;;;IArLIH,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAuB,kBAAA,MAAAH,MAAA,CAAA6C,WAAA,CAAA7C,MAAA,CAAA8C,QAAA,CAAAC,YAAA,OACF;IAEkBnE,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAiB,iBAAA,CAAAG,MAAA,CAAA8C,QAAA,CAAAC,YAAA,CAAyB;IACtBnE,EAAA,CAAAgB,SAAA,GAAkD;IAAlDhB,EAAA,CAAAoE,kBAAA,KAAAhD,MAAA,CAAA8C,QAAA,CAAAG,WAAA,SAAAjD,MAAA,CAAA8C,QAAA,CAAAI,UAAA,KAAkD;IAE7DtE,EAAA,CAAAgB,SAAA,GAA2B;IAA3BhB,EAAA,CAAAuB,kBAAA,aAAAH,MAAA,CAAA8C,QAAA,CAAAK,MAAA,KAA2B;IAC3BvE,EAAA,CAAAgB,SAAA,GAAoD;IAApDhB,EAAA,CAAAuB,kBAAA,aAAAvB,EAAA,CAAAyB,WAAA,SAAAL,MAAA,CAAA8C,QAAA,CAAAM,WAAA,oBAAoD;IACpDxE,EAAA,CAAAgB,SAAA,GAAqC;IAArChB,EAAA,CAAAuB,kBAAA,KAAAH,MAAA,CAAA8C,QAAA,CAAAO,WAAA,kBAAqC;IAgBNzE,EAAA,CAAAgB,SAAA,IAAiC;IAAjChB,EAAA,CAAAuB,kBAAA,KAAAH,MAAA,CAAAsD,iBAAA,iBAAiC;IACnC1E,EAAA,CAAAgB,SAAA,GAA6B;IAA7BhB,EAAA,CAAAuB,kBAAA,KAAAH,MAAA,CAAAuD,eAAA,eAA6B;IAK1C3E,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAmB,UAAA,YAAAC,MAAA,CAAA8C,QAAA,CAAAU,SAAA,CAAqB;IA2B1B5E,EAAA,CAAAgB,SAAA,GAA+B;IAA/BhB,EAAA,CAAAwB,gBAAA,YAAAJ,MAAA,CAAAgC,iBAAA,CAA+B;IAqChCpD,EAAA,CAAAgB,SAAA,IAA0C;IAA1ChB,EAAA,CAAAwB,gBAAA,YAAAJ,MAAA,CAAAkC,gBAAA,CAAAC,WAAA,CAA0C;IAC1CvD,EAAA,CAAAgB,SAAA,GAAwC;IAAxChB,EAAA,CAAAwB,gBAAA,YAAAJ,MAAA,CAAAkC,gBAAA,CAAAG,SAAA,CAAwC;IACxCzD,EAAA,CAAAgB,SAAA,GAAyC;IAAzChB,EAAA,CAAAwB,gBAAA,YAAAJ,MAAA,CAAAkC,gBAAA,CAAAK,UAAA,CAAyC;IACzC3D,EAAA,CAAAgB,SAAA,GAA2C;IAA3ChB,EAAA,CAAAwB,gBAAA,YAAAJ,MAAA,CAAAkC,gBAAA,CAAAO,YAAA,CAA2C;IAC3C7D,EAAA,CAAAgB,SAAA,GAA4C;IAA5ChB,EAAA,CAAAwB,gBAAA,YAAAJ,MAAA,CAAAkC,gBAAA,CAAAS,aAAA,CAA4C;IAmB7B/D,EAAA,CAAAgB,SAAA,IAAkC;IAAlChB,EAAA,CAAAmB,UAAA,SAAAC,MAAA,CAAA0B,iBAAA,CAAA+B,MAAA,KAAkC;;;AD3F/E,OAAM,MAAOC,0BAA0B;EASrCC,YAAoBC,KAAqB;IAArB,KAAAA,KAAK,GAALA,KAAK;IARzB,KAAAd,QAAQ,GAA0B,IAAI;IACtC,KAAAnB,iBAAiB,GAAa,CAAC,aAAa,EAAE,QAAQ,CAAC;IACvD,KAAAkC,cAAc,GAAG,CACf;MAAElD,WAAW,EAAE,+CAA+C;MAAEnB,MAAM,EAAE;IAAU,CAAE,EACpF;MAAEmB,WAAW,EAAE,gDAAgD;MAAEnB,MAAM,EAAE;IAAU,CAAE,EACrF;MAAEmB,WAAW,EAAE,4CAA4C;MAAEnB,MAAM,EAAE;IAAU,CAAE,CAClF;EAE2C;EAE5CsE,QAAQA,CAAA;IACN,MAAMZ,UAAU,GAAG,IAAI,CAACU,KAAK,CAACG,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACzD,IAAI,CAACC,kBAAkB,CAAChB,UAAU,CAAC;EACrC;EAEAgB,kBAAkBA,CAACC,EAAiB;IAClC;IACA,IAAI,CAACrB,QAAQ,GAAG;MACdqB,EAAE,EAAE,CAAC;MACLpB,YAAY,EAAE,aAAa;MAC3BG,UAAU,EAAE,QAAQ;MACpBD,WAAW,EAAE,mBAAmB;MAChCE,MAAM,EAAE,uCAAuC;MAC/CC,WAAW,EAAE,IAAIgB,IAAI,CAAC,YAAY,CAAC;MACnCf,WAAW,EAAE,GAAG;MAChBgB,WAAW,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MACnCZ,SAAS,EAAE,CACT;QAAE1D,IAAI,EAAE,yBAAyB;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAI,CAAE,EAClE;QAAEJ,IAAI,EAAE,oBAAoB;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAI,CAAE,EAC7D;QAAEJ,IAAI,EAAE,qBAAqB;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAK,CAAE,EAC/D;QAAEJ,IAAI,EAAE,iBAAiB;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAI,CAAE,EAC1D;QAAEJ,IAAI,EAAE,sBAAsB;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAK,CAAE,EAChE;QAAEJ,IAAI,EAAE,uBAAuB;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAK,CAAE,EACjE;QAAEJ,IAAI,EAAE,iBAAiB;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAK,CAAE,EAC3D;QAAEJ,IAAI,EAAE,aAAa;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAI,CAAE,EACtD;QAAEJ,IAAI,EAAE,aAAa;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAK,CAAE,EACvD;QAAEJ,IAAI,EAAE,kBAAkB;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAK,CAAE,EAC5D;QAAEJ,IAAI,EAAE,OAAO;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAI,CAAE,EAChD;QAAEJ,IAAI,EAAE,sBAAsB;QAAEN,MAAM,EAAE,KAAK;QAAEU,QAAQ,EAAE;MAAI,CAAE;KAElE;EACH;EAEA2C,WAAWA,CAAC/C,IAAY;IACtB,OAAOA,IAAI,CAACwE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,EAAE;EAC9D;EAEApB,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACR,QAAQ,EAAEU,SAAS,CAACmB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACpF,MAAM,KAAK,KAAK,CAAC,CAACiE,MAAM,IAAI,CAAC;EACnF;EAEAF,eAAeA,CAAA;IACb,OAAO,IAAI,CAACT,QAAQ,EAAEU,SAAS,CAACmB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACpF,MAAM,KAAK,SAAS,CAAC,CAACiE,MAAM,IAAI,CAAC;EACvF;EAEAoB,kBAAkBA,CAACrF,MAAc;IAC/B,OAAO,UAAUA,MAAM,CAACsF,WAAW,EAAE,EAAE;EACzC;EAEAC,aAAaA,CAACvF,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,KAAK;QAAE,OAAO,cAAc;MACjC,KAAK,IAAI;QAAE,OAAO,QAAQ;MAC1B,KAAK,SAAS;QAAE,OAAO,UAAU;MACjC;QAAS,OAAO,MAAM;IACxB;EACF;EAEAwF,kBAAkBA,CAACxF,MAAc;IAC/B,OAAO,UAAUA,MAAM,CAACsF,WAAW,EAAE,EAAE;EACzC;EAEAG,kBAAkBA,CAACzF,MAAc;IAC/B,QAAQA,MAAM;MACZ,KAAK,KAAK;QAAE,OAAO,iBAAiB;MACpC,KAAK,IAAI;QAAE,OAAO,qBAAqB;MACvC,KAAK,SAAS;QAAE,OAAO,gBAAgB;MACvC;QAAS,OAAO,EAAE;IACpB;EACF;EAEA0F,gBAAgBA,CAACC,KAAa,EAAE3F,MAAc;IAC5C,IAAI,IAAI,CAACsD,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACU,SAAS,CAAC2B,KAAK,CAAC,CAAC3F,MAAM,GAAGA,MAAkC;IAC5E;EACF;;;uCAtFWkE,0BAA0B,EAAA9E,EAAA,CAAAwG,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA1B5B,0BAA0B;MAAA6B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpCnCjH,EAFJ,CAAAC,cAAA,aAA8B,aACE,SACxB;UAAAD,EAAA,CAAAE,MAAA,+BAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,yDAAkD;UACvDF,EADuD,CAAAG,YAAA,EAAI,EACrD;UAENH,EAAA,CAAAc,UAAA,IAAAqG,yCAAA,oBAA6C;UA4L/CnH,EAAA,CAAAG,YAAA,EAAM;;;UA5LyBH,EAAA,CAAAgB,SAAA,GAAc;UAAdhB,EAAA,CAAAmB,UAAA,SAAA+F,GAAA,CAAAhD,QAAA,CAAc;;;qBDezC/E,YAAY,EAAAiI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EACZpI,aAAa,EAAAqI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,aAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EACbxI,eAAe,EAAAyI,EAAA,CAAAC,SAAA,EACfzI,aAAa,EACbC,cAAc,EAAAyI,EAAA,CAAAC,OAAA,EACdzI,aAAa,EAAA0I,EAAA,CAAAC,MAAA,EAAAD,EAAA,CAAAE,WAAA,EACb3I,cAAc,EAAA4I,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA,EACdrJ,iBAAiB,EAAAsJ,EAAA,CAAAC,WAAA,EACjBtJ,cAAc,EAAAuJ,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,cAAA,EACdxJ,eAAe,EACfC,kBAAkB,EAClBC,YAAY,EACZC,WAAW,EAAAsJ,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}