{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ViewContainerRef, ANIMATION_MODULE_TYPE, NgZone, ElementRef, Renderer2, EventEmitter, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ContentChild, ViewChild, ChangeDetectorRef, HostAttributeToken, numberAttribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport { TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { _IdGenerator, FocusMonitor, FocusKeyManager } from '@angular/cdk/a11y';\nimport { startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { UniqueSelectionDispatcher } from '@angular/cdk/collections';\nimport { DOCUMENT } from '@angular/common';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/bidi';\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst _c0 = [\"body\"];\nconst _c1 = [\"bodyWrapper\"];\nconst _c2 = [[[\"mat-expansion-panel-header\"]], \"*\", [[\"mat-action-row\"]]];\nconst _c3 = [\"mat-expansion-panel-header\", \"*\", \"mat-action-row\"];\nfunction MatExpansionPanel_ng_template_7_Template(rf, ctx) {}\nconst _c4 = [[[\"mat-panel-title\"]], [[\"mat-panel-description\"]], \"*\"];\nconst _c5 = [\"mat-panel-title\", \"mat-panel-description\", \"*\"];\nfunction MatExpansionPanelHeader_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 2);\n    i0.ɵɵelement(2, \"path\", 3);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst MAT_ACCORDION = /*#__PURE__*/new InjectionToken('MAT_ACCORDION');\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = /*#__PURE__*/new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nlet MatExpansionPanelContent = /*#__PURE__*/(() => {\n  class MatExpansionPanelContent {\n    _template = inject(TemplateRef);\n    _expansionPanel = inject(MAT_EXPANSION_PANEL, {\n      optional: true\n    });\n    constructor() {}\n    static ɵfac = function MatExpansionPanelContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelContent)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelContent,\n      selectors: [[\"ng-template\", \"matExpansionPanelContent\", \"\"]]\n    });\n  }\n  return MatExpansionPanelContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nlet MatExpansionPanel = /*#__PURE__*/(() => {\n  class MatExpansionPanel extends CdkAccordionItem {\n    _viewContainerRef = inject(ViewContainerRef);\n    _animationsDisabled = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    }) === 'NoopAnimations';\n    _document = inject(DOCUMENT);\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _renderer = inject(Renderer2);\n    _cleanupTransitionEnd;\n    /** Whether the toggle indicator should be hidden. */\n    get hideToggle() {\n      return this._hideToggle || this.accordion && this.accordion.hideToggle;\n    }\n    set hideToggle(value) {\n      this._hideToggle = value;\n    }\n    _hideToggle = false;\n    /** The position of the expansion indicator. */\n    get togglePosition() {\n      return this._togglePosition || this.accordion && this.accordion.togglePosition;\n    }\n    set togglePosition(value) {\n      this._togglePosition = value;\n    }\n    _togglePosition;\n    /** An event emitted after the body's expansion animation happens. */\n    afterExpand = new EventEmitter();\n    /** An event emitted after the body's collapse animation happens. */\n    afterCollapse = new EventEmitter();\n    /** Stream that emits for changes in `@Input` properties. */\n    _inputChanges = new Subject();\n    /** Optionally defined accordion the expansion panel belongs to. */\n    accordion = inject(MAT_ACCORDION, {\n      optional: true,\n      skipSelf: true\n    });\n    /** Content that will be rendered lazily. */\n    _lazyContent;\n    /** Element containing the panel's user-provided content. */\n    _body;\n    /** Element wrapping the panel body. */\n    _bodyWrapper;\n    /** Portal holding the user's content. */\n    _portal;\n    /** ID for the associated header element. Used for a11y labelling. */\n    _headerId = inject(_IdGenerator).getId('mat-expansion-panel-header-');\n    constructor() {\n      super();\n      const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      this._expansionDispatcher = inject(UniqueSelectionDispatcher);\n      if (defaultOptions) {\n        this.hideToggle = defaultOptions.hideToggle;\n      }\n    }\n    /** Determines whether the expansion panel should have spacing between it and its siblings. */\n    _hasSpacing() {\n      if (this.accordion) {\n        return this.expanded && this.accordion.displayMode === 'default';\n      }\n      return false;\n    }\n    /** Gets the expanded state string. */\n    _getExpandedState() {\n      return this.expanded ? 'expanded' : 'collapsed';\n    }\n    /** Toggles the expanded state of the expansion panel. */\n    toggle() {\n      this.expanded = !this.expanded;\n    }\n    /** Sets the expanded state of the expansion panel to false. */\n    close() {\n      this.expanded = false;\n    }\n    /** Sets the expanded state of the expansion panel to true. */\n    open() {\n      this.expanded = true;\n    }\n    ngAfterContentInit() {\n      if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n        // Render the content as soon as the panel becomes open.\n        this.opened.pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1)).subscribe(() => {\n          this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n        });\n      }\n      this._setupAnimationEvents();\n    }\n    ngOnChanges(changes) {\n      this._inputChanges.next(changes);\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this._cleanupTransitionEnd?.();\n      this._inputChanges.complete();\n    }\n    /** Checks whether the expansion panel's content contains the currently-focused element. */\n    _containsFocus() {\n      if (this._body) {\n        const focusedElement = this._document.activeElement;\n        const bodyElement = this._body.nativeElement;\n        return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n      }\n      return false;\n    }\n    _transitionEndListener = ({\n      target,\n      propertyName\n    }) => {\n      if (target === this._bodyWrapper?.nativeElement && propertyName === 'grid-template-rows') {\n        this._ngZone.run(() => {\n          if (this.expanded) {\n            this.afterExpand.emit();\n          } else {\n            this.afterCollapse.emit();\n          }\n        });\n      }\n    };\n    _setupAnimationEvents() {\n      // This method is defined separately, because we need to\n      // disable this logic in some internal components.\n      this._ngZone.runOutsideAngular(() => {\n        if (this._animationsDisabled) {\n          this.opened.subscribe(() => this._ngZone.run(() => this.afterExpand.emit()));\n          this.closed.subscribe(() => this._ngZone.run(() => this.afterCollapse.emit()));\n        } else {\n          setTimeout(() => {\n            const element = this._elementRef.nativeElement;\n            this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this._transitionEndListener);\n            element.classList.add('mat-expansion-panel-animations-enabled');\n          }, 200);\n        }\n      });\n    }\n    static ɵfac = function MatExpansionPanel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanel)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatExpansionPanel,\n      selectors: [[\"mat-expansion-panel\"]],\n      contentQueries: function MatExpansionPanel_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelContent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n        }\n      },\n      viewQuery: function MatExpansionPanel_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._body = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._bodyWrapper = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-expansion-panel\"],\n      hostVars: 4,\n      hostBindings: function MatExpansionPanel_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-expanded\", ctx.expanded)(\"mat-expansion-panel-spacing\", ctx._hasSpacing());\n        }\n      },\n      inputs: {\n        hideToggle: [2, \"hideToggle\", \"hideToggle\", booleanAttribute],\n        togglePosition: \"togglePosition\"\n      },\n      outputs: {\n        afterExpand: \"afterExpand\",\n        afterCollapse: \"afterCollapse\"\n      },\n      exportAs: [\"matExpansionPanel\"],\n      features: [i0.ɵɵProvidersFeature([\n      // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n      // to the same accordion.\n      {\n        provide: MAT_ACCORDION,\n        useValue: undefined\n      }, {\n        provide: MAT_EXPANSION_PANEL,\n        useExisting: MatExpansionPanel\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c3,\n      decls: 9,\n      vars: 4,\n      consts: [[\"bodyWrapper\", \"\"], [\"body\", \"\"], [1, \"mat-expansion-panel-content-wrapper\"], [\"role\", \"region\", 1, \"mat-expansion-panel-content\", 3, \"id\"], [1, \"mat-expansion-panel-body\"], [3, \"cdkPortalOutlet\"]],\n      template: function MatExpansionPanel_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 2, 0)(3, \"div\", 3, 1)(5, \"div\", 4);\n          i0.ɵɵprojection(6, 1);\n          i0.ɵɵtemplate(7, MatExpansionPanel_ng_template_7_Template, 0, 0, \"ng-template\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(8, 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"inert\", ctx.expanded ? null : \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"aria-labelledby\", ctx._headerId);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._portal);\n        }\n      },\n      dependencies: [CdkPortalOutlet],\n      styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;position:relative;background:var(--mat-expansion-container-background-color, var(--mat-sys-surface));color:var(--mat-expansion-container-text-color, var(--mat-sys-on-surface));border-radius:var(--mat-expansion-container-shape, 12px)}.mat-expansion-panel.mat-expansion-panel-animations-enabled{transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape, 12px);border-top-left-radius:var(--mat-expansion-container-shape, 12px)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape, 12px);border-bottom-left-radius:var(--mat-expansion-container-shape, 12px)}@media(forced-colors: active){.mat-expansion-panel{outline:solid 1px}}.mat-expansion-panel-content-wrapper{display:grid;grid-template-rows:0fr;grid-template-columns:100%}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content-wrapper{transition:grid-template-rows 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{grid-template-rows:1fr}@supports not (grid-template-rows: 0fr){.mat-expansion-panel-content-wrapper{height:0}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper{height:auto}}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;min-height:0;visibility:hidden;font-family:var(--mat-expansion-container-text-font, var(--mat-sys-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-sys-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-sys-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-sys-body-large-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-content{transition:visibility 190ms linear}.mat-expansion-panel.mat-expanded>.mat-expansion-panel-content-wrapper>.mat-expansion-panel-content{visibility:visible}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-sys-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatExpansionPanel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nlet MatExpansionPanelActionRow = /*#__PURE__*/(() => {\n  class MatExpansionPanelActionRow {\n    static ɵfac = function MatExpansionPanelActionRow_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelActionRow)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelActionRow,\n      selectors: [[\"mat-action-row\"]],\n      hostAttrs: [1, \"mat-action-row\"]\n    });\n  }\n  return MatExpansionPanelActionRow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nlet MatExpansionPanelHeader = /*#__PURE__*/(() => {\n  class MatExpansionPanelHeader {\n    panel = inject(MatExpansionPanel, {\n      host: true\n    });\n    _element = inject(ElementRef);\n    _focusMonitor = inject(FocusMonitor);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _parentChangeSubscription = Subscription.EMPTY;\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      const panel = this.panel;\n      const defaultOptions = inject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      const tabIndex = inject(new HostAttributeToken('tabindex'), {\n        optional: true\n      });\n      const accordionHideToggleChange = panel.accordion ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition']))) : EMPTY;\n      this.tabIndex = parseInt(tabIndex || '') || 0;\n      // Since the toggle state depends on an @Input on the panel, we\n      // need to subscribe and trigger change detection manually.\n      this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n        return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n      }))).subscribe(() => this._changeDetectorRef.markForCheck());\n      // Avoids focus being lost if the panel contained the focused element and was closed.\n      panel.closed.pipe(filter(() => panel._containsFocus())).subscribe(() => this._focusMonitor.focusVia(this._element, 'program'));\n      if (defaultOptions) {\n        this.expandedHeight = defaultOptions.expandedHeight;\n        this.collapsedHeight = defaultOptions.collapsedHeight;\n      }\n    }\n    /** Height of the header while the panel is expanded. */\n    expandedHeight;\n    /** Height of the header while the panel is collapsed. */\n    collapsedHeight;\n    /** Tab index of the header. */\n    tabIndex = 0;\n    /**\n     * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n     * @docs-private\n     */\n    get disabled() {\n      return this.panel.disabled;\n    }\n    /** Toggles the expanded state of the panel. */\n    _toggle() {\n      if (!this.disabled) {\n        this.panel.toggle();\n      }\n    }\n    /** Gets whether the panel is expanded. */\n    _isExpanded() {\n      return this.panel.expanded;\n    }\n    /** Gets the expanded state string of the panel. */\n    _getExpandedState() {\n      return this.panel._getExpandedState();\n    }\n    /** Gets the panel id. */\n    _getPanelId() {\n      return this.panel.id;\n    }\n    /** Gets the toggle position for the header. */\n    _getTogglePosition() {\n      return this.panel.togglePosition;\n    }\n    /** Gets whether the expand indicator should be shown. */\n    _showToggle() {\n      return !this.panel.hideToggle && !this.panel.disabled;\n    }\n    /**\n     * Gets the current height of the header. Null if no custom height has been\n     * specified, and if the default height from the stylesheet should be used.\n     */\n    _getHeaderHeight() {\n      const isExpanded = this._isExpanded();\n      if (isExpanded && this.expandedHeight) {\n        return this.expandedHeight;\n      } else if (!isExpanded && this.collapsedHeight) {\n        return this.collapsedHeight;\n      }\n      return null;\n    }\n    /** Handle keydown event calling to toggle() if appropriate. */\n    _keydown(event) {\n      switch (event.keyCode) {\n        // Toggle for space and enter keys.\n        case SPACE:\n        case ENTER:\n          if (!hasModifierKey(event)) {\n            event.preventDefault();\n            this._toggle();\n          }\n          break;\n        default:\n          if (this.panel.accordion) {\n            this.panel.accordion._handleHeaderKeydown(event);\n          }\n          return;\n      }\n    }\n    /**\n     * Focuses the panel header. Implemented as a part of `FocusableOption`.\n     * @param origin Origin of the action that triggered the focus.\n     * @docs-private\n     */\n    focus(origin, options) {\n      if (origin) {\n        this._focusMonitor.focusVia(this._element, origin, options);\n      } else {\n        this._element.nativeElement.focus(options);\n      }\n    }\n    ngAfterViewInit() {\n      this._focusMonitor.monitor(this._element).subscribe(origin => {\n        if (origin && this.panel.accordion) {\n          this.panel.accordion._handleHeaderFocus(this);\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._parentChangeSubscription.unsubscribe();\n      this._focusMonitor.stopMonitoring(this._element);\n    }\n    static ɵfac = function MatExpansionPanelHeader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelHeader)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatExpansionPanelHeader,\n      selectors: [[\"mat-expansion-panel-header\"]],\n      hostAttrs: [\"role\", \"button\", 1, \"mat-expansion-panel-header\", \"mat-focus-indicator\"],\n      hostVars: 13,\n      hostBindings: function MatExpansionPanelHeader_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatExpansionPanelHeader_click_HostBindingHandler() {\n            return ctx._toggle();\n          })(\"keydown\", function MatExpansionPanelHeader_keydown_HostBindingHandler($event) {\n            return ctx._keydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.panel._headerId)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx._getPanelId())(\"aria-expanded\", ctx._isExpanded())(\"aria-disabled\", ctx.panel.disabled);\n          i0.ɵɵstyleProp(\"height\", ctx._getHeaderHeight());\n          i0.ɵɵclassProp(\"mat-expanded\", ctx._isExpanded())(\"mat-expansion-toggle-indicator-after\", ctx._getTogglePosition() === \"after\")(\"mat-expansion-toggle-indicator-before\", ctx._getTogglePosition() === \"before\");\n        }\n      },\n      inputs: {\n        expandedHeight: \"expandedHeight\",\n        collapsedHeight: \"collapsedHeight\",\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n      },\n      ngContentSelectors: _c5,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"mat-content\"], [1, \"mat-expansion-indicator\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 -960 960 960\", \"aria-hidden\", \"true\", \"focusable\", \"false\"], [\"d\", \"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\"]],\n      template: function MatExpansionPanelHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c4);\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵprojection(3, 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, MatExpansionPanelHeader_Conditional_4_Template, 3, 0, \"span\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-content-hide-toggle\", !ctx._showToggle());\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(ctx._showToggle() ? 4 : -1);\n        }\n      },\n      styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;height:var(--mat-expansion-header-collapsed-state-height, 48px);font-family:var(--mat-expansion-header-text-font, var(--mat-sys-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-sys-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-sys-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-sys-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-sys-title-medium-tracking))}.mat-expansion-panel-animations-enabled .mat-expansion-panel-header{transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header::before{border-radius:inherit}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height, 64px)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-sys-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-sys-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-sys-on-surface-variant))}.mat-expansion-panel-animations-enabled .mat-expansion-indicator{transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-expansion-panel-header.mat-expanded .mat-expansion-indicator{transform:rotate(180deg)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, none)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-sys-on-surface-variant));display:var(--mat-expansion-header-indicator-display, inline-block)}@media(forced-colors: active){.mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatExpansionPanelHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nlet MatExpansionPanelDescription = /*#__PURE__*/(() => {\n  class MatExpansionPanelDescription {\n    static ɵfac = function MatExpansionPanelDescription_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelDescription)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelDescription,\n      selectors: [[\"mat-panel-description\"]],\n      hostAttrs: [1, \"mat-expansion-panel-header-description\"]\n    });\n  }\n  return MatExpansionPanelDescription;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nlet MatExpansionPanelTitle = /*#__PURE__*/(() => {\n  class MatExpansionPanelTitle {\n    static ɵfac = function MatExpansionPanelTitle_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelTitle)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelTitle,\n      selectors: [[\"mat-panel-title\"]],\n      hostAttrs: [1, \"mat-expansion-panel-header-title\"]\n    });\n  }\n  return MatExpansionPanelTitle;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Directive for a Material Design Accordion.\n */\nlet MatAccordion = /*#__PURE__*/(() => {\n  class MatAccordion extends CdkAccordion {\n    _keyManager;\n    /** Headers belonging to this accordion. */\n    _ownHeaders = new QueryList();\n    /** All headers inside the accordion. Includes headers inside nested accordions. */\n    _headers;\n    /** Whether the expansion indicator should be hidden. */\n    hideToggle = false;\n    /**\n     * Display mode used for all expansion panels in the accordion. Currently two display\n     * modes exist:\n     *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n     *     panel at a different elevation from the rest of the accordion.\n     *  flat - no spacing is placed around expanded panels, showing all panels at the same\n     *     elevation.\n     */\n    displayMode = 'default';\n    /** The position of the expansion indicator. */\n    togglePosition = 'after';\n    ngAfterContentInit() {\n      this._headers.changes.pipe(startWith(this._headers)).subscribe(headers => {\n        this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n        this._ownHeaders.notifyOnChanges();\n      });\n      this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n    }\n    /** Handles keyboard events coming in from the panel headers. */\n    _handleHeaderKeydown(event) {\n      this._keyManager.onKeydown(event);\n    }\n    _handleHeaderFocus(header) {\n      this._keyManager.updateActiveItem(header);\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this._keyManager?.destroy();\n      this._ownHeaders.destroy();\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatAccordion_BaseFactory;\n      return function MatAccordion_Factory(__ngFactoryType__) {\n        return (ɵMatAccordion_BaseFactory || (ɵMatAccordion_BaseFactory = i0.ɵɵgetInheritedFactory(MatAccordion)))(__ngFactoryType__ || MatAccordion);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatAccordion,\n      selectors: [[\"mat-accordion\"]],\n      contentQueries: function MatAccordion_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelHeader, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headers = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-accordion\"],\n      hostVars: 2,\n      hostBindings: function MatAccordion_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-accordion-multi\", ctx.multi);\n        }\n      },\n      inputs: {\n        hideToggle: [2, \"hideToggle\", \"hideToggle\", booleanAttribute],\n        displayMode: \"displayMode\",\n        togglePosition: \"togglePosition\"\n      },\n      exportAs: [\"matAccordion\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_ACCORDION,\n        useExisting: MatAccordion\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatAccordion;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatExpansionModule = /*#__PURE__*/(() => {\n  class MatExpansionModule {\n    static ɵfac = function MatExpansionModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatExpansionModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CdkAccordionModule, PortalModule]\n    });\n  }\n  return MatExpansionModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Time and timing curve for expansion panel animations.\n * @deprecated No longer used. Will be removed.\n * @breaking-change 21.0.0\n */\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM. This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matExpansionAnimations = {\n  // Represents:\n  // trigger('indicatorRotate', [\n  //   state('collapsed, void', style({transform: 'rotate(0deg)'})),\n  //   state('expanded', style({transform: 'rotate(180deg)'})),\n  //   transition(\n  //     'expanded <=> collapsed, void => collapsed',\n  //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n  //   ),\n  // ])\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: {\n    type: 7,\n    name: 'indicatorRotate',\n    definitions: [{\n      type: 0,\n      name: 'collapsed, void',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(0deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'expanded',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'rotate(180deg)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'expanded <=> collapsed, void => collapsed',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  },\n  // Represents:\n  // trigger('bodyExpansion', [\n  //   state('collapsed, void', style({height: '0px', visibility: 'hidden'})),\n  //   // Clear the `visibility` while open, otherwise the content will be visible when placed in\n  //   // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n  //   // that have a `visibility` of their own (see #27436).\n  //   state('expanded', style({height: '*', visibility: ''})),\n  //   transition(\n  //     'expanded <=> collapsed, void => collapsed',\n  //     animate(EXPANSION_PANEL_ANIMATION_TIMING),\n  //   ),\n  // ])\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: {\n    type: 7,\n    name: 'bodyExpansion',\n    definitions: [{\n      type: 0,\n      name: 'collapsed, void',\n      styles: {\n        type: 6,\n        styles: {\n          'height': '0px',\n          'visibility': 'hidden'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'expanded',\n      styles: {\n        type: 6,\n        styles: {\n          'height': '*',\n          'visibility': ''\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'expanded <=> collapsed, void => collapsed',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '225ms cubic-bezier(0.4,0.0,0.2,1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };\n//# sourceMappingURL=expansion.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}