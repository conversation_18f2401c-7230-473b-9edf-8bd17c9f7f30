{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/toolbar\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/menu\";\nexport let HeaderComponent = /*#__PURE__*/(() => {\n  class HeaderComponent {\n    constructor() {\n      this.toggleSidebar = new EventEmitter();\n    }\n    onToggleSidebar() {\n      this.toggleSidebar.emit();\n    }\n    onLogout() {\n      // Implementation for logout\n      console.log('Logout clicked');\n    }\n    onProfile() {\n      // Implementation for profile\n      console.log('Profile clicked');\n    }\n    static {\n      this.ɵfac = function HeaderComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HeaderComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HeaderComponent,\n        selectors: [[\"app-header\"]],\n        outputs: {\n          toggleSidebar: \"toggleSidebar\"\n        },\n        decls: 18,\n        vars: 1,\n        consts: [[\"userMenu\", \"matMenu\"], [1, \"header-toolbar\"], [1, \"header-left\"], [\"mat-button\", \"\", 1, \"menu-toggle\", 3, \"click\"], [1, \"app-title\"], [1, \"header-right\"], [\"mat-button\", \"\", 1, \"user-menu-trigger\", 3, \"matMenuTriggerFor\"], [1, \"user-name\"], [1, \"user-avatar\"], [\"mat-menu-item\", \"\", 3, \"click\"]],\n        template: function HeaderComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"mat-toolbar\", 1)(1, \"div\", 2)(2, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_2_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onToggleSidebar());\n            });\n            i0.ɵɵtext(3, \" \\u2630 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"h1\", 4);\n            i0.ɵɵtext(5, \"HRMS Promotion System\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"button\", 6)(8, \"span\", 7);\n            i0.ɵɵtext(9, \"Admin User\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"span\", 8);\n            i0.ɵɵtext(11, \"\\uD83D\\uDC64\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"mat-menu\", null, 0)(14, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_14_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onProfile());\n            });\n            i0.ɵɵtext(15, \" Profile \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onLogout());\n            });\n            i0.ɵɵtext(17, \" Logout \");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            const userMenu_r2 = i0.ɵɵreference(13);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r2);\n          }\n        },\n        dependencies: [CommonModule, MatToolbarModule, i1.MatToolbar, MatButtonModule, i2.MatButton, MatMenuModule, i3.MatMenu, i3.MatMenuItem, i3.MatMenuTrigger, RouterModule],\n        styles: [\".header-toolbar[_ngcontent-%COMP%]{background-color:#283163;color:#fff;position:fixed;top:0;left:0;right:0;z-index:1000;display:flex;justify-content:space-between;align-items:center;padding:0 20px;height:64px}.header-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:15px}.menu-toggle[_ngcontent-%COMP%]{background:none;border:none;color:#fff;font-size:18px;cursor:pointer;padding:8px;border-radius:4px;transition:background-color .3s ease}.menu-toggle[_ngcontent-%COMP%]:hover{background-color:#ffffff1a}.app-title[_ngcontent-%COMP%]{margin:0;font-size:20px;font-weight:500}.header-right[_ngcontent-%COMP%]{display:flex;align-items:center}.user-menu-trigger[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;color:#fff;background:none;border:none;padding:8px 12px;border-radius:4px;cursor:pointer;transition:background-color .3s ease}.user-menu-trigger[_ngcontent-%COMP%]:hover{background-color:#ffffff1a}.user-name[_ngcontent-%COMP%]{font-size:14px}.user-avatar[_ngcontent-%COMP%]{font-size:20px}@media (max-width: 768px){.app-title[_ngcontent-%COMP%]{font-size:16px}.user-name[_ngcontent-%COMP%]{display:none}}\"]\n      });\n    }\n  }\n  return HeaderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}