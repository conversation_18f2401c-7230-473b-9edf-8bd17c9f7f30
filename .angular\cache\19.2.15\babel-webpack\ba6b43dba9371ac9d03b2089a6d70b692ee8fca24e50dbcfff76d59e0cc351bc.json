{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Injector, EnvironmentInjector, ApplicationRef, createComponent, Injectable } from '@angular/core';\n\n/** Apps in which we've loaded styles. */\nconst appsWithLoaders = /*#__PURE__*/new WeakMap();\n/**\n * Service that loads structural styles dynamically\n * and ensures that they're only loaded once per app.\n */\nlet _CdkPrivateStyleLoader = /*#__PURE__*/(() => {\n  class _CdkPrivateStyleLoader {\n    _appRef;\n    _injector = inject(Injector);\n    _environmentInjector = inject(EnvironmentInjector);\n    /**\n     * Loads a set of styles.\n     * @param loader Component which will be instantiated to load the styles.\n     */\n    load(loader) {\n      // Resolve the app ref lazily to avoid circular dependency errors if this is called too early.\n      const appRef = this._appRef = this._appRef || this._injector.get(ApplicationRef);\n      let data = appsWithLoaders.get(appRef);\n      // If we haven't loaded for this app before, we have to initialize it.\n      if (!data) {\n        data = {\n          loaders: new Set(),\n          refs: []\n        };\n        appsWithLoaders.set(appRef, data);\n        // When the app is destroyed, we need to clean up all the related loaders.\n        appRef.onDestroy(() => {\n          appsWithLoaders.get(appRef)?.refs.forEach(ref => ref.destroy());\n          appsWithLoaders.delete(appRef);\n        });\n      }\n      // If the loader hasn't been loaded before, we need to instatiate it.\n      if (!data.loaders.has(loader)) {\n        data.loaders.add(loader);\n        data.refs.push(createComponent(loader, {\n          environmentInjector: this._environmentInjector\n        }));\n      }\n    }\n    static ɵfac = function _CdkPrivateStyleLoader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _CdkPrivateStyleLoader)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: _CdkPrivateStyleLoader,\n      factory: _CdkPrivateStyleLoader.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return _CdkPrivateStyleLoader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { _CdkPrivateStyleLoader as _ };\n//# sourceMappingURL=style-loader-Cu9AvjH9.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}