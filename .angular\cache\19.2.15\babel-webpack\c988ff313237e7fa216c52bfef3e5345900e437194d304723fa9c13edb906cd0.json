{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, booleanAttribute, Directive, Input, ChangeDetectorRef, EventEmitter, Output, NgModule } from '@angular/core';\nimport { Subject, Subscription } from 'rxjs';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { U as UniqueSelectionDispatcher } from './unique-selection-dispatcher-DtHZDqyJ.mjs';\n\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_ACCORDION = /*#__PURE__*/new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\nlet CdkAccordion = /*#__PURE__*/(() => {\n  class CdkAccordion {\n    /** Emits when the state of the accordion changes */\n    _stateChanges = new Subject();\n    /** Stream that emits true/false when openAll/closeAll is triggered. */\n    _openCloseAllActions = new Subject();\n    /** A readonly id value to use for unique selection coordination. */\n    id = inject(_IdGenerator).getId('cdk-accordion-');\n    /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n    multi = false;\n    /** Opens all enabled accordion items in an accordion where multi is enabled. */\n    openAll() {\n      if (this.multi) {\n        this._openCloseAllActions.next(true);\n      }\n    }\n    /** Closes all enabled accordion items. */\n    closeAll() {\n      this._openCloseAllActions.next(false);\n    }\n    ngOnChanges(changes) {\n      this._stateChanges.next(changes);\n    }\n    ngOnDestroy() {\n      this._stateChanges.complete();\n      this._openCloseAllActions.complete();\n    }\n    static ɵfac = function CdkAccordion_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAccordion)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkAccordion,\n      selectors: [[\"cdk-accordion\"], [\"\", \"cdkAccordion\", \"\"]],\n      inputs: {\n        multi: [2, \"multi\", \"multi\", booleanAttribute]\n      },\n      exportAs: [\"cdkAccordion\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_ACCORDION,\n        useExisting: CdkAccordion\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return CdkAccordion;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * A basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\nlet CdkAccordionItem = /*#__PURE__*/(() => {\n  class CdkAccordionItem {\n    accordion = inject(CDK_ACCORDION, {\n      optional: true,\n      skipSelf: true\n    });\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _expansionDispatcher = inject(UniqueSelectionDispatcher);\n    /** Subscription to openAll/closeAll events. */\n    _openCloseAllSubscription = Subscription.EMPTY;\n    /** Event emitted every time the AccordionItem is closed. */\n    closed = new EventEmitter();\n    /** Event emitted every time the AccordionItem is opened. */\n    opened = new EventEmitter();\n    /** Event emitted when the AccordionItem is destroyed. */\n    destroyed = new EventEmitter();\n    /**\n     * Emits whenever the expanded state of the accordion changes.\n     * Primarily used to facilitate two-way binding.\n     * @docs-private\n     */\n    expandedChange = new EventEmitter();\n    /** The unique AccordionItem id. */\n    id = inject(_IdGenerator).getId('cdk-accordion-child-');\n    /** Whether the AccordionItem is expanded. */\n    get expanded() {\n      return this._expanded;\n    }\n    set expanded(expanded) {\n      // Only emit events and update the internal value if the value changes.\n      if (this._expanded !== expanded) {\n        this._expanded = expanded;\n        this.expandedChange.emit(expanded);\n        if (expanded) {\n          this.opened.emit();\n          /**\n           * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n           * the name value is the id of the accordion.\n           */\n          const accordionId = this.accordion ? this.accordion.id : this.id;\n          this._expansionDispatcher.notify(this.id, accordionId);\n        } else {\n          this.closed.emit();\n        }\n        // Ensures that the animation will run when the value is set outside of an `@Input`.\n        // This includes cases like the open, close and toggle methods.\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    _expanded = false;\n    /** Whether the AccordionItem is disabled. */\n    disabled = false;\n    /** Unregister function for _expansionDispatcher. */\n    _removeUniqueSelectionListener = () => {};\n    constructor() {}\n    ngOnInit() {\n      this._removeUniqueSelectionListener = this._expansionDispatcher.listen((id, accordionId) => {\n        if (this.accordion && !this.accordion.multi && this.accordion.id === accordionId && this.id !== id) {\n          this.expanded = false;\n        }\n      });\n      // When an accordion item is hosted in an accordion, subscribe to open/close events.\n      if (this.accordion) {\n        this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n      }\n    }\n    /** Emits an event for the accordion item being destroyed. */\n    ngOnDestroy() {\n      this.opened.complete();\n      this.closed.complete();\n      this.destroyed.emit();\n      this.destroyed.complete();\n      this._removeUniqueSelectionListener();\n      this._openCloseAllSubscription.unsubscribe();\n    }\n    /** Toggles the expanded state of the accordion item. */\n    toggle() {\n      if (!this.disabled) {\n        this.expanded = !this.expanded;\n      }\n    }\n    /** Sets the expanded state of the accordion item to false. */\n    close() {\n      if (!this.disabled) {\n        this.expanded = false;\n      }\n    }\n    /** Sets the expanded state of the accordion item to true. */\n    open() {\n      if (!this.disabled) {\n        this.expanded = true;\n      }\n    }\n    _subscribeToOpenCloseAllActions() {\n      return this.accordion._openCloseAllActions.subscribe(expanded => {\n        // Only change expanded state if item is enabled\n        if (!this.disabled) {\n          this.expanded = expanded;\n        }\n      });\n    }\n    static ɵfac = function CdkAccordionItem_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAccordionItem)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkAccordionItem,\n      selectors: [[\"cdk-accordion-item\"], [\"\", \"cdkAccordionItem\", \"\"]],\n      inputs: {\n        expanded: [2, \"expanded\", \"expanded\", booleanAttribute],\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        closed: \"closed\",\n        opened: \"opened\",\n        destroyed: \"destroyed\",\n        expandedChange: \"expandedChange\"\n      },\n      exportAs: [\"cdkAccordionItem\"],\n      features: [i0.ɵɵProvidersFeature([\n      // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n      // registering to the same accordion.\n      {\n        provide: CDK_ACCORDION,\n        useValue: undefined\n      }])]\n    });\n  }\n  return CdkAccordionItem;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkAccordionModule = /*#__PURE__*/(() => {\n  class CdkAccordionModule {\n    static ɵfac = function CdkAccordionModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAccordionModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkAccordionModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return CdkAccordionModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { CDK_ACCORDION, CdkAccordion, CdkAccordionItem, CdkAccordionModule };\n//# sourceMappingURL=accordion.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}