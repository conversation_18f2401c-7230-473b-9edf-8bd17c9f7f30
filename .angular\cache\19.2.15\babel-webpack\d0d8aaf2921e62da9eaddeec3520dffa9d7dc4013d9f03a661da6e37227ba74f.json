{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nlet ShowOnDirtyErrorStateMatcher = /*#__PURE__*/(() => {\n  class ShowOnDirtyErrorStateMatcher {\n    isErrorState(control, form) {\n      return !!(control && control.invalid && (control.dirty || form && form.submitted));\n    }\n    static ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ShowOnDirtyErrorStateMatcher)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ShowOnDirtyErrorStateMatcher,\n      factory: ShowOnDirtyErrorStateMatcher.ɵfac\n    });\n  }\n  return ShowOnDirtyErrorStateMatcher;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nlet ErrorStateMatcher = /*#__PURE__*/(() => {\n  class ErrorStateMatcher {\n    isErrorState(control, form) {\n      return !!(control && control.invalid && (control.touched || form && form.submitted));\n    }\n    static ɵfac = function ErrorStateMatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ErrorStateMatcher)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ErrorStateMatcher,\n      factory: ErrorStateMatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ErrorStateMatcher;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { ErrorStateMatcher as E, ShowOnDirtyErrorStateMatcher as S };\n//# sourceMappingURL=error-options-Dm2JJUbF.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}