{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { HeaderComponent } from './header/header.component';\nimport { SidebarComponent } from './sidebar/sidebar.component';\nimport { FooterComponent } from './footer/footer.component';\nimport * as i0 from \"@angular/core\";\nexport let LayoutComponent = /*#__PURE__*/(() => {\n  class LayoutComponent {\n    constructor() {\n      this.sidebarOpen = true;\n    }\n    toggleSidebar() {\n      this.sidebarOpen = !this.sidebarOpen;\n    }\n    static {\n      this.ɵfac = function LayoutComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || LayoutComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LayoutComponent,\n        selectors: [[\"app-layout\"]],\n        decls: 7,\n        vars: 3,\n        consts: [[1, \"layout-container\"], [3, \"toggleSidebar\"], [1, \"main-content\"], [3, \"isOpen\"], [1, \"content-area\"]],\n        template: function LayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"app-header\", 1);\n            i0.ɵɵlistener(\"toggleSidebar\", function LayoutComponent_Template_app_header_toggleSidebar_1_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"div\", 2);\n            i0.ɵɵelement(3, \"app-sidebar\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4);\n            i0.ɵɵelement(5, \"router-outlet\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(6, \"app-footer\");\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"sidebar-open\", ctx.sidebarOpen);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"isOpen\", ctx.sidebarOpen);\n          }\n        },\n        dependencies: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent, FooterComponent],\n        styles: [\".layout-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:100vh;background-color:#f5f5f5}.main-content[_ngcontent-%COMP%]{display:flex;flex:1;transition:all .3s ease}.content-area[_ngcontent-%COMP%]{flex:1;background-color:#fff;transition:margin-left .3s ease;margin-left:0}.main-content.sidebar-open[_ngcontent-%COMP%]   .content-area[_ngcontent-%COMP%]{margin-left:250px}@media (max-width: 768px){.main-content.sidebar-open[_ngcontent-%COMP%]   .content-area[_ngcontent-%COMP%]{margin-left:0}}\"]\n      });\n    }\n  }\n  return LayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}