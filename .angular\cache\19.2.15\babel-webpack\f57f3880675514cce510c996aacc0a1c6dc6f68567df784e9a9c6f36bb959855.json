{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, inject, ChangeDetectorRef, numberAttribute, EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { j as MatFormField } from './form-field-DqPi4knt.mjs';\nimport { g as MatSelect, M as MatSelectModule } from './module-Cbt8Fcmv.mjs';\nimport { e as MatTooltip, h as MatTooltipModule } from './module-C9K6ZqpI.mjs';\nimport { M as MatOption } from './option-ChV6uQgD.mjs';\nimport { M as MatIconButton } from './icon-button-D1J0zeqv.mjs';\nimport { MatButtonModule } from './button.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/platform';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport '@angular/cdk/collections';\nimport '@angular/cdk/keycodes';\nimport '@angular/forms';\nimport './error-options-Dm2JJUbF.mjs';\nimport './error-state-Dtb1IHM-.mjs';\nimport './index-DOxJc1m4.mjs';\nimport './index-SYVYjXwK.mjs';\nimport './common-module-WayjW0Pb.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-module-CAX2sutq.mjs';\nimport './pseudo-checkbox-CJ7seqQH.mjs';\nimport './module-BXZhw7pQ.mjs';\nimport '@angular/cdk/observers';\nimport '@angular/cdk/portal';\nimport './structural-styles-BQUT6wsL.mjs';\nimport './ripple-loader-Ce3DAhPW.mjs';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nfunction MatPaginator_Conditional_2_Conditional_3_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pageSizeOption_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", pageSizeOption_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", pageSizeOption_r3, \" \");\n  }\n}\nfunction MatPaginator_Conditional_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 14)(1, \"mat-select\", 16, 0);\n    i0.ɵɵlistener(\"selectionChange\", function MatPaginator_Conditional_2_Conditional_3_Template_mat_select_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1._changePageSize($event.value));\n    });\n    i0.ɵɵrepeaterCreate(3, MatPaginator_Conditional_2_Conditional_3_For_4_Template, 2, 2, \"mat-option\", 17, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_2_Conditional_3_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const selectRef_r4 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(selectRef_r4.open());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r1._formFieldAppearance)(\"color\", ctx_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.pageSize)(\"disabled\", ctx_r1.disabled)(\"aria-labelledby\", ctx_r1._pageSizeLabelId)(\"panelClass\", ctx_r1.selectConfig.panelClass || \"\")(\"disableOptionCentering\", ctx_r1.selectConfig.disableOptionCentering);\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r1._displayedPageSizeOptions);\n  }\n}\nfunction MatPaginator_Conditional_2_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.pageSize);\n  }\n}\nfunction MatPaginator_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MatPaginator_Conditional_2_Conditional_3_Template, 6, 7, \"mat-form-field\", 14)(4, MatPaginator_Conditional_2_Conditional_4_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"id\", ctx_r1._pageSizeLabelId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1._intl.itemsPerPageLabel, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1._displayedPageSizeOptions.length > 1 ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1._displayedPageSizeOptions.length <= 1 ? 4 : -1);\n  }\n}\nfunction MatPaginator_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._buttonClicked(0, ctx_r1._previousButtonsDisabled()));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.firstPageLabel)(\"matTooltipDisabled\", ctx_r1._previousButtonsDisabled())(\"disabled\", ctx_r1._previousButtonsDisabled())(\"tabindex\", ctx_r1._previousButtonsDisabled() ? -1 : null);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.firstPageLabel);\n  }\n}\nfunction MatPaginator_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._buttonClicked(ctx_r1.getNumberOfPages() - 1, ctx_r1._nextButtonsDisabled()));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.lastPageLabel)(\"matTooltipDisabled\", ctx_r1._nextButtonsDisabled())(\"disabled\", ctx_r1._nextButtonsDisabled())(\"tabindex\", ctx_r1._nextButtonsDisabled() ? -1 : null);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.lastPageLabel);\n  }\n}\nlet MatPaginatorIntl = /*#__PURE__*/(() => {\n  class MatPaginatorIntl {\n    /**\n     * Stream to emit from when labels are changed. Use this to notify components when the labels have\n     * changed after initialization.\n     */\n    changes = new Subject();\n    /** A label for the page size selector. */\n    itemsPerPageLabel = 'Items per page:';\n    /** A label for the button that increments the current page. */\n    nextPageLabel = 'Next page';\n    /** A label for the button that decrements the current page. */\n    previousPageLabel = 'Previous page';\n    /** A label for the button that moves to the first page. */\n    firstPageLabel = 'First page';\n    /** A label for the button that moves to the last page. */\n    lastPageLabel = 'Last page';\n    /** A label for the range of items within the current page and the length of the whole list. */\n    getRangeLabel = (page, pageSize, length) => {\n      if (length == 0 || pageSize == 0) {\n        return `0 of ${length}`;\n      }\n      length = Math.max(length, 0);\n      const startIndex = page * pageSize;\n      // If the start index exceeds the list length, do not try and fix the end index to the end.\n      const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n      return `${startIndex + 1} – ${endIndex} of ${length}`;\n    };\n    static ɵfac = function MatPaginatorIntl_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPaginatorIntl)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatPaginatorIntl,\n      factory: MatPaginatorIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MatPaginatorIntl;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[/*#__PURE__*/new Optional(), /*#__PURE__*/new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {\n  /** The current page index. */\n  pageIndex;\n  /**\n   * Index of the page that was selected previously.\n   * @breaking-change 8.0.0 To be made into a required property.\n   */\n  previousPageIndex;\n  /** The current page size. */\n  pageSize;\n  /** The current total number of items being paged. */\n  length;\n}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nlet MatPaginator = /*#__PURE__*/(() => {\n  class MatPaginator {\n    _intl = inject(MatPaginatorIntl);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    /** If set, styles the \"page size\" form field with the designated style. */\n    _formFieldAppearance;\n    /** ID for the DOM node containing the paginator's items per page label. */\n    _pageSizeLabelId = inject(_IdGenerator).getId('mat-paginator-page-size-label-');\n    _intlChanges;\n    _isInitialized = false;\n    _initializedStream = new ReplaySubject(1);\n    /**\n     * Theme color of the underlying form controls. This API is supported in M2\n     * themes only,it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/paginator/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n    get pageIndex() {\n      return this._pageIndex;\n    }\n    set pageIndex(value) {\n      this._pageIndex = Math.max(value || 0, 0);\n      this._changeDetectorRef.markForCheck();\n    }\n    _pageIndex = 0;\n    /** The length of the total number of items that are being paginated. Defaulted to 0. */\n    get length() {\n      return this._length;\n    }\n    set length(value) {\n      this._length = value || 0;\n      this._changeDetectorRef.markForCheck();\n    }\n    _length = 0;\n    /** Number of items to display on a page. By default set to 50. */\n    get pageSize() {\n      return this._pageSize;\n    }\n    set pageSize(value) {\n      this._pageSize = Math.max(value || 0, 0);\n      this._updateDisplayedPageSizeOptions();\n    }\n    _pageSize;\n    /** The set of provided page size options to display to the user. */\n    get pageSizeOptions() {\n      return this._pageSizeOptions;\n    }\n    set pageSizeOptions(value) {\n      this._pageSizeOptions = (value || []).map(p => numberAttribute(p, 0));\n      this._updateDisplayedPageSizeOptions();\n    }\n    _pageSizeOptions = [];\n    /** Whether to hide the page size selection UI from the user. */\n    hidePageSize = false;\n    /** Whether to show the first/last buttons UI to the user. */\n    showFirstLastButtons = false;\n    /** Used to configure the underlying `MatSelect` inside the paginator. */\n    selectConfig = {};\n    /** Whether the paginator is disabled. */\n    disabled = false;\n    /** Event emitted when the paginator changes the page size or page index. */\n    page = new EventEmitter();\n    /** Displayed set of page size options. Will be sorted and include current page size. */\n    _displayedPageSizeOptions;\n    /** Emits when the paginator is initialized. */\n    initialized = this._initializedStream;\n    constructor() {\n      const _intl = this._intl;\n      const defaults = inject(MAT_PAGINATOR_DEFAULT_OPTIONS, {\n        optional: true\n      });\n      this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n      if (defaults) {\n        const {\n          pageSize,\n          pageSizeOptions,\n          hidePageSize,\n          showFirstLastButtons\n        } = defaults;\n        if (pageSize != null) {\n          this._pageSize = pageSize;\n        }\n        if (pageSizeOptions != null) {\n          this._pageSizeOptions = pageSizeOptions;\n        }\n        if (hidePageSize != null) {\n          this.hidePageSize = hidePageSize;\n        }\n        if (showFirstLastButtons != null) {\n          this.showFirstLastButtons = showFirstLastButtons;\n        }\n      }\n      this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n    }\n    ngOnInit() {\n      this._isInitialized = true;\n      this._updateDisplayedPageSizeOptions();\n      this._initializedStream.next();\n    }\n    ngOnDestroy() {\n      this._initializedStream.complete();\n      this._intlChanges.unsubscribe();\n    }\n    /** Advances to the next page if it exists. */\n    nextPage() {\n      if (this.hasNextPage()) {\n        this._navigate(this.pageIndex + 1);\n      }\n    }\n    /** Move back to the previous page if it exists. */\n    previousPage() {\n      if (this.hasPreviousPage()) {\n        this._navigate(this.pageIndex - 1);\n      }\n    }\n    /** Move to the first page if not already there. */\n    firstPage() {\n      // hasPreviousPage being false implies at the start\n      if (this.hasPreviousPage()) {\n        this._navigate(0);\n      }\n    }\n    /** Move to the last page if not already there. */\n    lastPage() {\n      // hasNextPage being false implies at the end\n      if (this.hasNextPage()) {\n        this._navigate(this.getNumberOfPages() - 1);\n      }\n    }\n    /** Whether there is a previous page. */\n    hasPreviousPage() {\n      return this.pageIndex >= 1 && this.pageSize != 0;\n    }\n    /** Whether there is a next page. */\n    hasNextPage() {\n      const maxPageIndex = this.getNumberOfPages() - 1;\n      return this.pageIndex < maxPageIndex && this.pageSize != 0;\n    }\n    /** Calculate the number of pages */\n    getNumberOfPages() {\n      if (!this.pageSize) {\n        return 0;\n      }\n      return Math.ceil(this.length / this.pageSize);\n    }\n    /**\n     * Changes the page size so that the first item displayed on the page will still be\n     * displayed using the new page size.\n     *\n     * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n     * switching so that the page size is 5 will set the third page as the current page so\n     * that the 10th item will still be displayed.\n     */\n    _changePageSize(pageSize) {\n      // Current page needs to be updated to reflect the new page size. Navigate to the page\n      // containing the previous page's first item.\n      const startIndex = this.pageIndex * this.pageSize;\n      const previousPageIndex = this.pageIndex;\n      this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n      this.pageSize = pageSize;\n      this._emitPageEvent(previousPageIndex);\n    }\n    /** Checks whether the buttons for going forwards should be disabled. */\n    _nextButtonsDisabled() {\n      return this.disabled || !this.hasNextPage();\n    }\n    /** Checks whether the buttons for going backwards should be disabled. */\n    _previousButtonsDisabled() {\n      return this.disabled || !this.hasPreviousPage();\n    }\n    /**\n     * Updates the list of page size options to display to the user. Includes making sure that\n     * the page size is an option and that the list is sorted.\n     */\n    _updateDisplayedPageSizeOptions() {\n      if (!this._isInitialized) {\n        return;\n      }\n      // If no page size is provided, use the first page size option or the default page size.\n      if (!this.pageSize) {\n        this._pageSize = this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n      }\n      this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n      if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n        this._displayedPageSizeOptions.push(this.pageSize);\n      }\n      // Sort the numbers using a number-specific sort function.\n      this._displayedPageSizeOptions.sort((a, b) => a - b);\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n    _emitPageEvent(previousPageIndex) {\n      this.page.emit({\n        previousPageIndex,\n        pageIndex: this.pageIndex,\n        pageSize: this.pageSize,\n        length: this.length\n      });\n    }\n    /** Navigates to a specific page index. */\n    _navigate(index) {\n      const previousIndex = this.pageIndex;\n      if (index !== previousIndex) {\n        this.pageIndex = index;\n        this._emitPageEvent(previousIndex);\n      }\n    }\n    /**\n     * Callback invoked when one of the navigation buttons is called.\n     * @param targetIndex Index to which the paginator should navigate.\n     * @param isDisabled Whether the button is disabled.\n     */\n    _buttonClicked(targetIndex, isDisabled) {\n      // Note that normally disabled buttons won't dispatch the click event, but the paginator ones\n      // do, because we're using `disabledInteractive` to allow them to be focusable. We need to\n      // check here to avoid the navigation.\n      if (!isDisabled) {\n        this._navigate(targetIndex);\n      }\n    }\n    static ɵfac = function MatPaginator_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPaginator)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatPaginator,\n      selectors: [[\"mat-paginator\"]],\n      hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-paginator\"],\n      inputs: {\n        color: \"color\",\n        pageIndex: [2, \"pageIndex\", \"pageIndex\", numberAttribute],\n        length: [2, \"length\", \"length\", numberAttribute],\n        pageSize: [2, \"pageSize\", \"pageSize\", numberAttribute],\n        pageSizeOptions: \"pageSizeOptions\",\n        hidePageSize: [2, \"hidePageSize\", \"hidePageSize\", booleanAttribute],\n        showFirstLastButtons: [2, \"showFirstLastButtons\", \"showFirstLastButtons\", booleanAttribute],\n        selectConfig: \"selectConfig\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        page: \"page\"\n      },\n      exportAs: [\"matPaginator\"],\n      decls: 14,\n      vars: 14,\n      consts: [[\"selectRef\", \"\"], [1, \"mat-mdc-paginator-outer-container\"], [1, \"mat-mdc-paginator-container\"], [1, \"mat-mdc-paginator-page-size\"], [1, \"mat-mdc-paginator-range-actions\"], [\"aria-live\", \"polite\", 1, \"mat-mdc-paginator-range-label\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-previous\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"viewBox\", \"0 0 24 24\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-paginator-icon\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-next\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [1, \"mat-mdc-paginator-page-size-label\"], [1, \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\"], [1, \"mat-mdc-paginator-page-size-value\"], [\"hideSingleSelectionIndicator\", \"\", 3, \"selectionChange\", \"value\", \"disabled\", \"aria-labelledby\", \"panelClass\", \"disableOptionCentering\"], [3, \"value\"], [1, \"mat-mdc-paginator-touch-target\", 3, \"click\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"d\", \"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", \"matTooltipPosition\", \"above\", \"disabledInteractive\", \"\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"disabled\", \"tabindex\"], [\"d\", \"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"]],\n      template: function MatPaginator_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵtemplate(2, MatPaginator_Conditional_2_Template, 5, 4, \"div\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, MatPaginator_Conditional_6_Template, 3, 5, \"button\", 6);\n          i0.ɵɵelementStart(7, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_7_listener() {\n            return ctx._buttonClicked(ctx.pageIndex - 1, ctx._previousButtonsDisabled());\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 8);\n          i0.ɵɵelement(9, \"path\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(10, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_10_listener() {\n            return ctx._buttonClicked(ctx.pageIndex + 1, ctx._nextButtonsDisabled());\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(11, \"svg\", 8);\n          i0.ɵɵelement(12, \"path\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, MatPaginator_Conditional_13_Template, 3, 5, \"button\", 12);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx.hidePageSize ? 2 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx._intl.getRangeLabel(ctx.pageIndex, ctx.pageSize, ctx.length), \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.showFirstLastButtons ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.previousPageLabel)(\"matTooltipDisabled\", ctx._previousButtonsDisabled())(\"disabled\", ctx._previousButtonsDisabled())(\"tabindex\", ctx._previousButtonsDisabled() ? -1 : null);\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.previousPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.nextPageLabel)(\"matTooltipDisabled\", ctx._nextButtonsDisabled())(\"disabled\", ctx._nextButtonsDisabled())(\"tabindex\", ctx._nextButtonsDisabled() ? -1 : null);\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.nextPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx.showFirstLastButtons ? 13 : -1);\n        }\n      },\n      dependencies: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height:var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatPaginator;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatPaginatorModule = /*#__PURE__*/(() => {\n  class MatPaginatorModule {\n    static ɵfac = function MatPaginatorModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPaginatorModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatPaginatorModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_PAGINATOR_INTL_PROVIDER],\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator]\n    });\n  }\n  return MatPaginatorModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent };\n//# sourceMappingURL=paginator.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}