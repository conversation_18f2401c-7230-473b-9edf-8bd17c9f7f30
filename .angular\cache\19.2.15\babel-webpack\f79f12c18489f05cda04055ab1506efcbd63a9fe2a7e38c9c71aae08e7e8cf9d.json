{"ast": null, "code": "import { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let PromotionService = /*#__PURE__*/(() => {\n  class PromotionService {\n    constructor() {}\n    // Promotion Request Methods\n    submitPromotionRequest(request) {\n      // Implementation for submitting promotion request\n      console.log('Submitting promotion request:', request);\n      return of({\n        success: true,\n        message: 'Promotion request submitted successfully'\n      });\n    }\n    getPromotionRequests() {\n      // Implementation for fetching promotion requests\n      return of([]);\n    }\n    // Employee Methods\n    getEmployees() {\n      // Implementation for fetching employees\n      return of([]);\n    }\n    getEmployeeById(id) {\n      // Implementation for fetching employee by ID\n      return of({});\n    }\n    // Eligibility Methods\n    checkEligibility(employeeId) {\n      // Implementation for checking promotion eligibility\n      return of({});\n    }\n    getEligibleEmployees() {\n      // Implementation for fetching eligible employees\n      return of([]);\n    }\n    // Suitability Report Methods\n    submitSuitabilityReport(report) {\n      // Implementation for submitting suitability report\n      console.log('Submitting suitability report:', report);\n      return of({\n        success: true,\n        message: 'Suitability report submitted successfully'\n      });\n    }\n    getSuitabilityReports() {\n      // Implementation for fetching suitability reports\n      return of([]);\n    }\n    // Panel Approval Methods\n    submitPanelApproval(approval) {\n      // Implementation for submitting panel approval\n      console.log('Submitting panel approval:', approval);\n      return of({\n        success: true,\n        message: 'Panel approval submitted successfully'\n      });\n    }\n    getPanelApprovals() {\n      // Implementation for fetching panel approvals\n      return of([]);\n    }\n    // Promotion Results Methods\n    getPromotionResults() {\n      // Implementation for fetching promotion results\n      return of([]);\n    }\n    generatePromotionOrder(employeeId) {\n      // Implementation for generating promotion order\n      console.log('Generating promotion order for employee:', employeeId);\n      return of({\n        success: true,\n        orderNumber: 'PO-2024-001'\n      });\n    }\n    // Utility Methods\n    getDepartments() {\n      return of(['Administration', 'Finance', 'Operations', 'Technical', 'Human Resources']);\n    }\n    getDesignations() {\n      return of(['Seasonal Employee', 'Junior Assistant', 'Assistant', 'Superintendent', 'Assistant Manager', 'Deputy Manager']);\n    }\n    getPromotionTypes() {\n      return of(['Seasonal to Regular', 'Cadre-wise Promotion']);\n    }\n    getPanelTypes() {\n      return of(['Departmental Promotion Committee', 'Selection Committee', 'Review Committee']);\n    }\n    static {\n      this.ɵfac = function PromotionService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PromotionService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PromotionService,\n        factory: PromotionService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PromotionService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}