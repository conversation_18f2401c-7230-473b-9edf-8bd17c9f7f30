{"ast": null, "code": "import { CdkDialogContainer, Dialog, DialogConfig, DialogModule } from '@angular/cdk/dialog';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { inject, ANIMATION_MODULE_TYPE, EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, InjectionToken, Injectable, ElementRef, Directive, Input, NgModule } from '@angular/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { Subject, merge, defer } from 'rxjs';\nimport { filter, take, startWith } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollable } from '@angular/cdk/scrolling';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nfunction MatDialogContainer_ng_template_2_Template(rf, ctx) {}\nclass MatDialogConfig {\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This affects what is available for injection and the change detection order for the\n   * component instantiated inside of the dialog. This does not affect where the dialog\n   * content will be rendered.\n   */\n  viewContainerRef;\n  /**\n   * Injector used for the instantiation of the component to be attached. If provided,\n   * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n   */\n  injector;\n  /** ID for the dialog. If omitted, a unique one will be generated. */\n  id;\n  /** The ARIA role of the dialog element. */\n  role = 'dialog';\n  /** Custom class for the overlay pane. */\n  panelClass = '';\n  /** Whether the dialog has a backdrop. */\n  hasBackdrop = true;\n  /** Custom class for the backdrop. */\n  backdropClass = '';\n  /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n  disableClose = false;\n  /** Width of the dialog. */\n  width = '';\n  /** Height of the dialog. */\n  height = '';\n  /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n  minWidth;\n  /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n  minHeight;\n  /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n  maxWidth;\n  /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n  maxHeight;\n  /** Position overrides. */\n  position;\n  /** Data being injected into the child component. */\n  data = null;\n  /** Layout direction for the dialog's content. */\n  direction;\n  /** ID of the element that describes the dialog. */\n  ariaDescribedBy = null;\n  /** ID of the element that labels the dialog. */\n  ariaLabelledBy = null;\n  /** Aria label to assign to the dialog element. */\n  ariaLabel = null;\n  /**\n   * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n   * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n   * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n   */\n  ariaModal = false;\n  /**\n   * Where the dialog should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus = 'first-tabbable';\n  /**\n   * Whether the dialog should restore focus to the\n   * previously-focused element, after it's closed.\n   */\n  restoreFocus = true;\n  /** Whether to wait for the opening animation to finish before trapping focus. */\n  delayFocusTrap = true;\n  /** Scroll strategy to be used for the dialog. */\n  scrollStrategy;\n  /**\n   * Whether the dialog should close when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  closeOnNavigation = true;\n  /**\n   * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n   * @deprecated No longer used. Will be removed.\n   * @breaking-change 20.0.0\n   */\n  componentFactoryResolver;\n  /**\n   * Duration of the enter animation in ms.\n   * Should be a number, string type is deprecated.\n   * @breaking-change 17.0.0 Remove string signature.\n   */\n  enterAnimationDuration;\n  /**\n   * Duration of the exit animation in ms.\n   * Should be a number, string type is deprecated.\n   * @breaking-change 17.0.0 Remove string signature.\n   */\n  exitAnimationDuration;\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n/** Duration of the opening animation in milliseconds. */\nconst OPEN_ANIMATION_DURATION = 150;\n/** Duration of the closing animation in milliseconds. */\nconst CLOSE_ANIMATION_DURATION = 75;\nlet MatDialogContainer = /*#__PURE__*/(() => {\n  class MatDialogContainer extends CdkDialogContainer {\n    _animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    /** Emits when an animation state changes. */\n    _animationStateChanged = new EventEmitter();\n    /** Whether animations are enabled. */\n    _animationsEnabled = this._animationMode !== 'NoopAnimations';\n    /** Number of actions projected in the dialog. */\n    _actionSectionCount = 0;\n    /** Host element of the dialog container component. */\n    _hostElement = this._elementRef.nativeElement;\n    /** Duration of the dialog open animation. */\n    _enterAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION : 0;\n    /** Duration of the dialog close animation. */\n    _exitAnimationDuration = this._animationsEnabled ? parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION : 0;\n    /** Current timer for dialog animations. */\n    _animationTimer = null;\n    _contentAttached() {\n      // Delegate to the original dialog-container initialization (i.e. saving the\n      // previous element, setting up the focus trap and moving focus to the container).\n      super._contentAttached();\n      // Note: Usually we would be able to use the MDC dialog foundation here to handle\n      // the dialog animation for us, but there are a few reasons why we just leverage\n      // their styles and not use the runtime foundation code:\n      //   1. Foundation does not allow us to disable animations.\n      //   2. Foundation contains unnecessary features we don't need and aren't\n      //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n      this._startOpenAnimation();\n    }\n    /** Starts the dialog open animation if enabled. */\n    _startOpenAnimation() {\n      this._animationStateChanged.emit({\n        state: 'opening',\n        totalTime: this._enterAnimationDuration\n      });\n      if (this._animationsEnabled) {\n        this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._enterAnimationDuration}ms`);\n        // We need to give the `setProperty` call from above some time to be applied.\n        // One would expect that the open class is added once the animation finished, but MDC\n        // uses the open class in combination with the opening class to start the animation.\n        this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n        this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n      } else {\n        this._hostElement.classList.add(OPEN_CLASS);\n        // Note: We could immediately finish the dialog opening here with noop animations,\n        // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n        // Executing this immediately would mean that `afterOpened` emits synchronously\n        // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n        Promise.resolve().then(() => this._finishDialogOpen());\n      }\n    }\n    /**\n     * Starts the exit animation of the dialog if enabled. This method is\n     * called by the dialog ref.\n     */\n    _startExitAnimation() {\n      this._animationStateChanged.emit({\n        state: 'closing',\n        totalTime: this._exitAnimationDuration\n      });\n      this._hostElement.classList.remove(OPEN_CLASS);\n      if (this._animationsEnabled) {\n        this._hostElement.style.setProperty(TRANSITION_DURATION_PROPERTY, `${this._exitAnimationDuration}ms`);\n        // We need to give the `setProperty` call from above some time to be applied.\n        this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n        this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n      } else {\n        // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n        // set up before any user can subscribe to the backdrop click. The subscription triggers\n        // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n        // animation state event if animations are disabled, the overlay would be disposed\n        // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n        // skipped. We work around this by waiting with the dialog close until the next tick when\n        // all subscriptions have been fired as expected. This is not an ideal solution, but\n        // there doesn't seem to be any other good way. Alternatives that have been considered:\n        //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n        //      Also this issue is specific to the MDC implementation where the dialog could\n        //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n        //      and closing always takes at least a tick.\n        //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n        //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n        //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n        // Based on the fact that this is specific to the MDC-based implementation of the dialog\n        // animations, the defer is applied here.\n        Promise.resolve().then(() => this._finishDialogClose());\n      }\n    }\n    /**\n     * Updates the number action sections.\n     * @param delta Increase/decrease in the number of sections.\n     */\n    _updateActionSectionCount(delta) {\n      this._actionSectionCount += delta;\n      this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Completes the dialog open by clearing potential animation classes, trapping\n     * focus and emitting an opened event.\n     */\n    _finishDialogOpen = () => {\n      this._clearAnimationClasses();\n      this._openAnimationDone(this._enterAnimationDuration);\n    };\n    /**\n     * Completes the dialog close by clearing potential animation classes, restoring\n     * focus and emitting a closed event.\n     */\n    _finishDialogClose = () => {\n      this._clearAnimationClasses();\n      this._animationStateChanged.emit({\n        state: 'closed',\n        totalTime: this._exitAnimationDuration\n      });\n    };\n    /** Clears all dialog animation classes. */\n    _clearAnimationClasses() {\n      this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n    }\n    _waitForAnimationToComplete(duration, callback) {\n      if (this._animationTimer !== null) {\n        clearTimeout(this._animationTimer);\n      }\n      // Note that we want this timer to run inside the NgZone, because we want\n      // the related events like `afterClosed` to be inside the zone as well.\n      this._animationTimer = setTimeout(callback, duration);\n    }\n    /** Runs a callback in `requestAnimationFrame`, if available. */\n    _requestAnimationFrame(callback) {\n      this._ngZone.runOutsideAngular(() => {\n        if (typeof requestAnimationFrame === 'function') {\n          requestAnimationFrame(callback);\n        } else {\n          callback();\n        }\n      });\n    }\n    _captureInitialFocus() {\n      if (!this._config.delayFocusTrap) {\n        this._trapFocus();\n      }\n    }\n    /**\n     * Callback for when the open dialog animation has finished. Intended to\n     * be called by sub-classes that use different animation implementations.\n     */\n    _openAnimationDone(totalTime) {\n      if (this._config.delayFocusTrap) {\n        this._trapFocus();\n      }\n      this._animationStateChanged.next({\n        state: 'opened',\n        totalTime\n      });\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      if (this._animationTimer !== null) {\n        clearTimeout(this._animationTimer);\n      }\n    }\n    attachComponentPortal(portal) {\n      // When a component is passed into the dialog, the host element interrupts\n      // the `display:flex` from affecting the dialog title, content, and\n      // actions. To fix this, we make the component host `display: contents` by\n      // marking its host with the `mat-mdc-dialog-component-host` class.\n      //\n      // Note that this problem does not exist when a template ref is used since\n      // the title, contents, and actions are then nested directly under the\n      // dialog surface.\n      const ref = super.attachComponentPortal(portal);\n      ref.location.nativeElement.classList.add('mat-mdc-dialog-component-host');\n      return ref;\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatDialogContainer_BaseFactory;\n      return function MatDialogContainer_Factory(__ngFactoryType__) {\n        return (ɵMatDialogContainer_BaseFactory || (ɵMatDialogContainer_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogContainer)))(__ngFactoryType__ || MatDialogContainer);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatDialogContainer,\n      selectors: [[\"mat-dialog-container\"]],\n      hostAttrs: [\"tabindex\", \"-1\", 1, \"mat-mdc-dialog-container\", \"mdc-dialog\"],\n      hostVars: 10,\n      hostBindings: function MatDialogContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx._config.id);\n          i0.ɵɵattribute(\"aria-modal\", ctx._config.ariaModal)(\"role\", ctx._config.role)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n          i0.ɵɵclassProp(\"_mat-animation-noopable\", !ctx._animationsEnabled)(\"mat-mdc-dialog-container-with-actions\", ctx._actionSectionCount > 0);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"mat-mdc-dialog-inner-container\", \"mdc-dialog__container\"], [1, \"mat-mdc-dialog-surface\", \"mdc-dialog__surface\"], [\"cdkPortalOutlet\", \"\"]],\n      template: function MatDialogContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, MatDialogContainer_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [CdkPortalOutlet],\n      styles: [\".mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mdc-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mdc-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mdc-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mdc-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mdc-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mdc-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mdc-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mdc-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mdc-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mdc-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mdc-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mdc-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mdc-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mdc-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\\n\"],\n      encapsulation: 2\n    });\n  }\n  return MatDialogContainer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time) {\n  if (time == null) {\n    return null;\n  }\n  if (typeof time === 'number') {\n    return time;\n  }\n  if (time.endsWith('ms')) {\n    return coerceNumberProperty(time.substring(0, time.length - 2));\n  }\n  if (time.endsWith('s')) {\n    return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n  }\n  if (time === '0') {\n    return 0;\n  }\n  return null; // anything else is invalid.\n}\nvar MatDialogState = /*#__PURE__*/function (MatDialogState) {\n  MatDialogState[MatDialogState[\"OPEN\"] = 0] = \"OPEN\";\n  MatDialogState[MatDialogState[\"CLOSING\"] = 1] = \"CLOSING\";\n  MatDialogState[MatDialogState[\"CLOSED\"] = 2] = \"CLOSED\";\n  return MatDialogState;\n}(MatDialogState || {});\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nclass MatDialogRef {\n  _ref;\n  _containerInstance;\n  /** The instance of component opened into the dialog. */\n  componentInstance;\n  /**\n   * `ComponentRef` of the component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentRef;\n  /** Whether the user is allowed to close the dialog. */\n  disableClose;\n  /** Unique ID for the dialog. */\n  id;\n  /** Subject for notifying the user that the dialog has finished opening. */\n  _afterOpened = /*#__PURE__*/new Subject();\n  /** Subject for notifying the user that the dialog has started closing. */\n  _beforeClosed = /*#__PURE__*/new Subject();\n  /** Result to be passed to afterClosed. */\n  _result;\n  /** Handle to the timeout that's running as a fallback in case the exit animation doesn't fire. */\n  _closeFallbackTimeout;\n  /** Current state of the dialog. */\n  _state = MatDialogState.OPEN;\n  // TODO(crisbeto): we shouldn't have to declare this property, because `DialogRef.close`\n  // already has a second `options` parameter that we can use. The problem is that internal tests\n  // have assertions like `expect(MatDialogRef.close).toHaveBeenCalledWith(foo)` which will break,\n  // because it'll be called with two arguments by things like `MatDialogClose`.\n  /** Interaction that caused the dialog to close. */\n  _closeInteractionType;\n  constructor(_ref, config, _containerInstance) {\n    this._ref = _ref;\n    this._containerInstance = _containerInstance;\n    this.disableClose = config.disableClose;\n    this.id = _ref.id;\n    // Used to target panels specifically tied to dialogs.\n    _ref.addPanelClass('mat-mdc-dialog-panel');\n    // Emit when opening animation completes\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'opened'), take(1)).subscribe(() => {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    });\n    // Dispose overlay when closing animation is complete\n    _containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closed'), take(1)).subscribe(() => {\n      clearTimeout(this._closeFallbackTimeout);\n      this._finishDialogClose();\n    });\n    _ref.overlayRef.detachments().subscribe(() => {\n      this._beforeClosed.next(this._result);\n      this._beforeClosed.complete();\n      this._finishDialogClose();\n    });\n    merge(this.backdropClick(), this.keydownEvents().pipe(filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)))).subscribe(event => {\n      if (!this.disableClose) {\n        event.preventDefault();\n        _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param dialogResult Optional result to return to the dialog opener.\n   */\n  close(dialogResult) {\n    this._result = dialogResult;\n    // Transition the backdrop in parallel to the dialog.\n    this._containerInstance._animationStateChanged.pipe(filter(event => event.state === 'closing'), take(1)).subscribe(event => {\n      this._beforeClosed.next(dialogResult);\n      this._beforeClosed.complete();\n      this._ref.overlayRef.detachBackdrop();\n      // The logic that disposes of the overlay depends on the exit animation completing, however\n      // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n      // timeout which will clean everything up if the animation hasn't fired within the specified\n      // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n      // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n      this._closeFallbackTimeout = setTimeout(() => this._finishDialogClose(), event.totalTime + 100);\n    });\n    this._state = MatDialogState.CLOSING;\n    this._containerInstance._startExitAnimation();\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished opening.\n   */\n  afterOpened() {\n    return this._afterOpened;\n  }\n  /**\n   * Gets an observable that is notified when the dialog is finished closing.\n   */\n  afterClosed() {\n    return this._ref.closed;\n  }\n  /**\n   * Gets an observable that is notified when the dialog has started closing.\n   */\n  beforeClosed() {\n    return this._beforeClosed;\n  }\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n  backdropClick() {\n    return this._ref.backdropClick;\n  }\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n  keydownEvents() {\n    return this._ref.keydownEvents;\n  }\n  /**\n   * Updates the dialog's position.\n   * @param position New dialog position.\n   */\n  updatePosition(position) {\n    let strategy = this._ref.config.positionStrategy;\n    if (position && (position.left || position.right)) {\n      position.left ? strategy.left(position.left) : strategy.right(position.right);\n    } else {\n      strategy.centerHorizontally();\n    }\n    if (position && (position.top || position.bottom)) {\n      position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n    } else {\n      strategy.centerVertically();\n    }\n    this._ref.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this._ref.updateSize(width, height);\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this._ref.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this._ref.removePanelClass(classes);\n    return this;\n  }\n  /** Gets the current state of the dialog's lifecycle. */\n  getState() {\n    return this._state;\n  }\n  /**\n   * Finishes the dialog close by updating the state of the dialog\n   * and disposing the overlay.\n   */\n  _finishDialogClose() {\n    this._state = MatDialogState.CLOSED;\n    this._ref.close(this._result, {\n      focusOrigin: this._closeInteractionType\n    });\n    this.componentInstance = null;\n  }\n}\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nfunction _closeDialogVia(ref, interactionType, result) {\n  ref._closeInteractionType = interactionType;\n  return ref.close(result);\n}\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nconst MAT_DIALOG_DATA = /*#__PURE__*/new InjectionToken('MatMdcDialogData');\n/** Injection token that can be used to specify default dialog options. */\nconst MAT_DIALOG_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('mat-mdc-dialog-default-options');\n/** Injection token that determines the scroll handling while the dialog is open. */\nconst MAT_DIALOG_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('mat-mdc-dialog-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.block();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst MAT_DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Service to open Material Design modal dialogs.\n */\nlet MatDialog = /*#__PURE__*/(() => {\n  class MatDialog {\n    _overlay = inject(Overlay);\n    _defaultOptions = inject(MAT_DIALOG_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    _scrollStrategy = inject(MAT_DIALOG_SCROLL_STRATEGY);\n    _parentDialog = inject(MatDialog, {\n      optional: true,\n      skipSelf: true\n    });\n    _idGenerator = inject(_IdGenerator);\n    _dialog = inject(Dialog);\n    _openDialogsAtThisLevel = [];\n    _afterAllClosedAtThisLevel = new Subject();\n    _afterOpenedAtThisLevel = new Subject();\n    dialogConfigClass = MatDialogConfig;\n    _dialogRefConstructor;\n    _dialogContainerType;\n    _dialogDataToken;\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n      return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n      return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    _getAfterAllClosed() {\n      const parent = this._parentDialog;\n      return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n    constructor() {\n      this._dialogRefConstructor = MatDialogRef;\n      this._dialogContainerType = MatDialogContainer;\n      this._dialogDataToken = MAT_DIALOG_DATA;\n    }\n    open(componentOrTemplateRef, config) {\n      let dialogRef;\n      config = {\n        ...(this._defaultOptions || new MatDialogConfig()),\n        ...config\n      };\n      config.id = config.id || this._idGenerator.getId('mat-mdc-dialog-');\n      config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n      const cdkRef = this._dialog.open(componentOrTemplateRef, {\n        ...config,\n        positionStrategy: this._overlay.position().global().centerHorizontally().centerVertically(),\n        // Disable closing since we need to sync it up to the animation ourselves.\n        disableClose: true,\n        // Disable closing on destroy, because this service cleans up its open dialogs as well.\n        // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n        // the dialogs immediately whereas we want it to wait for the animations to finish.\n        closeOnDestroy: false,\n        // Disable closing on detachments so that we can sync up the animation.\n        // The Material dialog ref handles this manually.\n        closeOnOverlayDetachments: false,\n        container: {\n          type: this._dialogContainerType,\n          providers: () => [\n          // Provide our config as the CDK config as well since it has the same interface as the\n          // CDK one, but it contains the actual values passed in by the user for things like\n          // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n          {\n            provide: this.dialogConfigClass,\n            useValue: config\n          }, {\n            provide: DialogConfig,\n            useValue: config\n          }]\n        },\n        templateContext: () => ({\n          dialogRef\n        }),\n        providers: (ref, cdkConfig, dialogContainer) => {\n          dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n          dialogRef.updatePosition(config?.position);\n          return [{\n            provide: this._dialogContainerType,\n            useValue: dialogContainer\n          }, {\n            provide: this._dialogDataToken,\n            useValue: cdkConfig.data\n          }, {\n            provide: this._dialogRefConstructor,\n            useValue: dialogRef\n          }];\n        }\n      });\n      // This can't be assigned in the `providers` callback, because\n      // the instance hasn't been assigned to the CDK ref yet.\n      dialogRef.componentRef = cdkRef.componentRef;\n      dialogRef.componentInstance = cdkRef.componentInstance;\n      this.openDialogs.push(dialogRef);\n      this.afterOpened.next(dialogRef);\n      dialogRef.afterClosed().subscribe(() => {\n        const index = this.openDialogs.indexOf(dialogRef);\n        if (index > -1) {\n          this.openDialogs.splice(index, 1);\n          if (!this.openDialogs.length) {\n            this._getAfterAllClosed().next();\n          }\n        }\n      });\n      return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n      this._closeDialogs(this.openDialogs);\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n      return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n      // Only close the dialogs at this level on destroy\n      // since the parent service may still be active.\n      this._closeDialogs(this._openDialogsAtThisLevel);\n      this._afterAllClosedAtThisLevel.complete();\n      this._afterOpenedAtThisLevel.complete();\n    }\n    _closeDialogs(dialogs) {\n      let i = dialogs.length;\n      while (i--) {\n        dialogs[i].close();\n      }\n    }\n    static ɵfac = function MatDialog_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatDialog)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatDialog,\n      factory: MatDialog.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MatDialog;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Button that will close the current dialog.\n */\nlet MatDialogClose = /*#__PURE__*/(() => {\n  class MatDialogClose {\n    dialogRef = inject(MatDialogRef, {\n      optional: true\n    });\n    _elementRef = inject(ElementRef);\n    _dialog = inject(MatDialog);\n    /** Screen-reader label for the button. */\n    ariaLabel;\n    /** Default to \"button\" to prevents accidental form submits. */\n    type = 'button';\n    /** Dialog close input. */\n    dialogResult;\n    _matDialogClose;\n    constructor() {}\n    ngOnInit() {\n      if (!this.dialogRef) {\n        // When this directive is included in a dialog via TemplateRef (rather than being\n        // in a Component), the DialogRef isn't available via injection because embedded\n        // views cannot be given a custom injector. Instead, we look up the DialogRef by\n        // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n        // be resolved at constructor time.\n        this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n      }\n    }\n    ngOnChanges(changes) {\n      const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n      if (proxiedChange) {\n        this.dialogResult = proxiedChange.currentValue;\n      }\n    }\n    _onButtonClick(event) {\n      // Determinate the focus origin using the click event, because using the FocusMonitor will\n      // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n      // dialog, and therefore clicking the button won't result in a focus change. This means that\n      // the FocusMonitor won't detect any origin change, and will always output `program`.\n      _closeDialogVia(this.dialogRef, event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse', this.dialogResult);\n    }\n    static ɵfac = function MatDialogClose_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatDialogClose)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatDialogClose,\n      selectors: [[\"\", \"mat-dialog-close\", \"\"], [\"\", \"matDialogClose\", \"\"]],\n      hostVars: 2,\n      hostBindings: function MatDialogClose_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatDialogClose_click_HostBindingHandler($event) {\n            return ctx._onButtonClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        type: \"type\",\n        dialogResult: [0, \"mat-dialog-close\", \"dialogResult\"],\n        _matDialogClose: [0, \"matDialogClose\", \"_matDialogClose\"]\n      },\n      exportAs: [\"matDialogClose\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return MatDialogClose;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatDialogLayoutSection = /*#__PURE__*/(() => {\n  class MatDialogLayoutSection {\n    _dialogRef = inject(MatDialogRef, {\n      optional: true\n    });\n    _elementRef = inject(ElementRef);\n    _dialog = inject(MatDialog);\n    constructor() {}\n    ngOnInit() {\n      if (!this._dialogRef) {\n        this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs);\n      }\n      if (this._dialogRef) {\n        Promise.resolve().then(() => {\n          this._onAdd();\n        });\n      }\n    }\n    ngOnDestroy() {\n      // Note: we null check because there are some internal\n      // tests that are mocking out `MatDialogRef` incorrectly.\n      const instance = this._dialogRef?._containerInstance;\n      if (instance) {\n        Promise.resolve().then(() => {\n          this._onRemove();\n        });\n      }\n    }\n    static ɵfac = function MatDialogLayoutSection_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatDialogLayoutSection)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatDialogLayoutSection\n    });\n  }\n  return MatDialogLayoutSection;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\nlet MatDialogTitle = /*#__PURE__*/(() => {\n  class MatDialogTitle extends MatDialogLayoutSection {\n    id = inject(_IdGenerator).getId('mat-mdc-dialog-title-');\n    _onAdd() {\n      // Note: we null check the queue, because there are some internal\n      // tests that are mocking out `MatDialogRef` incorrectly.\n      this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id);\n    }\n    _onRemove() {\n      this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id);\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatDialogTitle_BaseFactory;\n      return function MatDialogTitle_Factory(__ngFactoryType__) {\n        return (ɵMatDialogTitle_BaseFactory || (ɵMatDialogTitle_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogTitle)))(__ngFactoryType__ || MatDialogTitle);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatDialogTitle,\n      selectors: [[\"\", \"mat-dialog-title\", \"\"], [\"\", \"matDialogTitle\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-dialog-title\", \"mdc-dialog__title\"],\n      hostVars: 1,\n      hostBindings: function MatDialogTitle_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n        }\n      },\n      inputs: {\n        id: \"id\"\n      },\n      exportAs: [\"matDialogTitle\"],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatDialogTitle;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Scrollable content container of a dialog.\n */\nlet MatDialogContent = /*#__PURE__*/(() => {\n  class MatDialogContent {\n    static ɵfac = function MatDialogContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatDialogContent)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatDialogContent,\n      selectors: [[\"\", \"mat-dialog-content\", \"\"], [\"mat-dialog-content\"], [\"\", \"matDialogContent\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-dialog-content\", \"mdc-dialog__content\"],\n      features: [i0.ɵɵHostDirectivesFeature([i1.CdkScrollable])]\n    });\n  }\n  return MatDialogContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\nlet MatDialogActions = /*#__PURE__*/(() => {\n  class MatDialogActions extends MatDialogLayoutSection {\n    /**\n     * Horizontal alignment of action buttons.\n     */\n    align;\n    _onAdd() {\n      this._dialogRef._containerInstance?._updateActionSectionCount?.(1);\n    }\n    _onRemove() {\n      this._dialogRef._containerInstance?._updateActionSectionCount?.(-1);\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵMatDialogActions_BaseFactory;\n      return function MatDialogActions_Factory(__ngFactoryType__) {\n        return (ɵMatDialogActions_BaseFactory || (ɵMatDialogActions_BaseFactory = i0.ɵɵgetInheritedFactory(MatDialogActions)))(__ngFactoryType__ || MatDialogActions);\n      };\n    })();\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatDialogActions,\n      selectors: [[\"\", \"mat-dialog-actions\", \"\"], [\"mat-dialog-actions\"], [\"\", \"matDialogActions\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-dialog-actions\", \"mdc-dialog__actions\"],\n      hostVars: 6,\n      hostBindings: function MatDialogActions_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-dialog-actions-align-start\", ctx.align === \"start\")(\"mat-mdc-dialog-actions-align-center\", ctx.align === \"center\")(\"mat-mdc-dialog-actions-align-end\", ctx.align === \"end\");\n        }\n      },\n      inputs: {\n        align: \"align\"\n      },\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return MatDialogActions;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element, openDialogs) {\n  let parent = element.nativeElement.parentElement;\n  while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n    parent = parent.parentElement;\n  }\n  return parent ? openDialogs.find(dialog => dialog.id === parent.id) : null;\n}\nconst DIRECTIVES = [MatDialogContainer, MatDialogClose, MatDialogTitle, MatDialogActions, MatDialogContent];\nlet MatDialogModule = /*#__PURE__*/(() => {\n  class MatDialogModule {\n    static ɵfac = function MatDialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatDialogModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatDialogModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MatDialog],\n      imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, MatCommonModule]\n    });\n  }\n  return MatDialogModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatDialogActions as M, _closeDialogVia as _, MatDialogClose as a, MatDialogTitle as b, MatDialogContent as c, MatDialogContainer as d, MAT_DIALOG_DATA as e, MAT_DIALOG_DEFAULT_OPTIONS as f, MAT_DIALOG_SCROLL_STRATEGY as g, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY as h, MAT_DIALOG_SCROLL_STRATEGY_PROVIDER as i, MatDialog as j, MatDialogConfig as k, MatDialogState as l, MatDialogRef as m, MatDialogModule as n };\n//# sourceMappingURL=module-BnDTus5c.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}