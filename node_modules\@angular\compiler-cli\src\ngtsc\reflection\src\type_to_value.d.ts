/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import ts from 'typescript';
import { TypeValueReference } from './host';
/**
 * Potentially convert a `ts.TypeNode` to a `TypeValueReference`, which indicates how to use the
 * type given in the `ts.TypeNode` in a value position.
 *
 * This can return `null` if the `typeNode` is `null`, if it does not refer to a symbol with a value
 * declaration, or if it is not possible to statically understand.
 */
export declare function typeToValue(typeNode: ts.TypeNode | null, checker: ts.TypeChecker, isLocalCompilation: boolean): TypeValueReference;
/**
 * Attempt to extract a `ts.Expression` that's equivalent to a `ts.TypeNode`, as the two have
 * different AST shapes but can reference the same symbols.
 *
 * This will return `null` if an equivalent expression cannot be constructed.
 */
export declare function typeNodeToValueExpr(node: ts.TypeNode): ts.Expression | null;
export declare function entityNameToValue(node: ts.EntityName): ts.Expression | null;
