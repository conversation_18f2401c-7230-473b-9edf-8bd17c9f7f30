//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-none'),
    'header-action-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'header-action-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'header-action-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'header-action-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'header-action-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'header-action-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'header-action-label-text-color': map.get($deps, 'md-sys-color', 'primary'),
    'header-action-label-text-font':
      map.get($deps, 'md-sys-typescale', 'label-large-font'),
    'header-action-label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'label-large-line-height'),
    'header-action-label-text-size':
      map.get($deps, 'md-sys-typescale', 'label-large-size'),
    'header-action-label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'label-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.full-screen-dialog.header.action.label-text.tracking cannot be
    // represented in the "font" property shorthand. Consider using the discrete properties instead.
    'header-action-label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'label-large-weight')
          map.get($deps, 'md-sys-typescale', 'label-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'label-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'label-large-font')
      ),
    'header-action-label-text-weight':
      map.get($deps, 'md-sys-typescale', 'label-large-weight'),
    'header-action-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'header-action-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'header-action-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'header-container-color': map.get($deps, 'md-sys-color', 'surface'),
    'header-container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'header-container-height': if($exclude-hardcoded-values, null, 56px),
    'header-container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'header-headline-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'header-headline-font':
      map.get($deps, 'md-sys-typescale', 'title-large-font'),
    'header-headline-line-height':
      map.get($deps, 'md-sys-typescale', 'title-large-line-height'),
    'header-headline-size':
      map.get($deps, 'md-sys-typescale', 'title-large-size'),
    'header-headline-tracking':
      map.get($deps, 'md-sys-typescale', 'title-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.full-screen-dialog.header.headline.tracking cannot be represented in
    // the "font" property shorthand. Consider using the discrete properties instead.
    'header-headline-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'title-large-weight')
          map.get($deps, 'md-sys-typescale', 'title-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'title-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'title-large-font')
      ),
    'header-headline-weight':
      map.get($deps, 'md-sys-typescale', 'title-large-weight'),
    'header-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'header-icon-size': if($exclude-hardcoded-values, null, 24px),
    'header-on-scroll-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level2')
  );
}
