//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'container-height': if($exclude-hardcoded-values, null, 32px),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-small'),
    'disabled-label-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-label-text-opacity': if($exclude-hardcoded-values, null, 0.38),
    'disabled-selected-container-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-selected-container-opacity':
      if($exclude-hardcoded-values, null, 0.12),
    'disabled-unselected-outline-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-unselected-outline-opacity':
      if($exclude-hardcoded-values, null, 0.12),
    'dragged-container-elevation': map.get($deps, 'md-sys-elevation', 'level4'),
    'label-text-font': map.get($deps, 'md-sys-typescale', 'label-large-font'),
    'label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'label-large-line-height'),
    'label-text-size': map.get($deps, 'md-sys-typescale', 'label-large-size'),
    'label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'label-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.input-chip.label-text.tracking cannot be represented in the "font"
    // property shorthand. Consider using the discrete properties instead.
    'label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'label-large-weight')
          map.get($deps, 'md-sys-typescale', 'label-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'label-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'label-large-font')
      ),
    'label-text-weight':
      map.get($deps, 'md-sys-typescale', 'label-large-weight'),
    'selected-container-color':
      map.get($deps, 'md-sys-color', 'secondary-container'),
    'selected-dragged-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-dragged-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-dragged-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'dragged-state-layer-opacity'),
    'selected-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'selected-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'selected-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-outline-width': if($exclude-hardcoded-values, null, 0),
    'selected-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'unselected-dragged-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-dragged-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-dragged-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'dragged-state-layer-opacity'),
    'unselected-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-focus-outline-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'unselected-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'unselected-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-outline-color': map.get($deps, 'md-sys-color', 'outline'),
    'unselected-outline-width': if($exclude-hardcoded-values, null, 1px),
    'unselected-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'with-avatar-avatar-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'with-avatar-avatar-size': if($exclude-hardcoded-values, null, 24px),
    'with-avatar-disabled-avatar-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'with-leading-icon-disabled-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-leading-icon-disabled-leading-icon-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'with-leading-icon-leading-icon-size':
      if($exclude-hardcoded-values, null, 18px),
    'with-leading-icon-selected-dragged-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-leading-icon-selected-focus-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-selected-hover-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-selected-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-selected-pressed-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-unselected-dragged-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-leading-icon-unselected-focus-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-unselected-hover-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-unselected-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-leading-icon-unselected-pressed-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-trailing-icon-disabled-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-trailing-icon-disabled-trailing-icon-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'with-trailing-icon-selected-dragged-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-trailing-icon-selected-focus-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-trailing-icon-selected-hover-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-trailing-icon-selected-pressed-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-trailing-icon-selected-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-trailing-icon-trailing-icon-size':
      if($exclude-hardcoded-values, null, 18px),
    'with-trailing-icon-unselected-dragged-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-trailing-icon-unselected-focus-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-trailing-icon-unselected-hover-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-trailing-icon-unselected-pressed-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-trailing-icon-unselected-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant')
  );
}
