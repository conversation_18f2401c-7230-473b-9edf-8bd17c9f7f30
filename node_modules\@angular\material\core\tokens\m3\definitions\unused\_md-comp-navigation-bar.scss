//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'active-indicator-color':
      map.get($deps, 'md-sys-color', 'secondary-container'),
    'active-indicator-height': if($exclude-hardcoded-values, null, 32px),
    'active-indicator-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'active-indicator-width': if($exclude-hardcoded-values, null, 64px),
    'active-focus-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'active-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'active-hover-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'active-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'active-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-label-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'active-label-text-weight':
      map.get($deps, 'md-sys-typescale', 'label-medium-weight-prominent'),
    'active-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'active-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level2'),
    'container-height': if($exclude-hardcoded-values, null, 80px),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-none'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'icon-size': if($exclude-hardcoded-values, null, 24px),
    'inactive-focus-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-hover-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-icon-color': map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'inactive-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'inactive-pressed-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'label-text-font': map.get($deps, 'md-sys-typescale', 'label-medium-font'),
    'label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'label-medium-line-height'),
    'label-text-size': map.get($deps, 'md-sys-typescale', 'label-medium-size'),
    'label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'label-medium-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.navigation-bar.label-text.tracking cannot be represented in the "font"
    // property shorthand. Consider using the discrete properties instead.
    'label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'label-medium-weight')
          map.get($deps, 'md-sys-typescale', 'label-medium-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'label-medium-line-height'
          ) map.get($deps, 'md-sys-typescale', 'label-medium-font')
      ),
    'label-text-weight':
      map.get($deps, 'md-sys-typescale', 'label-medium-weight'),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity')
  );
}
