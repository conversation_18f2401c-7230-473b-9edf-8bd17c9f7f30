//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'active-indicator-color':
      map.get($deps, 'md-sys-color', 'secondary-container'),
    'active-indicator-height': if($exclude-hardcoded-values, null, 56px),
    'active-indicator-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'active-indicator-width': if($exclude-hardcoded-values, null, 336px),
    'active-focus-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-hover-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-label-text-weight':
      map.get($deps, 'md-sys-typescale', 'label-large-weight-prominent'),
    'active-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'active-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'bottom-container-shape': map.get($deps, 'md-sys-shape', 'corner-large-top'),
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-height': if($exclude-hardcoded-values, null, 100%),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-large-end'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'container-width': if($exclude-hardcoded-values, null, 360px),
    'focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'headline-color': map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'headline-font': map.get($deps, 'md-sys-typescale', 'title-small-font'),
    'headline-line-height':
      map.get($deps, 'md-sys-typescale', 'title-small-line-height'),
    'headline-size': map.get($deps, 'md-sys-typescale', 'title-small-size'),
    'headline-tracking':
      map.get($deps, 'md-sys-typescale', 'title-small-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.navigation-drawer.headline.tracking cannot be represented in the "font"
    // property shorthand. Consider using the discrete properties instead.
    'headline-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'title-small-weight')
          map.get($deps, 'md-sys-typescale', 'title-small-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'title-small-line-height'
          ) map.get($deps, 'md-sys-typescale', 'title-small-font')
      ),
    'headline-weight': map.get($deps, 'md-sys-typescale', 'title-small-weight'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'icon-size': if($exclude-hardcoded-values, null, 24px),
    'inactive-focus-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-hover-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-icon-color': map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'inactive-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'inactive-pressed-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'inactive-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'label-text-font': map.get($deps, 'md-sys-typescale', 'label-large-font'),
    'label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'label-large-line-height'),
    'label-text-size': map.get($deps, 'md-sys-typescale', 'label-large-size'),
    'label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'label-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.navigation-drawer.label-text.tracking cannot be represented in the "font"
    // property shorthand. Consider using the discrete properties instead.
    'label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'label-large-weight')
          map.get($deps, 'md-sys-typescale', 'label-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'label-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'label-large-font')
      ),
    'label-text-weight':
      map.get($deps, 'md-sys-typescale', 'label-large-weight'),
    'large-badge-label-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'large-badge-label-font':
      map.get($deps, 'md-sys-typescale', 'label-large-font'),
    'large-badge-label-line-height':
      map.get($deps, 'md-sys-typescale', 'label-large-line-height'),
    'large-badge-label-size':
      map.get($deps, 'md-sys-typescale', 'label-large-size'),
    'large-badge-label-tracking':
      map.get($deps, 'md-sys-typescale', 'label-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.navigation-drawer.large-badge-label.tracking cannot be represented in the
    // "font" property shorthand. Consider using the discrete properties instead.
    'large-badge-label-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'label-large-weight')
          map.get($deps, 'md-sys-typescale', 'label-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'label-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'label-large-font')
      ),
    'large-badge-label-weight':
      map.get($deps, 'md-sys-typescale', 'label-large-weight'),
    'modal-container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'standard-container-elevation': map.get($deps, 'md-sys-elevation', 'level0')
  );
}
