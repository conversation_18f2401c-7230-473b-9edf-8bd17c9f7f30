//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
);

@function values($deps: $_default) {
  @return (
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-extra-large'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint')
  );
}
