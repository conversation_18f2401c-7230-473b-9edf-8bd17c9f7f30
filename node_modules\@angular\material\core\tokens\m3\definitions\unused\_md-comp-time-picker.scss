//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'clock-dial-color': map.get($deps, 'md-sys-color', 'surface-variant'),
    'clock-dial-container-size': if($exclude-hardcoded-values, null, 256px),
    'clock-dial-label-text-font':
      map.get($deps, 'md-sys-typescale', 'body-large-font'),
    'clock-dial-label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-large-line-height'),
    'clock-dial-label-text-size':
      map.get($deps, 'md-sys-typescale', 'body-large-size'),
    'clock-dial-label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.time-picker.clock-dial.label-text.tracking cannot be represented in the
    // "font" property shorthand. Consider using the discrete properties instead.
    'clock-dial-label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'body-large-weight')
          map.get($deps, 'md-sys-typescale', 'body-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'body-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'body-large-font')
      ),
    'clock-dial-label-text-weight':
      map.get($deps, 'md-sys-typescale', 'body-large-weight'),
    'clock-dial-selected-label-text-color':
      map.get($deps, 'md-sys-color', 'on-primary'),
    'clock-dial-selector-center-container-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'clock-dial-selector-center-container-shape':
      map.get($deps, 'md-sys-shape', 'corner-full'),
    'clock-dial-selector-center-container-size':
      if($exclude-hardcoded-values, null, 8px),
    'clock-dial-selector-handle-container-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'clock-dial-selector-handle-container-shape':
      map.get($deps, 'md-sys-shape', 'corner-full'),
    'clock-dial-selector-handle-container-size':
      if($exclude-hardcoded-values, null, 48px),
    'clock-dial-selector-track-container-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'clock-dial-selector-track-container-width':
      if($exclude-hardcoded-values, null, 2px),
    'clock-dial-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'clock-dial-unselected-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level3'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-extra-large'),
    'headline-color': map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'headline-font': map.get($deps, 'md-sys-typescale', 'label-medium-font'),
    'headline-line-height':
      map.get($deps, 'md-sys-typescale', 'label-medium-line-height'),
    'headline-size': map.get($deps, 'md-sys-typescale', 'label-medium-size'),
    'headline-tracking':
      map.get($deps, 'md-sys-typescale', 'label-medium-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.time-picker.headline.tracking cannot be represented in the "font" property
    // shorthand. Consider using the discrete properties instead.
    'headline-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'label-medium-weight')
          map.get($deps, 'md-sys-typescale', 'label-medium-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'label-medium-line-height'
          ) map.get($deps, 'md-sys-typescale', 'label-medium-font')
      ),
    'headline-weight': map.get($deps, 'md-sys-typescale', 'label-medium-weight'),
    'period-selector-container-shape':
      map.get($deps, 'md-sys-shape', 'corner-small'),
    'period-selector-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'period-selector-horizontal-container-height':
      if($exclude-hardcoded-values, null, 38px),
    'period-selector-horizontal-container-width':
      if($exclude-hardcoded-values, null, 216px),
    'period-selector-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'period-selector-label-text-font':
      map.get($deps, 'md-sys-typescale', 'title-medium-font'),
    'period-selector-label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'title-medium-line-height'),
    'period-selector-label-text-size':
      map.get($deps, 'md-sys-typescale', 'title-medium-size'),
    'period-selector-label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'title-medium-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.time-picker.period-selector.label-text.tracking cannot be represented in
    // the "font" property shorthand. Consider using the discrete properties instead.
    'period-selector-label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'title-medium-weight')
          map.get($deps, 'md-sys-typescale', 'title-medium-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'title-medium-line-height'
          ) map.get($deps, 'md-sys-typescale', 'title-medium-font')
      ),
    'period-selector-label-text-weight':
      map.get($deps, 'md-sys-typescale', 'title-medium-weight'),
    'period-selector-outline-color': map.get($deps, 'md-sys-color', 'outline'),
    'period-selector-outline-width': if($exclude-hardcoded-values, null, 1px),
    'period-selector-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'period-selector-selected-container-color':
      map.get($deps, 'md-sys-color', 'tertiary-container'),
    'period-selector-selected-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-tertiary-container'),
    'period-selector-selected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-tertiary-container'),
    'period-selector-selected-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-tertiary-container'),
    'period-selector-selected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-tertiary-container'),
    'period-selector-selected-label-text-color':
      map.get($deps, 'md-sys-color', 'on-tertiary-container'),
    'period-selector-selected-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-tertiary-container'),
    'period-selector-selected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-tertiary-container'),
    'period-selector-unselected-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'period-selector-unselected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'period-selector-unselected-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'period-selector-unselected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'period-selector-unselected-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'period-selector-unselected-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'period-selector-unselected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'period-selector-vertical-container-height':
      if($exclude-hardcoded-values, null, 80px),
    'period-selector-vertical-container-width':
      if($exclude-hardcoded-values, null, 52px),
    'surface-tint-layer-color': map.get($deps, 'md-sys-color', 'surface-tint'),
    'time-selector-24h-vertical-container-width':
      if($exclude-hardcoded-values, null, 114px),
    'time-selector-container-height': if($exclude-hardcoded-values, null, 80px),
    'time-selector-container-shape':
      map.get($deps, 'md-sys-shape', 'corner-small'),
    'time-selector-container-width': if($exclude-hardcoded-values, null, 96px),
    'time-selector-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'time-selector-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'time-selector-label-text-font':
      map.get($deps, 'md-sys-typescale', 'display-large-font'),
    'time-selector-label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'display-large-line-height'),
    'time-selector-label-text-size':
      map.get($deps, 'md-sys-typescale', 'display-large-size'),
    'time-selector-label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'display-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.time-picker.time-selector.label-text.tracking cannot be represented in
    // the "font" property shorthand. Consider using the discrete properties instead.
    'time-selector-label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'display-large-weight')
          map.get($deps, 'md-sys-typescale', 'display-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'display-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'display-large-font')
      ),
    'time-selector-label-text-weight':
      map.get($deps, 'md-sys-typescale', 'display-large-weight'),
    'time-selector-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'time-selector-selected-container-color':
      map.get($deps, 'md-sys-color', 'primary-container'),
    'time-selector-selected-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'time-selector-selected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'time-selector-selected-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'time-selector-selected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'time-selector-selected-label-text-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'time-selector-selected-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'time-selector-selected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-primary-container'),
    'time-selector-separator-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'time-selector-separator-font':
      map.get($deps, 'md-sys-typescale', 'display-large-font'),
    'time-selector-separator-line-height':
      map.get($deps, 'md-sys-typescale', 'display-large-line-height'),
    'time-selector-separator-size':
      map.get($deps, 'md-sys-typescale', 'display-large-size'),
    'time-selector-separator-tracking':
      map.get($deps, 'md-sys-typescale', 'display-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.time-picker.time-selector.separator.tracking cannot be represented in the
    // "font" property shorthand. Consider using the discrete properties instead.
    'time-selector-separator-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'display-large-weight')
          map.get($deps, 'md-sys-typescale', 'display-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'display-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'display-large-font')
      ),
    'time-selector-separator-weight':
      map.get($deps, 'md-sys-typescale', 'display-large-weight'),
    'time-selector-unselected-container-color':
      map.get($deps, 'md-sys-color', 'surface-variant'),
    'time-selector-unselected-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'time-selector-unselected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'time-selector-unselected-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'time-selector-unselected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'time-selector-unselected-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'time-selector-unselected-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'time-selector-unselected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface')
  );
}
