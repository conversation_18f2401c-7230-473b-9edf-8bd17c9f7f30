@use 'sass:map';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, icon);

/// Generates custom tokens for the mat-icon.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-icon
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: ((
    color: token-definition.hardcode(inherit, $exclude-hardcoded),
  ), (
    // Color variants:
    surface: (), // Default, no overrides needed.
    primary: (
      color: map.get($systems, md-sys-color, primary),
    ),
    secondary: (
      color: map.get($systems, md-sys-color, secondary),
    ),
    tertiary: (
      color: map.get($systems, md-sys-color, tertiary),
    ),
    error: (
      color: map.get($systems, md-sys-color, error),
    )
  ));

  @return token-definition.namespace-tokens($prefix, $tokens, $token-slots);
}
