import { Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./layout/layout.component').then(m => m.LayoutComponent),
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        redirectTo: '/dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./components/promotion-dashboard/promotion-dashboard.component').then(m => m.PromotionDashboardComponent)
      },
      {
        path: 'promotion-request',
        loadComponent: () => import('./components/promotion-request/promotion-request.component').then(m => m.PromotionRequestComponent)
      },
      {
        path: 'eligibility-seniority',
        loadComponent: () => import('./components/eligibility-seniority/eligibility-seniority.component').then(m => m.EligibilitySeniorityComponent)
      },
      {
        path: 'employee-list',
        loadComponent: () => import('./components/employee-list/employee-list.component').then(m => m.EmployeeListComponent)
      },
      {
        path: 'suitability-report',
        loadComponent: () => import('./components/suitability-report/suitability-report.component').then(m => m.SuitabilityReportComponent)
      },
      {
        path: 'suitability-report/:id',
        loadComponent: () => import('./components/suitability-report/suitability-report.component').then(m => m.SuitabilityReportComponent)
      },
      {
        path: 'disciplinary-status',
        loadComponent: () => import('./components/disciplinary-status/disciplinary-status.component').then(m => m.DisciplinaryStatusComponent)
      },
      {
        path: 'panel-approval',
        loadComponent: () => import('./components/panel-approval/panel-approval.component').then(m => m.PanelApprovalComponent)
      },
      {
        path: 'promotion-results',
        loadComponent: () => import('./components/promotion-results/promotion-results.component').then(m => m.PromotionResultsComponent)
      },
      {
        path: 'promotion-workflow',
        loadComponent: () => import('./components/promotion-workflow/promotion-workflow.component').then(m => m.PromotionWorkflowComponent)
      }
    ]
  },
  {
    path: '**',
    redirectTo: '/dashboard'
  }
];
