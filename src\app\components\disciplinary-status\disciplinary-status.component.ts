import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { DisciplinaryRecord, DisciplinaryStatus } from '../../models/promotion.model';

@Component({
  selector: 'app-disciplinary-status',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatChipsModule,
    MatIconModule,
    MatTableModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule
  ],
  templateUrl: './disciplinary-status.component.html',
  styleUrls: ['./disciplinary-status.component.scss']

})
export class DisciplinaryStatusComponent implements OnInit {
  disciplinaryForm: FormGroup;
  searchEmployeeId = '';
  selectedEmployee: any = null;
  recordColumns: string[] = ['punishmentType', 'punishmentDate', 'description', 'currencyInForce', 'orderNumber'];

  disciplinaryRecords: DisciplinaryRecord[] = [];

  // Sample employee data
  employees = [
    { employeeId: 'EMP001', employeeName: 'Arun Kumar' },
    { employeeId: 'EMP002', employeeName: 'Priya Sharma' },
    { employeeId: 'EMP003', employeeName: 'Rajesh Patel' },
    { employeeId: 'EMP004', employeeName: 'Meera Krishnan' }
  ];

  constructor(private fb: FormBuilder) {
    this.disciplinaryForm = this.fb.group({
      employeeId: ['', Validators.required],
      employeeName: ['', Validators.required],
      pastPunishments: [false],
      punishmentType: [''],
      punishmentDate: [null],
      currencyInForce: [false],
      passedOverEarlier: [false],
      remarks: ['']
    });
  }

  ngOnInit(): void {}

  searchEmployee(): void {
    const employee = this.employees.find(emp => emp.employeeId === this.searchEmployeeId);
    if (employee) {
      this.selectedEmployee = employee;
      this.disciplinaryForm.patchValue({
        employeeId: employee.employeeId,
        employeeName: employee.employeeName
      });
      this.loadDisciplinaryRecords(employee.employeeId);
    } else {
      alert('Employee not found');
    }
  }

  loadDisciplinaryRecords(employeeId: string): void {
    // Sample disciplinary records
    if (employeeId === 'EMP003') {
      this.disciplinaryRecords = [
        {
          id: 1,
          employeeId: 'EMP003',
          employeeName: 'Rajesh Patel',
          punishmentType: 'censure',
          punishmentDate: new Date('2023-05-15'),
          description: 'Late attendance for consecutive days',
          currencyInForce: true,
          orderNumber: 'ORD/2023/156'
        }
      ];
    } else {
      this.disciplinaryRecords = [];
    }
  }

  onPunishmentChange(event: any): void {
    const hasPunishments = event.value;
    if (!hasPunishments) {
      this.disciplinaryForm.patchValue({
        punishmentType: '',
        punishmentDate: null,
        currencyInForce: false
      });
    }
  }

  getEligibilityStatus(): string {
    const pastPunishments = this.disciplinaryForm.get('pastPunishments')?.value;
    const currencyInForce = this.disciplinaryForm.get('currencyInForce')?.value;
    const passedOverEarlier = this.disciplinaryForm.get('passedOverEarlier')?.value;

    if (!pastPunishments && !passedOverEarlier) {
      return 'Eligible';
    } else if (pastPunishments && currencyInForce) {
      return 'Not Eligible';
    } else if (passedOverEarlier) {
      return 'Under Review';
    } else {
      return 'Eligible';
    }
  }

  getEligibilityClass(): string {
    const status = this.getEligibilityStatus();
    switch (status) {
      case 'Eligible': return 'eligible';
      case 'Not Eligible': return 'not-eligible';
      default: return 'status-warning';
    }
  }

  getPunishmentSeverityClass(type: string): string {
    const majorPunishments = ['reduction', 'compulsory-retirement', 'dismissal'];
    const minorPunishments = ['censure', 'increment-stop'];

    if (majorPunishments.includes(type)) return 'punishment-severe';
    if (minorPunishments.includes(type)) return 'punishment-minor';
    return 'punishment-major';
  }

  formatPunishmentType(type: string): string {
    const typeMap: { [key: string]: string } = {
      'censure': 'Censure',
      'increment-stop': 'Withholding of Increment',
      'recovery': 'Recovery from Pay',
      'reduction': 'Reduction in Rank',
      'compulsory-retirement': 'Compulsory Retirement',
      'dismissal': 'Dismissal'
    };
    return typeMap[type] || type;
  }

  checkAllRecords(): void {
    console.log('Checking all disciplinary records');
  }

  onSubmit(): void {
    if (this.disciplinaryForm.valid) {
      console.log('Disciplinary Status Updated:', this.disciplinaryForm.value);
    }
  }

  onReset(): void {
    this.disciplinaryForm.reset();
    this.selectedEmployee = null;
    this.disciplinaryRecords = [];
    this.searchEmployeeId = '';
  }
}
