import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatMenuModule } from '@angular/material/menu';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { EmployeeListItem } from '../../models/promotion.model';

@Component({
  selector: 'app-employee-list',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatMenuModule,
    MatCardModule,
    MatSelectModule,
    MatDividerModule,
    RouterModule,
    FormsModule
  ],
  templateUrl: './employee-list.component.html',
  styleUrls: ['./employee-list.component.scss']
})
export class EmployeeListComponent implements OnInit, AfterViewInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  displayedColumns: string[] = ['employee', 'designation', 'department', 'joiningDate', 'workingDays', 'eligibilityStatus', 'actions'];
  dataSource = new MatTableDataSource<EmployeeListItem>();

  selectedDepartment = '';
  selectedStatus = '';

  employees: EmployeeListItem[] = [
    {
      id: 1,
      employeeName: 'Saravanan M',
      employeeId: 'EMP001',
      designation: 'Software Engineer',
      department: 'Engineering',
      joiningDate: new Date('2023-01-15'),
      workingDays: 365,
      dateOfBirth: new Date('1995-06-10'),
      status: 'Active',
      eligibilityStatus: 'Eligible'
    },
    {
      id: 2,
      employeeName: 'Santoshi K',
      employeeId: 'EMP002',
      designation: 'HR Executive',
      department: 'HR',
      joiningDate: new Date('2023-03-20'),
      workingDays: 290,
      dateOfBirth: new Date('1992-08-25'),
      status: 'Active',
      eligibilityStatus: 'Under Review'
    },
    // Add more sample data...
  ];

  ngOnInit(): void {
    this.dataSource.data = this.employees;
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  filterByDepartment(): void {
    this.applyFilters();
  }

  filterByStatus(): void {
    this.applyFilters();
  }

  private applyFilters(): void {
    this.dataSource.filterPredicate = (data: EmployeeListItem, filter: string) => {
      const matchesDepartment = !this.selectedDepartment || data.department === this.selectedDepartment;
      const matchesStatus = !this.selectedStatus || data.eligibilityStatus === this.selectedStatus;
      const matchesSearch = !filter ||
        data.employeeName.toLowerCase().includes(filter) ||
        data.employeeId.toLowerCase().includes(filter) ||
        data.designation.toLowerCase().includes(filter);

      return matchesDepartment && matchesStatus && matchesSearch;
    };

    this.dataSource.filter = Math.random().toString(); // Trigger filter
  }

  clearFilters(): void {
    this.selectedDepartment = '';
    this.selectedStatus = '';
    this.dataSource.filter = '';
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase().replace(' ', '-')) {
      case 'eligible': return 'status-eligible';
      case 'not-eligible': return 'status-not-eligible';
      case 'under-review': return 'status-under-review';
      default: return '';
    }
  }

  selectEmployee(employee: EmployeeListItem): void {
    console.log('Selected employee:', employee);
  }

  editEmployee(employee: EmployeeListItem): void {
    console.log('Edit employee:', employee);
  }

  viewDetails(employee: EmployeeListItem): void {
    console.log('View details:', employee);
  }

  initiatePromotion(employee: EmployeeListItem): void {
    console.log('Initiate promotion for:', employee);
  }
}
