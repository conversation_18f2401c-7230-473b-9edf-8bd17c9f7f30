import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { PanelCandidate, PanelDetails } from '../../models/promotion.model';

@Component({
  selector: 'app-panel-approval',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatChipsModule,
    MatIconModule,
    MatTableModule,
    MatTabsModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule
  ],
  templateUrl: './panel-approval.component.html',
  styleUrls: ['./panel-approval.component.scss']
})
export class PanelApprovalComponent implements OnInit {
  approvalForm: FormGroup;
  selectedCandidateId = '';
  selectedCandidate: PanelCandidate | null = null;
  uploadedFile: File | null = null;
  bulkColumns: string[] = ['employee', 'designation', 'department', 'score', 'recommendation', 'actions'];

  currentPanel: PanelDetails = {
    panelId: 'PNL-2024-001',
    panelType: 'Promotion Panel',
    chairperson: 'Dr. Rajesh Kumar',
    members: ['Dr. Rajesh Kumar', 'Ms. Priya Singh', 'Mr. Amit Sharma'],
    meetingDate: new Date('2024-03-15'),
    status: 'In Progress',
    venue: 'Conference Room A'
  };

  panelCandidates: PanelCandidate[] = [
    {
      employeeId: 'EMP001',
      employeeName: 'Amit Kumar',
      currentDesignation: 'Assistant',
      proposedDesignation: 'Superintendent',
      department: 'Operations',
      suitabilityScore: 85,
      panelRecommendation: 'Pending',
      remarks: ''
    },
    {
      employeeId: 'EMP002',
      employeeName: 'Priya Sharma',
      currentDesignation: 'Junior Assistant',
      proposedDesignation: 'Assistant',
      department: 'Finance',
      suitabilityScore: 92,
      panelRecommendation: 'Pending',
      remarks: ''
    }
  ];

  constructor(private fb: FormBuilder) {
    this.approvalForm = this.fb.group({
      panelRecommendation: ['', Validators.required],
      effectiveDate: ['', Validators.required],
      remarks: ['', Validators.required],
      orderNumber: ['', Validators.required]
    });
  }

  ngOnInit(): void {}

  onCandidateSelect(event: any): void {
    const candidateId = event.value;
    this.selectedCandidate = this.panelCandidates.find(c => c.employeeId === candidateId) || null;
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.uploadedFile = file;
    }
  }

  removeFile(): void {
    this.uploadedFile = null;
  }

  getPanelStatusClass(status: string): string {
    switch (status) {
      case 'Scheduled': return 'status-scheduled';
      case 'In Progress': return 'status-in-progress';
      case 'Completed': return 'status-completed';
      default: return 'status-scheduled';
    }
  }

  getScoreClass(score: number): string {
    if (score >= 90) return 'score-excellent';
    if (score >= 75) return 'score-good';
    if (score >= 60) return 'score-average';
    return 'score-poor';
  }

  getRecommendationClass(recommendation: string): string {
    switch (recommendation) {
      case 'Recommended': return 'recommendation-recommended';
      case 'Not Recommended': return 'recommendation-not-recommended';
      case 'Pending': return 'recommendation-pending';
      default: return 'recommendation-pending';
    }
  }

  viewPanelDetails(): void {
    console.log('View panel details');
  }

  onSubmitApproval(): void {
    if (this.approvalForm.valid && this.selectedCandidate) {
      const approvalData = {
        ...this.approvalForm.value,
        employeeId: this.selectedCandidate.employeeId,
        uploadedDocument: this.uploadedFile
      };
      console.log('Panel approval submitted:', approvalData);
    }
  }

  onReset(): void {
    this.approvalForm.reset();
    this.selectedCandidate = null;
    this.selectedCandidateId = '';
    this.uploadedFile = null;
  }

  editCandidate(candidate: PanelCandidate): void {
    console.log('Edit candidate:', candidate);
  }

  viewDetails(candidate: PanelCandidate): void {
    console.log('View candidate details:', candidate);
  }

  approveAll(): void {
    console.log('Approve all recommended candidates');
  }

  exportResults(): void {
    console.log('Export panel results');
  }
}
