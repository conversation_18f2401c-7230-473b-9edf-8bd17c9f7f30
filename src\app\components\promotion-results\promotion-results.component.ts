import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTableDataSource } from '@angular/material/table';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { PromotionResultItem, PromotionDashboardStats } from '../../models/promotion.model';

@Component({
  selector: 'app-promotion-results',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatChipsModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    RouterModule
  ],
  templateUrl: './promotion-results.component.html',
  styleUrls: ['./promotion-results.component.scss']
})
export class PromotionResultsComponent implements OnInit, AfterViewInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  dataSource = new MatTableDataSource<PromotionResultItem>();
  displayedColumns: string[] = ['employee', 'designation', 'department', 'status', 'effectiveDate', 'orderNumber', 'actions'];

  selectedStatus = '';
  selectedDepartment = '';
  fromDate: Date | null = null;
  toDate: Date | null = null;

  dashboardStats: PromotionDashboardStats = {
    totalPromotions: 156,
    approvedPromotions: 142,
    rejectedPromotions: 8,
    pendingPromotions: 4,
    onHoldPromotions: 2
  };

  promotionResults: PromotionResultItem[] = [
    {
      employeeId: 'EMP001',
      employeeName: 'Amit Kumar',
      fromDesignation: 'Assistant',
      toDesignation: 'Superintendent',
      department: 'Operations',
      status: 'Approved',
      effectiveDate: new Date('2024-04-01'),
      orderNumber: 'ORD-2024-001',
      panelDate: new Date('2024-03-15'),
      approvedBy: 'Panel Chairman',
      remarks: 'Promotion approved based on excellent performance'
    },
    {
      employeeId: 'EMP002',
      employeeName: 'Priya Sharma',
      fromDesignation: 'Junior Assistant',
      toDesignation: 'Assistant',
      department: 'Finance',
      status: 'Approved',
      effectiveDate: new Date('2024-04-01'),
      orderNumber: 'ORD-2024-002',
      panelDate: new Date('2024-03-15'),
      approvedBy: 'Panel Chairman',
      remarks: 'Promotion approved with commendation'
    },
    {
      employeeId: 'EMP003',
      employeeName: 'Rajesh Patel',
      fromDesignation: 'Clerk',
      toDesignation: 'Senior Clerk',
      department: 'Administration',
      status: 'On Hold',
      effectiveDate: new Date('2024-05-01'),
      orderNumber: '',
      panelDate: new Date('2024-03-20'),
      approvedBy: 'Panel Chairman',
      remarks: 'Pending documentation verification'
    }
  ];

  constructor() {}

  ngOnInit(): void {
    this.dataSource.data = this.promotionResults;
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  applyFilters(): void {
    let filteredData = this.promotionResults;

    if (this.selectedStatus) {
      filteredData = filteredData.filter(item => item.status === this.selectedStatus);
    }

    if (this.selectedDepartment) {
      filteredData = filteredData.filter(item => item.department === this.selectedDepartment);
    }

    if (this.fromDate) {
      filteredData = filteredData.filter(item => item.effectiveDate >= this.fromDate!);
    }

    if (this.toDate) {
      filteredData = filteredData.filter(item => item.effectiveDate <= this.toDate!);
    }

    this.dataSource.data = filteredData;
  }

  clearFilters(): void {
    this.selectedStatus = '';
    this.selectedDepartment = '';
    this.fromDate = null;
    this.toDate = null;
    this.dataSource.data = this.promotionResults;
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Approved': return 'status-approved';
      case 'Rejected': return 'status-rejected';
      case 'Pending': return 'status-pending';
      case 'On Hold': return 'status-on-hold';
      default: return 'status-pending';
    }
  }

  viewDetails(result: PromotionResultItem): void {
    console.log('View details for:', result);
  }

  editResult(result: PromotionResultItem): void {
    console.log('Edit result for:', result);
  }

  generateReport(): void {
    console.log('Generate promotion results report');
  }

  exportResults(): void {
    console.log('Export promotion results');
  }
}
