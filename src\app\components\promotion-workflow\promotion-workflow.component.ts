import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatStepperModule } from '@angular/material/stepper';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { RouterModule } from '@angular/router';
import { WorkflowStep, PromotionCase } from '../../models/promotion.model';

@Component({
  selector: 'app-promotion-workflow',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatStepperModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressBarModule,
    MatTabsModule,
    MatListModule,
    MatDividerModule,
    RouterModule
  ],
  templateUrl: './promotion-workflow.component.html',
  styleUrls: ['./promotion-workflow.component.scss']
})
export class PromotionWorkflowComponent implements OnInit {
  Math = Math; // Make Math available in template

  workflowSteps: WorkflowStep[] = [
    {
      id: 1,
      title: 'Application Submission',
      description: 'Employee submits promotion application with required documents',
      status: 'completed',
      assignedTo: 'Employee',
      estimatedDays: 1,
      completedDate: new Date('2024-01-15')
    },
    {
      id: 2,
      title: 'Initial Review',
      description: 'HR department reviews application for completeness',
      status: 'completed',
      assignedTo: 'HR Department',
      estimatedDays: 3,
      completedDate: new Date('2024-01-18')
    },
    {
      id: 3,
      title: 'Eligibility Verification',
      description: 'Verify employee meets promotion criteria and seniority requirements',
      status: 'current',
      assignedTo: 'HR Officer',
      estimatedDays: 5,
      completedDate: null
    },
    {
      id: 4,
      title: 'Suitability Assessment',
      description: 'Evaluate employee performance and suitability for promotion',
      status: 'pending',
      assignedTo: 'Reporting Manager',
      estimatedDays: 7,
      completedDate: null
    },
    {
      id: 5,
      title: 'Panel Review',
      description: 'Promotion panel reviews and makes recommendation',
      status: 'pending',
      assignedTo: 'Promotion Panel',
      estimatedDays: 10,
      completedDate: null
    }
  ];

  activeCases: PromotionCase[] = [
    {
      id: 'CASE-001',
      employeeId: 'EMP001',
      employeeName: 'Amit Kumar',
      type: 'seasonal-to-regular',
      priority: 'high',
      currentStep: 3,
      totalSteps: 10,
      startDate: new Date('2024-01-15'),
      expectedCompletion: new Date('2024-03-15'),
      status: 'in-progress'
    },
    {
      id: 'CASE-002',
      employeeId: 'EMP002',
      employeeName: 'Priya Sharma',
      type: 'cadre-wise',
      priority: 'medium',
      currentStep: 5,
      totalSteps: 12,
      startDate: new Date('2024-01-10'),
      expectedCompletion: new Date('2024-03-25'),
      status: 'in-progress'
    },
    {
      id: 'CASE-003',
      employeeId: 'EMP003',
      employeeName: 'Rajesh Patel',
      type: 'seasonal-to-regular',
      priority: 'low',
      currentStep: 2,
      totalSteps: 10,
      startDate: new Date('2024-01-20'),
      expectedCompletion: new Date('2024-03-20'),
      status: 'in-progress'
    }
  ];

  constructor() {}

  ngOnInit(): void {}

  getStepState(status: string): string {
    switch (status) {
      case 'completed': return 'done';
      case 'current': return 'edit';
      case 'pending': return 'number';
      default: return 'number';
    }
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  }

  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'high': return 'priority-high';
      case 'medium': return 'priority-medium';
      case 'low': return 'priority-low';
      default: return 'priority-medium';
    }
  }

  getTypeLabel(type: string): string {
    switch (type) {
      case 'seasonal-to-regular': return 'Seasonal to Regular';
      case 'cadre-wise': return 'Cadre-wise Promotion';
      case 'special': return 'Special Promotion';
      default: return 'Standard Promotion';
    }
  }

  viewCaseDetails(caseItem: PromotionCase): void {
    console.log('View case details:', caseItem);
  }

  updateCaseStatus(caseItem: PromotionCase): void {
    console.log('Update case status:', caseItem);
  }

  startStep(step: WorkflowStep): void {
    console.log('Start step:', step);
  }

  viewStepDetails(step: WorkflowStep): void {
    console.log('View step details:', step);
  }

  useTemplate(templateType: string): void {
    console.log('Use template:', templateType);
  }

  createNewCase(): void {
    console.log('Create new promotion case');
  }
}
